// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package logx

import (
	"bytes"

	"github.com/go-kit/log"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/utils/logger"
)

type Logger struct {
	prefix string
	w      log.Logger
}

func New(prefix string) *Logger {
	l := &Logger{
		w: log.NewLogfmtLogger(writer{
			prefix: prefix,
		}),
	}
	return l
}

func (l *Logger) Log(keyvals ...interface{}) error {
	return l.w.Log(keyvals...)
}

type writer struct {
	prefix string
}

func (w writer) Write(b []byte) (int, error) {
	s := string(bytes.TrimSpace(b))
	logger.Infof("%s\t%s", w.prefix, s)
	return len(s), nil
}
