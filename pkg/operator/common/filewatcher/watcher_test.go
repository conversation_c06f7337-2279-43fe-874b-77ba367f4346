// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package filewatcher

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestWatcher(t *testing.T) {
	dir, err := os.MkdirTemp("", "watchertest")
	assert.NoError(t, err)

	f1 := filepath.Join(dir, "f1")
	assert.NoError(t, os.WriteFile(f1, []byte("1000"), 0o666))

	watcher := New()
	ch1, err := watcher.AddPath(f1)
	assert.NoError(t, err)

	stop := make(chan struct{}, 1)

	go func() {
		defer close(stop)
		select {
		case <-ch1:
			break
		}
	}()

	time.Sleep(time.Second)
	assert.NoError(t, os.WriteFile(f1, []byte("1002"), 0o666))

	<-stop

	assert.NoError(t, watcher.RemovePath(f1))
	watcher.Stop()
}
