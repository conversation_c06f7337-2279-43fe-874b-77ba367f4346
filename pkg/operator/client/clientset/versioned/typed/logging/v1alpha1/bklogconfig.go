// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/TencentBlueKing/bkmonitor-datalink/pkg/operator/apis/logging/v1alpha1"
	scheme "github.com/TencentBlueKing/bkmonitor-datalink/pkg/operator/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// BkLogConfigsGetter has a method to return a BkLogConfigInterface.
// A group's client should implement this interface.
type BkLogConfigsGetter interface {
	BkLogConfigs(namespace string) BkLogConfigInterface
}

// BkLogConfigInterface has methods to work with BkLogConfig resources.
type BkLogConfigInterface interface {
	Create(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.CreateOptions) (*v1alpha1.BkLogConfig, error)
	Update(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.UpdateOptions) (*v1alpha1.BkLogConfig, error)
	UpdateStatus(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.UpdateOptions) (*v1alpha1.BkLogConfig, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.BkLogConfig, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.BkLogConfigList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.BkLogConfig, err error)
	BkLogConfigExpansion
}

// bkLogConfigs implements BkLogConfigInterface
type bkLogConfigs struct {
	client rest.Interface
	ns     string
}

// newBkLogConfigs returns a BkLogConfigs
func newBkLogConfigs(c *BkV1alpha1Client, namespace string) *bkLogConfigs {
	return &bkLogConfigs{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the bkLogConfig, and returns the corresponding bkLogConfig object, and an error if there is any.
func (c *bkLogConfigs) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.BkLogConfig, err error) {
	result = &v1alpha1.BkLogConfig{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("bklogconfigs").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of BkLogConfigs that match those selectors.
func (c *bkLogConfigs) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.BkLogConfigList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.BkLogConfigList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("bklogconfigs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested bkLogConfigs.
func (c *bkLogConfigs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("bklogconfigs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a bkLogConfig and creates it.  Returns the server's representation of the bkLogConfig, and an error, if there is any.
func (c *bkLogConfigs) Create(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.CreateOptions) (result *v1alpha1.BkLogConfig, err error) {
	result = &v1alpha1.BkLogConfig{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("bklogconfigs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bkLogConfig).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a bkLogConfig and updates it. Returns the server's representation of the bkLogConfig, and an error, if there is any.
func (c *bkLogConfigs) Update(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.UpdateOptions) (result *v1alpha1.BkLogConfig, err error) {
	result = &v1alpha1.BkLogConfig{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("bklogconfigs").
		Name(bkLogConfig.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bkLogConfig).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *bkLogConfigs) UpdateStatus(ctx context.Context, bkLogConfig *v1alpha1.BkLogConfig, opts v1.UpdateOptions) (result *v1alpha1.BkLogConfig, err error) {
	result = &v1alpha1.BkLogConfig{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("bklogconfigs").
		Name(bkLogConfig.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bkLogConfig).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the bkLogConfig and deletes it. Returns an error if one occurs.
func (c *bkLogConfigs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("bklogconfigs").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *bkLogConfigs) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("bklogconfigs").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched bkLogConfig.
func (c *bkLogConfigs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.BkLogConfig, err error) {
	result = &v1alpha1.BkLogConfig{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("bklogconfigs").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
