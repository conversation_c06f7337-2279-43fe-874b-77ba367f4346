// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by lister-gen. DO NOT EDIT.

package v1beta1

import (
	v1beta1 "github.com/TencentBlueKing/bkmonitor-datalink/pkg/operator/apis/monitoring/v1beta1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// DataIDLister helps list DataIDs.
// All objects returned here must be treated as read-only.
type DataIDLister interface {
	// List lists all DataIDs in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.DataID, err error)
	// DataIDs returns an object that can list and get DataIDs.
	DataIDs(namespace string) DataIDNamespaceLister
	DataIDListerExpansion
}

// dataIDLister implements the DataIDLister interface.
type dataIDLister struct {
	indexer cache.Indexer
}

// NewDataIDLister returns a new DataIDLister.
func NewDataIDLister(indexer cache.Indexer) DataIDLister {
	return &dataIDLister{indexer: indexer}
}

// List lists all DataIDs in the indexer.
func (s *dataIDLister) List(selector labels.Selector) (ret []*v1beta1.DataID, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.DataID))
	})
	return ret, err
}

// DataIDs returns an object that can list and get DataIDs.
func (s *dataIDLister) DataIDs(namespace string) DataIDNamespaceLister {
	return dataIDNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// DataIDNamespaceLister helps list and get DataIDs.
// All objects returned here must be treated as read-only.
type DataIDNamespaceLister interface {
	// List lists all DataIDs in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1beta1.DataID, err error)
	// Get retrieves the DataID from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1beta1.DataID, error)
	DataIDNamespaceListerExpansion
}

// dataIDNamespaceLister implements the DataIDNamespaceLister
// interface.
type dataIDNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all DataIDs in the indexer for a given namespace.
func (s dataIDNamespaceLister) List(selector labels.Selector) (ret []*v1beta1.DataID, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1beta1.DataID))
	})
	return ret, err
}

// Get retrieves the DataID from the indexer for a given namespace and name.
func (s dataIDNamespaceLister) Get(name string) (*v1beta1.DataID, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1beta1.Resource("dataid"), name)
	}
	return obj.(*v1beta1.DataID), nil
}
