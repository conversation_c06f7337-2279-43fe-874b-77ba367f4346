// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "github.com/TencentBlueKing/bkmonitor-datalink/pkg/operator/apis/logging/v1alpha1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// BkLogConfigLister helps list BkLogConfigs.
// All objects returned here must be treated as read-only.
type BkLogConfigLister interface {
	// List lists all BkLogConfigs in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.BkLogConfig, err error)
	// BkLogConfigs returns an object that can list and get BkLogConfigs.
	BkLogConfigs(namespace string) BkLogConfigNamespaceLister
	BkLogConfigListerExpansion
}

// bkLogConfigLister implements the BkLogConfigLister interface.
type bkLogConfigLister struct {
	indexer cache.Indexer
}

// NewBkLogConfigLister returns a new BkLogConfigLister.
func NewBkLogConfigLister(indexer cache.Indexer) BkLogConfigLister {
	return &bkLogConfigLister{indexer: indexer}
}

// List lists all BkLogConfigs in the indexer.
func (s *bkLogConfigLister) List(selector labels.Selector) (ret []*v1alpha1.BkLogConfig, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.BkLogConfig))
	})
	return ret, err
}

// BkLogConfigs returns an object that can list and get BkLogConfigs.
func (s *bkLogConfigLister) BkLogConfigs(namespace string) BkLogConfigNamespaceLister {
	return bkLogConfigNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// BkLogConfigNamespaceLister helps list and get BkLogConfigs.
// All objects returned here must be treated as read-only.
type BkLogConfigNamespaceLister interface {
	// List lists all BkLogConfigs in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.BkLogConfig, err error)
	// Get retrieves the BkLogConfig from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha1.BkLogConfig, error)
	BkLogConfigNamespaceListerExpansion
}

// bkLogConfigNamespaceLister implements the BkLogConfigNamespaceLister
// interface.
type bkLogConfigNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all BkLogConfigs in the indexer for a given namespace.
func (s bkLogConfigNamespaceLister) List(selector labels.Selector) (ret []*v1alpha1.BkLogConfig, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.BkLogConfig))
	})
	return ret, err
}

// Get retrieves the BkLogConfig from the indexer for a given namespace and name.
func (s bkLogConfigNamespaceLister) Get(name string) (*v1alpha1.BkLogConfig, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha1.Resource("bklogconfig"), name)
	}
	return obj.(*v1alpha1.BkLogConfig), nil
}
