# libgse 配置说明

## 配置样例

```yaml
# ================================ Outputs =====================================
output.bkpipe:
  synccfg: true
  endpoint: '/var/run/ipc.state.report'
  # 地址分配方式，static：静态 dynamic：动态
  bk_addressing: static
  hostip: 0.0.0.0
  cloudid: 0
  hostid: 3

path.logs: '/var/log/gse'
path.data: '/var/lib/gse'
path.pid: '/var/run/gse'
seccomp.enabled: false

# ================================ Logging ======================================
# Available log levels are: critical, error, warning, info, debug
logging.level: debug
logging.path: '/var/log/gse'
logging.maxsize: 200
logging.maxage: 10
logging.backups: 5

# ============================= Resource ==================================
resource_limit:
  enabled: true
  cpu: 1    # CPU 资源限制 单位 core(float64)
  mem: -1 # 内存资源限制 单位 MB(int)，-1 代表无限制
```
注：各个模块如需引用 libgse, 则需添加以上配置信息

## 引用举例

### 以 bkmonitorbeat 采集模块引用 libgse 为例

bkmonitorbeat 是蓝鲸监控的底层采集器组件， 需要将采集到的数据上报给 gse agent， 则其本身需要充当 gse client 的角色，利用 libgse 提供的接口可以实现这一目标。

#### 引用步骤

1. 加载配置

应用模块引入 libgse/beat 包：

```golang 
import (
    ...	
	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/libgse/beat"
    ...
)
var (
	BeatName         = "bkmonitorbeat"
)
...
func main() {
...
	settings := instance.Settings{Processing: processing.MakeDefaultSupport(!*disableNormalize)}
	pubConfig := beat.PublishConfig{PublishMode: libbeat.PublishMode(beat.GuaranteedSend)}

	config, err := beat.InitWithPublishConfig(BeatName, Version, pubConfig, settings)
...

	// 日志配置
	logCfgContent, err := beat.GetRawConfig().Child("logging", -1)
	if err != nil {
		fmt.Printf("failed to parse logging config: %v\n", err)
		os.Exit(1)
	}
	type LogConfig struct {
		Stdout  bool   `config:"stdout"`
		Level   string `config:"level"`
		Path    string `config:"path"`
		MaxSize int    `config:"maxsize"`
		MaxAge  int    `config:"maxage"`
		Backups int    `config:"backups"`
	}
	var logCfg LogConfig
	if err := logCfgContent.Unpack(&logCfg); err != nil {
		fmt.Printf("failed to unpack logging config: %v\n", err)
		os.Exit(1)
	}

	logger.SetOptions(logger.Options{
		Stdout:     logCfg.Stdout,
		Filename:   filepath.Join(logCfg.Path, "bkmonitorbeat.log"),
		MaxSize:    logCfg.MaxSize,
		MaxAge:     logCfg.MaxAge,
		MaxBackups: logCfg.Backups,
		Level:      logCfg.Level,
	})

	bt, err := beater.New(config, BeatName, Version)
	if err != nil {
		fmt.Printf("New failed with error: %v\n", err)
		os.Exit(1)
	}
	...
}
```

通过指定模块名， 如上面 bkmonitorbeat， 通过启动命令： `./bkmonitorbeat -c bkmonitorbeat.conf`， 通过 -c 指定配置文件路径即可加载配置， 程序内部再解析即可。

2. 启动 gse client 

```golang
import (
...
	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/libgse/output/gse"
...
)
```

引入包 libgse/output/gse 会自动调用包中的 init 方法，继而调用 MakeGSE 方法， 创建一个 gse client：

```golang
func init() {
    outputs.RegisterType("gse", MakeGSE)
}

// MakeGSE create a gse client
func MakeGSE(im outputs.IndexManager, beat beat.Info, stats outputs.Observer, cfg *common.Config) (outputs.Group, error) {
    ...
    // create gse client
    cli, err := gse.NewGseClient(cfg)
    ...
}
```

3. 数据上报 gse agent 

数据上报 gse agent 是通过 libgse/beat 包提供的 Send 方法：

```golang
import (
    ...
    "github.com/TencentBlueKing/bkmonitor-datalink/pkg/libgse/beat"
    ...
)

func SendMsg(msg){
    ...
    beat.Send(msg)
    ...
}
```

注: Send 方法通过调用 beats/libbeat/beat 提供的接口 Client 中的 Publish 方法，该方法在  libgse/output/gse 包中被实现:

```golang
// Publish implement output interface
func (c *Output) Publish(batch publisher.Batch) error {
	events := batch.Events()
	for i := range events {
		if events[i].Content.Fields == nil {
			metricGsePublishDropped.Add(1)
			continue
		}
		metricGsePublishReceived.Add(1)
		err := c.PublishEvent(&events[i])
		if err != nil {
			logp.Err("publish event failed: %v", err)
			metricGsePublishFailed.Add(1)
		} else {
			metricGsePublishTotal.Add(1)
		}
	}

	batch.ACK()
	return nil
}
```
