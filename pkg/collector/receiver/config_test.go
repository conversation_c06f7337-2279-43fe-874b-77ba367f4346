// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package receiver

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/collector/confengine"
)

func TestLoadConfig(t *testing.T) {
	content := `
apm:
  patterns:
    - "../example/fixtures/subconfig.yml"
`
	config, err := confengine.LoadConfigContent(content)
	assert.NoError(t, err)

	subConfigs := LoadConfigFrom(config)

	subConfig, ok := subConfigs["token1"]
	assert.True(t, ok)
	assert.Equal(t, SkywalkingConfig{
		Sn: "my-test-sn",
		Rules: []SkywalkingRule{
			{
				Type:    "Http",
				Enabled: true,
				Target:  "cookie",
				Field:   "language",
			},
			{
				Type:    "Http",
				Enabled: true,
				Target:  "header",
				Field:   "Accept",
			},
			{
				Type:    "Http",
				Enabled: true,
				Target:  "query_parameter",
				Field:   "from",
			},
		},
	}, subConfig)
}
