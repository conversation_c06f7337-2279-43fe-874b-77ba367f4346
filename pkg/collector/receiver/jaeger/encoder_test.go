// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package jaeger

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestThriftV1Encoder(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		content, err := os.ReadFile("../../example/fixtures/jaeger.bytes")
		assert.NoError(t, err)

		encoder := newThriftV1Encoder()
		assert.Equal(t, "thrift", encoder.Type())

		_, err = encoder.UnmarshalTraces(content)
		assert.NoError(t, err)
	})

	t.Run("Failed", func(t *testing.T) {
		encoder := newThriftV1Encoder()
		_, err := encoder.UnmarshalTraces([]byte("{-}"))
		assert.Error(t, err)
	})
}
