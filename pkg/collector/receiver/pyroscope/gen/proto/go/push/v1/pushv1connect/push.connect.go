// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: push/v1/push.proto

package pushv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/TencentBlueKing/bkmonitor-datalink/pkg/collector/receiver/pyroscope/gen/proto/go/push/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PusherServiceName is the fully-qualified name of the PusherService service.
	PusherServiceName = "push.v1.PusherService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PusherServicePushProcedure is the fully-qualified name of the PusherService's Push RPC.
	PusherServicePushProcedure = "/push.v1.PusherService/Push"
)

// These variables are the protoreflect.Descriptor objects for the RPCs defined in this package.
var (
	pusherServiceServiceDescriptor    = v1.File_push_v1_push_proto.Services().ByName("PusherService")
	pusherServicePushMethodDescriptor = pusherServiceServiceDescriptor.Methods().ByName("Push")
)

// PusherServiceClient is a client for the push.v1.PusherService service.
type PusherServiceClient interface {
	Push(context.Context, *connect.Request[v1.PushRequest]) (*connect.Response[v1.PushResponse], error)
}

// NewPusherServiceClient constructs a client for the push.v1.PusherService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPusherServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PusherServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	return &pusherServiceClient{
		push: connect.NewClient[v1.PushRequest, v1.PushResponse](
			httpClient,
			baseURL+PusherServicePushProcedure,
			connect.WithSchema(pusherServicePushMethodDescriptor),
			connect.WithClientOptions(opts...),
		),
	}
}

// pusherServiceClient implements PusherServiceClient.
type pusherServiceClient struct {
	push *connect.Client[v1.PushRequest, v1.PushResponse]
}

// Push calls push.v1.PusherService.Push.
func (c *pusherServiceClient) Push(ctx context.Context, req *connect.Request[v1.PushRequest]) (*connect.Response[v1.PushResponse], error) {
	return c.push.CallUnary(ctx, req)
}

// PusherServiceHandler is an implementation of the push.v1.PusherService service.
type PusherServiceHandler interface {
	Push(context.Context, *connect.Request[v1.PushRequest]) (*connect.Response[v1.PushResponse], error)
}

// NewPusherServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPusherServiceHandler(svc PusherServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	pusherServicePushHandler := connect.NewUnaryHandler(
		PusherServicePushProcedure,
		svc.Push,
		connect.WithSchema(pusherServicePushMethodDescriptor),
		connect.WithHandlerOptions(opts...),
	)
	return "/push.v1.PusherService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PusherServicePushProcedure:
			pusherServicePushHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPusherServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPusherServiceHandler struct{}

func (UnimplementedPusherServiceHandler) Push(context.Context, *connect.Request[v1.PushRequest]) (*connect.Response[v1.PushResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("push.v1.PusherService.Push is not implemented"))
}
