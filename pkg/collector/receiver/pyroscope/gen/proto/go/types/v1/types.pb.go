// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        (unknown)
// source: types/v1/types.proto

package typesv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TimeSeriesAggregationType int32

const (
	TimeSeriesAggregationType_TIME_SERIES_AGGREGATION_TYPE_SUM     TimeSeriesAggregationType = 0
	TimeSeriesAggregationType_TIME_SERIES_AGGREGATION_TYPE_AVERAGE TimeSeriesAggregationType = 1
)

// Enum value maps for TimeSeriesAggregationType.
var (
	TimeSeriesAggregationType_name = map[int32]string{
		0: "TIME_SERIES_AGGREGATION_TYPE_SUM",
		1: "TIME_SERIES_AGGREGATION_TYPE_AVERAGE",
	}
	TimeSeriesAggregationType_value = map[string]int32{
		"TIME_SERIES_AGGREGATION_TYPE_SUM":     0,
		"TIME_SERIES_AGGREGATION_TYPE_AVERAGE": 1,
	}
)

func (x TimeSeriesAggregationType) Enum() *TimeSeriesAggregationType {
	p := new(TimeSeriesAggregationType)
	*p = x
	return p
}

func (x TimeSeriesAggregationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeSeriesAggregationType) Descriptor() protoreflect.EnumDescriptor {
	return file_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (TimeSeriesAggregationType) Type() protoreflect.EnumType {
	return &file_types_v1_types_proto_enumTypes[0]
}

func (x TimeSeriesAggregationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeSeriesAggregationType.Descriptor instead.
func (TimeSeriesAggregationType) EnumDescriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{0}
}

type LabelPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *LabelPair) Reset() {
	*x = LabelPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelPair) ProtoMessage() {}

func (x *LabelPair) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelPair.ProtoReflect.Descriptor instead.
func (*LabelPair) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *LabelPair) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LabelPair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ProfileType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	SampleType string `protobuf:"bytes,4,opt,name=sample_type,json=sampleType,proto3" json:"sample_type,omitempty"`
	SampleUnit string `protobuf:"bytes,5,opt,name=sample_unit,json=sampleUnit,proto3" json:"sample_unit,omitempty"`
	PeriodType string `protobuf:"bytes,6,opt,name=period_type,json=periodType,proto3" json:"period_type,omitempty"`
	PeriodUnit string `protobuf:"bytes,7,opt,name=period_unit,json=periodUnit,proto3" json:"period_unit,omitempty"`
}

func (x *ProfileType) Reset() {
	*x = ProfileType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileType) ProtoMessage() {}

func (x *ProfileType) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileType.ProtoReflect.Descriptor instead.
func (*ProfileType) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *ProfileType) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *ProfileType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProfileType) GetSampleType() string {
	if x != nil {
		return x.SampleType
	}
	return ""
}

func (x *ProfileType) GetSampleUnit() string {
	if x != nil {
		return x.SampleUnit
	}
	return ""
}

func (x *ProfileType) GetPeriodType() string {
	if x != nil {
		return x.PeriodType
	}
	return ""
}

func (x *ProfileType) GetPeriodUnit() string {
	if x != nil {
		return x.PeriodUnit
	}
	return ""
}

type Labels struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// LabelPair is the key value pairs to identify the corresponding profile
	Labels []*LabelPair `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *Labels) Reset() {
	*x = Labels{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Labels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Labels) ProtoMessage() {}

func (x *Labels) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Labels.ProtoReflect.Descriptor instead.
func (*Labels) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *Labels) GetLabels() []*LabelPair {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Series struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels []*LabelPair `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	Points []*Point     `protobuf:"bytes,2,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *Series) Reset() {
	*x = Series{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Series) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Series) ProtoMessage() {}

func (x *Series) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Series.ProtoReflect.Descriptor instead.
func (*Series) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{3}
}

func (x *Series) GetLabels() []*LabelPair {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Series) GetPoints() []*Point {
	if x != nil {
		return x.Points
	}
	return nil
}

type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// Milliseconds unix timestamp
	Timestamp int64 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{4}
}

func (x *Point) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *Point) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type LabelValuesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Matchers []string `protobuf:"bytes,2,rep,name=matchers,proto3" json:"matchers,omitempty"`
	Start    int64    `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	End      int64    `protobuf:"varint,4,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *LabelValuesRequest) Reset() {
	*x = LabelValuesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelValuesRequest) ProtoMessage() {}

func (x *LabelValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelValuesRequest.ProtoReflect.Descriptor instead.
func (*LabelValuesRequest) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{5}
}

func (x *LabelValuesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LabelValuesRequest) GetMatchers() []string {
	if x != nil {
		return x.Matchers
	}
	return nil
}

func (x *LabelValuesRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *LabelValuesRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type LabelValuesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
}

func (x *LabelValuesResponse) Reset() {
	*x = LabelValuesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelValuesResponse) ProtoMessage() {}

func (x *LabelValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelValuesResponse.ProtoReflect.Descriptor instead.
func (*LabelValuesResponse) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{6}
}

func (x *LabelValuesResponse) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

type LabelNamesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Matchers []string `protobuf:"bytes,1,rep,name=matchers,proto3" json:"matchers,omitempty"`
	Start    int64    `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End      int64    `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *LabelNamesRequest) Reset() {
	*x = LabelNamesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelNamesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelNamesRequest) ProtoMessage() {}

func (x *LabelNamesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelNamesRequest.ProtoReflect.Descriptor instead.
func (*LabelNamesRequest) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{7}
}

func (x *LabelNamesRequest) GetMatchers() []string {
	if x != nil {
		return x.Matchers
	}
	return nil
}

func (x *LabelNamesRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *LabelNamesRequest) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type LabelNamesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
}

func (x *LabelNamesResponse) Reset() {
	*x = LabelNamesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelNamesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelNamesResponse) ProtoMessage() {}

func (x *LabelNamesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelNamesResponse.ProtoReflect.Descriptor instead.
func (*LabelNamesResponse) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{8}
}

func (x *LabelNamesResponse) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

type BlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ulid       string           `protobuf:"bytes,1,opt,name=ulid,proto3" json:"ulid,omitempty"`
	MinTime    int64            `protobuf:"varint,2,opt,name=min_time,json=minTime,proto3" json:"min_time,omitempty"`
	MaxTime    int64            `protobuf:"varint,3,opt,name=max_time,json=maxTime,proto3" json:"max_time,omitempty"`
	Compaction *BlockCompaction `protobuf:"bytes,4,opt,name=compaction,proto3" json:"compaction,omitempty"`
	Labels     []*LabelPair     `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *BlockInfo) Reset() {
	*x = BlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfo) ProtoMessage() {}

func (x *BlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfo.ProtoReflect.Descriptor instead.
func (*BlockInfo) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{9}
}

func (x *BlockInfo) GetUlid() string {
	if x != nil {
		return x.Ulid
	}
	return ""
}

func (x *BlockInfo) GetMinTime() int64 {
	if x != nil {
		return x.MinTime
	}
	return 0
}

func (x *BlockInfo) GetMaxTime() int64 {
	if x != nil {
		return x.MaxTime
	}
	return 0
}

func (x *BlockInfo) GetCompaction() *BlockCompaction {
	if x != nil {
		return x.Compaction
	}
	return nil
}

func (x *BlockInfo) GetLabels() []*LabelPair {
	if x != nil {
		return x.Labels
	}
	return nil
}

type BlockCompaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level   int32    `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Sources []string `protobuf:"bytes,2,rep,name=sources,proto3" json:"sources,omitempty"`
	Parents []string `protobuf:"bytes,3,rep,name=parents,proto3" json:"parents,omitempty"`
}

func (x *BlockCompaction) Reset() {
	*x = BlockCompaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockCompaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockCompaction) ProtoMessage() {}

func (x *BlockCompaction) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockCompaction.ProtoReflect.Descriptor instead.
func (*BlockCompaction) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{10}
}

func (x *BlockCompaction) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *BlockCompaction) GetSources() []string {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *BlockCompaction) GetParents() []string {
	if x != nil {
		return x.Parents
	}
	return nil
}

// StackTraceSelector is used for filtering stack traces by locations.
type StackTraceSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Stack trace of the call site. Root at call_site[0].
	// Only stack traces having the prefix provided will be selected.
	// If empty, the filter is ignored.
	CallSite []*Location `protobuf:"bytes,1,rep,name=call_site,json=callSite,proto3" json:"call_site,omitempty"`
	// Stack trace selector for profiles purposed for Go PGO.
	// If set, call_site is ignored.
	GoPgo *GoPGO `protobuf:"bytes,2,opt,name=go_pgo,json=goPgo,proto3" json:"go_pgo,omitempty"`
}

func (x *StackTraceSelector) Reset() {
	*x = StackTraceSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StackTraceSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StackTraceSelector) ProtoMessage() {}

func (x *StackTraceSelector) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StackTraceSelector.ProtoReflect.Descriptor instead.
func (*StackTraceSelector) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{11}
}

func (x *StackTraceSelector) GetCallSite() []*Location {
	if x != nil {
		return x.CallSite
	}
	return nil
}

func (x *StackTraceSelector) GetGoPgo() *GoPGO {
	if x != nil {
		return x.GoPgo
	}
	return nil
}

type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{12}
}

func (x *Location) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GoPGO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the number of leaf locations to keep.
	KeepLocations uint32 `protobuf:"varint,1,opt,name=keep_locations,json=keepLocations,proto3" json:"keep_locations,omitempty"`
	// Aggregate callees causes the leaf location line number to be ignored,
	// thus aggregating all callee samples (but not callers).
	AggregateCallees bool `protobuf:"varint,2,opt,name=aggregate_callees,json=aggregateCallees,proto3" json:"aggregate_callees,omitempty"`
}

func (x *GoPGO) Reset() {
	*x = GoPGO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoPGO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoPGO) ProtoMessage() {}

func (x *GoPGO) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoPGO.ProtoReflect.Descriptor instead.
func (*GoPGO) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{13}
}

func (x *GoPGO) GetKeepLocations() uint32 {
	if x != nil {
		return x.KeepLocations
	}
	return 0
}

func (x *GoPGO) GetAggregateCallees() bool {
	if x != nil {
		return x.AggregateCallees
	}
	return false
}

type GetProfileStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetProfileStatsRequest) Reset() {
	*x = GetProfileStatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileStatsRequest) ProtoMessage() {}

func (x *GetProfileStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileStatsRequest.ProtoReflect.Descriptor instead.
func (*GetProfileStatsRequest) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{14}
}

type GetProfileStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether we received any data at any time in the past.
	DataIngested bool `protobuf:"varint,1,opt,name=data_ingested,json=dataIngested,proto3" json:"data_ingested,omitempty"`
	// Milliseconds since epoch.
	OldestProfileTime int64 `protobuf:"varint,2,opt,name=oldest_profile_time,json=oldestProfileTime,proto3" json:"oldest_profile_time,omitempty"`
	// Milliseconds since epoch.
	NewestProfileTime int64 `protobuf:"varint,3,opt,name=newest_profile_time,json=newestProfileTime,proto3" json:"newest_profile_time,omitempty"`
}

func (x *GetProfileStatsResponse) Reset() {
	*x = GetProfileStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_types_v1_types_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileStatsResponse) ProtoMessage() {}

func (x *GetProfileStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_types_v1_types_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileStatsResponse.ProtoReflect.Descriptor instead.
func (*GetProfileStatsResponse) Descriptor() ([]byte, []int) {
	return file_types_v1_types_proto_rawDescGZIP(), []int{15}
}

func (x *GetProfileStatsResponse) GetDataIngested() bool {
	if x != nil {
		return x.DataIngested
	}
	return false
}

func (x *GetProfileStatsResponse) GetOldestProfileTime() int64 {
	if x != nil {
		return x.OldestProfileTime
	}
	return 0
}

func (x *GetProfileStatsResponse) GetNewestProfileTime() int64 {
	if x != nil {
		return x.NewestProfileTime
	}
	return 0
}

var File_types_v1_types_proto protoreflect.FileDescriptor

var file_types_v1_types_proto_rawDesc = []byte{
	0x0a, 0x14, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x22, 0x35, 0x0a, 0x09, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb5, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x22,
	0x35, 0x0a, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0x5e, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x2b, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x27, 0x0a,
	0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x3b, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x22, 0x6c, 0x0a, 0x12, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e,
	0x64, 0x22, 0x2b, 0x0a, 0x13, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x57,
	0x0a, 0x11, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x2a, 0x0a, 0x12, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x22, 0xbd, 0x01, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x6c, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x22, 0x5b, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x6d, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x63,
	0x61, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x67, 0x6f, 0x5f, 0x70, 0x67,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x6f, 0x50, 0x47, 0x4f, 0x52, 0x05, 0x67, 0x6f, 0x50, 0x67, 0x6f, 0x22,
	0x1e, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x5b, 0x0a, 0x05, 0x47, 0x6f, 0x50, 0x47, 0x4f, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x65, 0x70,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0d, 0x6b, 0x65, 0x65, 0x70, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2b, 0x0a, 0x11, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x73, 0x22, 0x18, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x67, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x6c, 0x64, 0x65, 0x73,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x65, 0x73,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x65, 0x77, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0x6b, 0x0a, 0x19, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x52,
	0x49, 0x45, 0x53, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x4d, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x56, 0x45, 0x52, 0x41,
	0x47, 0x45, 0x10, 0x01, 0x42, 0x9b, 0x01, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x42, 0x0a, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x67, 0x72, 0x61, 0x66, 0x61, 0x6e, 0x61, 0x2f, 0x70, 0x79, 0x72, 0x6f, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x67, 0x6f, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x54, 0x58, 0x58, 0xaa, 0x02, 0x08, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x08, 0x54, 0x79, 0x70, 0x65, 0x73, 0x5c, 0x56, 0x31, 0xe2,
	0x02, 0x14, 0x54, 0x79, 0x70, 0x65, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x09, 0x54, 0x79, 0x70, 0x65, 0x73, 0x3a, 0x3a,
	0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_types_v1_types_proto_rawDescOnce sync.Once
	file_types_v1_types_proto_rawDescData = file_types_v1_types_proto_rawDesc
)

func file_types_v1_types_proto_rawDescGZIP() []byte {
	file_types_v1_types_proto_rawDescOnce.Do(func() {
		file_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(file_types_v1_types_proto_rawDescData)
	})
	return file_types_v1_types_proto_rawDescData
}

var file_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_types_v1_types_proto_goTypes = []interface{}{
	(TimeSeriesAggregationType)(0),  // 0: types.v1.TimeSeriesAggregationType
	(*LabelPair)(nil),               // 1: types.v1.LabelPair
	(*ProfileType)(nil),             // 2: types.v1.ProfileType
	(*Labels)(nil),                  // 3: types.v1.Labels
	(*Series)(nil),                  // 4: types.v1.Series
	(*Point)(nil),                   // 5: types.v1.Point
	(*LabelValuesRequest)(nil),      // 6: types.v1.LabelValuesRequest
	(*LabelValuesResponse)(nil),     // 7: types.v1.LabelValuesResponse
	(*LabelNamesRequest)(nil),       // 8: types.v1.LabelNamesRequest
	(*LabelNamesResponse)(nil),      // 9: types.v1.LabelNamesResponse
	(*BlockInfo)(nil),               // 10: types.v1.BlockInfo
	(*BlockCompaction)(nil),         // 11: types.v1.BlockCompaction
	(*StackTraceSelector)(nil),      // 12: types.v1.StackTraceSelector
	(*Location)(nil),                // 13: types.v1.Location
	(*GoPGO)(nil),                   // 14: types.v1.GoPGO
	(*GetProfileStatsRequest)(nil),  // 15: types.v1.GetProfileStatsRequest
	(*GetProfileStatsResponse)(nil), // 16: types.v1.GetProfileStatsResponse
}
var file_types_v1_types_proto_depIdxs = []int32{
	1,  // 0: types.v1.Labels.labels:type_name -> types.v1.LabelPair
	1,  // 1: types.v1.Series.labels:type_name -> types.v1.LabelPair
	5,  // 2: types.v1.Series.points:type_name -> types.v1.Point
	11, // 3: types.v1.BlockInfo.compaction:type_name -> types.v1.BlockCompaction
	1,  // 4: types.v1.BlockInfo.labels:type_name -> types.v1.LabelPair
	13, // 5: types.v1.StackTraceSelector.call_site:type_name -> types.v1.Location
	14, // 6: types.v1.StackTraceSelector.go_pgo:type_name -> types.v1.GoPGO
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_types_v1_types_proto_init() }
func file_types_v1_types_proto_init() {
	if File_types_v1_types_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_types_v1_types_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Labels); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Series); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelValuesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelValuesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelNamesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelNamesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockCompaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StackTraceSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoPGO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileStatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_types_v1_types_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_types_v1_types_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_types_v1_types_proto_goTypes,
		DependencyIndexes: file_types_v1_types_proto_depIdxs,
		EnumInfos:         file_types_v1_types_proto_enumTypes,
		MessageInfos:      file_types_v1_types_proto_msgTypes,
	}.Build()
	File_types_v1_types_proto = out.File
	file_types_v1_types_proto_rawDesc = nil
	file_types_v1_types_proto_goTypes = nil
	file_types_v1_types_proto_depIdxs = nil
}
