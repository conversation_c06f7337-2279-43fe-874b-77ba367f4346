// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

/*
# ApdexCalculator: apdex 状态计算器

processor:
  # 标准 apdex 计算方式
  - name: "apdex_calculator/standard"
    config:
      calculator:
        type: "standard"
	    rules:
	      - kind: ""
	        metric_name: "bk_apm_duration"
	        destination: "apdex_type"
	        apdex_t: 20 # ms

  # 固定 apdex 状态（测试用途）
  - name: "apdex_calculator/fixed"
    config:
      calculator:
        type: "fixed"
        apdex_status: "satisfied"
*/

package apdexcalculator
