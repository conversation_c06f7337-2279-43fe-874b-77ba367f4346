// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

/*
# ServiceDiscover: 自定义服务发现

processor:
  - name: "service_discover/common"
    config:
      rules:
        - service: "my-service"
          type: "http"
          match_type: "manual" # 手动匹配（即用户指定规则）
          predicate_key: "attributes.http.method"
          kind: "SPAN_KIND_CLIENT"
          match_key: "attributes.http.url"
          match_groups:
            - source: "service"
              destination: "peer.service"
          rule:
            params:
              - name: "version"
                operator: "eq"
                value: "v1"
            host:
              value: "https://doc.weixin.qq.com"
              operator: eq
            path:
              value: "/api/v1/users"
              operator: nq

        - service: "None"
          type: "http"
          match_type: "auto" # 自动匹配（使用正则）
          predicate_key: "attributes.http.method"
          kind: "SPAN_KIND_CLIENT"
          match_key: "attributes.http.url"
          match_groups:
            - source: "peer_service"
              destination: "peer.service"
            - source: "span_name"
              destination: "span_name"
          rule:
            regex: "https://(?P<peer_service>[^/]+)/(?P<span_name>\\w+)/.+"
*/

package servicediscover
