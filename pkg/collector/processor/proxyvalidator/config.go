// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package proxyvalidator

const (
	dataTypeTimeSeries = "time_series" // 时序数据类型
	dataTypeEvent      = "event"       // 事件数据类型
)

type Config struct {
	Type                string `config:"type" mapstructure:"type"`
	Version             string `config:"version" mapstructure:"version"`
	MaxFutureTimeOffset int64  `config:"max_future_time_offset" mapstructure:"max_future_time_offset"`
}
