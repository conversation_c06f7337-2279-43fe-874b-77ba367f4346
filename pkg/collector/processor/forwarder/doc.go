// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

/*
# Forwarder: 数据转发器

processor:
   - name: "forwarder/traces"
     config:
       resolver:
         identifier: "localhost:4316" # 本机标识
         type: "static" # 静态解析器
         endpoints: # 集群服务端点
         - "localhost:4316"
         - "localhost:4315"
*/

package forwarder
