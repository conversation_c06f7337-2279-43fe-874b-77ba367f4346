// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package forwarder

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNoopResolver(t *testing.T) {
	r := NewResolver(ResolverConfig{})
	assert.Equal(t, resolverTypeNoop, r.Type())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		r.Watch()
	}()

	assert.NoError(t, r.Stop())
	wg.Wait()
}

func TestStaticResolver(t *testing.T) {
	r := NewResolver(ResolverConfig{
		Type:      resolverTypeStatic,
		Endpoints: []string{":1001", ":1002"},
	})
	assert.Equal(t, resolverTypeStatic, r.Type())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()

		ch := r.Watch()
		count := 0
		for range ch {
			count++
			if count >= 2 {
				break
			}
		}
	}()

	assert.NoError(t, r.Stop())
	wg.Wait()
}
