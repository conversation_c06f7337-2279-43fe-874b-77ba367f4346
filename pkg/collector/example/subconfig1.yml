type: "subconfig"
token: "sub_token1"

default:
  processor:
    - name: "db_filter/common"
      config:
        slow_query:
          destination: "db.is_slow"
          rules:
            - match: "Mysql"
              threshold: 50ms
            - match: "Postgresql"
              threshold: 50ms
            - match: "Elasticsearch"
              threshold: 50ms
            - match: "Redis"
              threshold: 50ms
            - match: "Mangodb"
              threshold: 50ms
            - match: ""
              threshold: 3s

    - name: "traces_deriver/delta"
      config:
        operations:
          - type: "delta"
            metric_name: "bk_apm_count"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"

#    - name: "traces_deriver/delta_duration"
#      config:
#        operations:
#          - type: "delta_duration"
#            metric_name: "bk_apm_duration_delta"
#            publish_interval: "10s"
#            gc_interval: "1h"
#            max_series: 1000
#            rules:
#              - kind: "SPAN_KIND_SERVER"
#                predicate_key: ""
#                dimensions:
#                  - "resource.bk.instance.id"
#                  - "span_name"
#                  - "kind"
#                  - "status.code"
#                  - "resource.service.name"
#                  - "resource.service.version"
#                  - "resource.telemetry.sdk.name"
#                  - "resource.telemetry.sdk.version"
#                  - "resource.telemetry.sdk.language"
#                  - "attributes.peer.service"
#                  - "attributes.apdex_type"
