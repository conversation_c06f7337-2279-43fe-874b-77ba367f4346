{"resourceSpans": [{"resource": {"attributes": [{"key": "environment", "value": {"stringValue": "test"}}, {"key": "service.name", "value": {"stringValue": "traces-demo"}}, {"key": "service.version", "value": {"stringValue": "v1.0.0"}}, {"key": "telemetry.sdk.language", "value": {"stringValue": "go"}}, {"key": "telemetry.sdk.name", "value": {"stringValue": "opentelemetry"}}, {"key": "telemetry.sdk.version", "value": {"stringValue": "1.9.0"}}]}, "scopeSpans": [{"scope": {"name": "traces-demo/v1"}, "spans": [{"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "6a8dae0c50af3614", "parentSpanId": "6369383b235afc6c", "name": "getAgeFromLocalCache", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449127599642000", "endTimeUnixNano": "1676449127838756513", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "f5890796e5ff2d5d", "parentSpanId": "22d11858633ebe46", "name": "getAgeFromCacheServer", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449127839060000", "endTimeUnixNano": "1676449128413515304", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "20d386bb18ddb6a0", "parentSpanId": "6369383b235afc6c", "name": "queryAgeWithTraces-server", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449127838775000", "endTimeUnixNano": "1676449128413668687", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "ec87ae4599c0286a", "parentSpanId": "", "name": "queryAgeWithTraces-client", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449127599377000", "endTimeUnixNano": "1676449128413759632", "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "d63d0cb31b38a0d3", "parentSpanId": "7d808c8ba377dbd9", "name": "getAgeFromLocalCache", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449130599706000", "endTimeUnixNano": "1676449131350739158", "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "0b36dd0c8ceb45c9", "parentSpanId": "", "name": "queryAgeWithTraces-client", "kind": "SPAN_KIND_INTERNAL", "startTimeUnixNano": "1676449130599442000", "endTimeUnixNano": "1676449131350927164", "status": {}}]}, {"scope": {"name": "go.opentelemetry.io/otel/instrumentation/httptrace", "version": "semver:0.34.0"}, "spans": [{"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "58dae1950c51a280", "parentSpanId": "20d386bb18ddb6a0", "name": "http.getconn", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449127838828000", "endTimeUnixNano": "1676449127838839287", "attributes": [{"key": "http.host", "value": {"stringValue": "localhost:56099"}}, {"key": "http.remote", "value": {"stringValue": "127.0.0.1:56099"}}, {"key": "http.local", "value": {"stringValue": "127.0.0.1:55406"}}, {"key": "http.conn.reused", "value": {"boolValue": true}}, {"key": "http.conn.wasidle", "value": {"boolValue": true}}, {"key": "http.conn.idletime", "value": {"stringValue": "2.032274375s"}}], "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "0aa672973c0a3607", "parentSpanId": "20d386bb18ddb6a0", "name": "http.headers", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449127838856000", "endTimeUnixNano": "1676449127838865764", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "07174a8016de67ab", "parentSpanId": "20d386bb18ddb6a0", "name": "http.send", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449127838868000", "endTimeUnixNano": "1676449127838869310", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "533e76e93ae93625", "parentSpanId": "20d386bb18ddb6a0", "name": "http.receive", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449128413630000", "endTimeUnixNano": "1676449128413666602", "status": {}}, {"traceId": "056bf008474fe2c3a45aa1987de78f48", "spanId": "84f20043e3bf9dcd", "parentSpanId": "ec87ae4599c0286a", "name": "http.receive", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449128413743000", "endTimeUnixNano": "1676449128413767050", "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "790ae1cef95cb3a7", "parentSpanId": "0b36dd0c8ceb45c9", "name": "http.getconn", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449130599493000", "endTimeUnixNano": "1676449130599504746", "attributes": [{"key": "http.host", "value": {"stringValue": "localhost:56089"}}, {"key": "http.remote", "value": {"stringValue": "127.0.0.1:56089"}}, {"key": "http.local", "value": {"stringValue": "127.0.0.1:55405"}}, {"key": "http.conn.reused", "value": {"boolValue": true}}, {"key": "http.conn.wasidle", "value": {"boolValue": true}}, {"key": "http.conn.idletime", "value": {"stringValue": "2.18569397s"}}], "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "3610aef2bb208ca3", "parentSpanId": "0b36dd0c8ceb45c9", "name": "http.headers", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449130599528000", "endTimeUnixNano": "1676449130599544328", "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "d0871ef5e3b7919e", "parentSpanId": "0b36dd0c8ceb45c9", "name": "http.send", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449130599546000", "endTimeUnixNano": "1676449130599547380", "status": {}}, {"traceId": "9c2209516394721ea9c9a69803f0c805", "spanId": "99e0073bf386d94e", "parentSpanId": "0b36dd0c8ceb45c9", "name": "http.receive", "kind": "SPAN_KIND_CLIENT", "startTimeUnixNano": "1676449131350893000", "endTimeUnixNano": "1676449131350929512", "status": {}}]}], "schemaUrl": "https://opentelemetry.io/schemas/1.12.0"}]}