# Beat self-config
# ================================ Logging ===================================
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/logp/config.go
logging:
  level: error
  rotateeverybytes: 10485760
  keepfiles: 7


# ================================ Output ====================================
# console for debugging
output.console:

# bkpipe for production
#output.bkpipe:
#  endpoint: /var/run/ipc.state.report
#  synccfg: true


# ================================= Path =====================================
#path:
#  logs: /var/log/gse_opbk
#  data: /var/lib/gse_opbk
#  pid: /var/run/gse_opbk


# ============================ Publisher Queue ===============================
# publisher 发送队列配置
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/publisher/queue/memqueue/config.go
queue:
  mem:
    events: 512
    flush.min_events: 0
    flush.timeout: "1s"


# ============================== Monitoring ==================================
xpack:
  monitoring:
    enabled: false


# ================================= Resource =================================
resource_limit:
  enabled: false
#  # CPU 资源限制 单位 core(float64)
#  cpu: 1
#  # 内存资源限制 单位 MB(int)
#  mem: 512


# bk-collector self-config
bk-collector:
  # =============================== SubConfig ================================
  apm:
    patterns:
#      - "./platform.yml"
#      - "./subconfig.yml"


  # =============================== Logging ==================================
  logging:
    stdout: true
    # optional: logfmt/json/console
    format: "logfmt"
    level: debug
    path: /var/log/gse
    maxsize: 10
    maxage: 3
    backups: 5


  # =============================== Receiver =================================
  receiver:
    # Http Server Config
    http_server:
      # 是否启动 Http 服务
      # default: false
      enabled: true
      # 服务监听端点
      # default: ""
      endpoint: ":4318"
      middlewares:
        - "logging"
        - "cors"
        - "content_decompressor"

    # Grpc Server Config
    grpc_server:
      # 是否启动 Grpc 服务
      # default: false
      enabled: true
      # 传输协议，目前支持 tcp
      # default: ""
      transport: "tcp"
      # 服务监听端点
      # default: ""
      endpoint: ":4317"

    components:
      jaeger:
        enabled: true
      otlp:
        enabled: true
      pushgateway:
        enabled: true
      zipkin:
        enabled: false


  # =============================== Processor ================================
  processor:
    # ApdexCalculator: 健康度状态计算器
    - name: "apdex_calculator/standard"
      config:
        calculator:
          type: "standard"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"
            apdex_t: 20 # ms

    # AttributeFilter: 属性过滤处理器
    - name: "attribute_filter/as_string"
      config:
        as_string:
          keys:
            - "attributes.http.host"

    # ResourceFilter: 资源过滤处理器
    - name: "resource_filter/instance_id"
      config:
        assemble:
          - destination: "bk.instance.id"
            separator: ":"
            keys:
              - "resource.telemetry.sdk.language"
              - "resource.service.name"
              - "resource.net.host.name"
              - "resource.net.host.ip"
              - "resource.net.host.port"
        drop:
          keys:
            - "bk.data.token"

    # Sampler: 采样处理器
    - name: "sampler/random"
      config:
        type: "random"
        sampling_percentage: 100

    # TokenChecker: 权限校验处理器
    - name: "token_checker/fixed"
      config:
        type: "fixed"
        fixed_token: "token1"
        resource_key: "bk.data.token"
        traces_dataid: 1000   # default: 0
        metrics_dataid: 1001  # default: 0
        logs_dataid: 1002     # default: 0

    # TracesDeriver: Traces 派生处理器
    - name: "traces_deriver/duration"
      config:
        operations:
          - type: "duration"
            metric_name: "bk_apm_duration"
            rules:
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "attributes.http.method"
                  - "attributes.http.status_code"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"

              - kind: "SPAN_KIND_SERVER"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "attributes.http.server_name"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.http.method"
                  - "attributes.http.scheme"
                  - "attributes.http.flavor"
                  - "attributes.http.status_code"


  # =============================== Pipeline =================================
  pipeline:
    - name: "traces_pipeline/common"
      type: "traces"
      processors:
        - "resource_filter/instance_id"
        - "attribute_filter/as_string"
        - "token_checker/fixed"
#        - "service_discover/common"
        - "traces_deriver/duration"
        - "sampler/random"

    - name: "metrics_pipeline/derived"
      type: "metrics.derived"
      processors:
        - "token_checker/fixed"
        - "apdex_calculator/standard"

    - name: "pushgateway_pipeline/common"
      type: "pushgateway"
      processors:
        - "token_checker/fixed"
        - "apdex_calculator/standard"


  # =============================== Exporter =================================
  exporter:
    queue:
      logs_batch_size: 1
      metrics_batch_size: 1
      traces_batch_size: 1
      flush_interval: 10s
