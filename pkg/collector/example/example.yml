# Beat self-config
# ================================ Logging ===================================
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/logp/config.go
logging:
  level: error
  rotateeverybytes: 10485760
  keepfiles: 7


# ================================ Output ====================================
# console for debugging
output.console:

# drop data for debugging
#output.dropper:

# bkpipe for production
#output.bkpipe:
#  endpoint: /var/run/ipc.state.report
#  synccfg: true


# ================================= Path =====================================
#path:
#  logs: /var/log/gse_opbk
#  data: /var/lib/gse_opbk
#  pid: /var/run/gse_opbk


# ============================ Publisher Queue ===============================
# publisher 发送队列配置
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/publisher/queue/memqueue/config.go
queue:
  mem:
    events: 1024
    flush.min_events: 0
    flush.timeout: "1s"


# ============================== Monitoring ==================================
xpack:
  monitoring:
    enabled: false


# ============================== Resource ====================================
resource_limit:
  enabled: false
#  # CPU 资源限制 单位 core(float64)
#  cpu: 1
#  # 内存资源限制 单位 MB(int)
#  mem: 512


# bk-collector self-config
bk-collector:
  # =============================== SubConfig ================================
  apm:
    patterns:
      - "./example/fixtures/report_*.yml"
      - "./example/platform.yml"
      - "./example/subconfig*.yml"

  # =============================== Logging ==================================
  logging:
    stdout: true
    # optional: logfmt/json/console
    format: "console"
    level: debug
    path: /var/log/gse
    maxsize: 10
    maxage: 3
    backups: 5

  # ============================= Metrics Push ===============================
  bk_metrics_pusher:
    disabled: true
    dataid: 1100014
    period: 60s
    batch_size: 1024
    labels: [ ]
    metric_relabel_configs:

  # ================================= Proxy ==================================
  proxy:
    disabled: false
    auto_reload: true
    http:
      host: ""
      port: 10205
      middlewares:
        - "logging"
        - "maxconns;maxConnectionsRatio=256"

  # ============================== Pingserver ================================
  pingserver:
    disabled: true
    auto_reload: true
    patterns:
      - "./example/fixtures/pingserver_sub*.yml"

  # ================================ Cluster =================================
  cluster:
    disabled: false
    address: ":4316"

  # =============================== Receiver =================================
  receiver:
    disabled: false
    # Http Server Config
    http_server:
      # 是否启动 Http 服务
      # default: false
      enabled: true
      # 服务监听端点
      # default: ""
      endpoint: ":4318"
      # 服务中间件，目前支持：logging/cors/content_decompressor
      max_request_bytes: 10240000
      middlewares:
        - "logging"
        - "cors"
        - "content_decompressor"
        - "maxconns;maxConnectionsRatio=256"
        - "maxbytes;maxRequestBytes=209715200"

    admin_server:
      # 是否启动 Http 服务
      # default: false
      enabled: true
      # 服务监听端点
      # default: ""
      endpoint: "127.0.0.1:4310"
      middlewares:
        - "logging"

    # Grpc Server Config
    grpc_server:
      # 是否启动 Grpc 服务
      # default: false
      enabled: true
      # 传输协议，目前支持 tcp
      # default: ""
      transport: "tcp"
      # 服务监听端点
      # default: ""
      endpoint: ":4317"
      middlewares:
        - "maxbytes;maxRequestBytes=8388608"

    # Tars Server Config
    tars_server:
      # 是否启动 Tars 服务
      # default: false
      enabled: true
      # 传输协议，目前支持 tcp
      # default: ""
      transport: "tcp"
      # 服务监听端点
      # default: ""
      endpoint: ":4319"

    components:
      jaeger:
        enabled: true
      otlp:
        enabled: true
      pushgateway:
        enabled: true
      remotewrite:
        enabled: true
      zipkin:
        enabled: true
      skywalking:
        enabled: true
      pyroscope:
        enabled: true
      fta:
        enabled: true
      beat:
        enabled: true
      tars:
        enabled: true

  # =============================== Processor ================================
  # name: 名称规则为 ${processor}[/${id}]，id 字段为可选项
  # config: 配置内容
  # supported processors:
  # - apdex_calculator: [random, fixed, standard]
  # - attribute_filter: [as_string]
  # - metrics_filter: [drop, replace]
  # - rate_limiter: [noop, token_bucket]
  # - resource_filter: [drop, add, replace, assemble]
  # - sampler: [random]
  # - service_discover
  # - proxy_validator
  # - token_chcker: [fixed, random, aes256]
  # - traces_deriver: [duration]

  processor:
    # ApdexCalculator: 健康度状态计算器
    # Fixed
    - name: "apdex_calculator/fixed"
      config:
        calculator:
          type: "fixed"
          apdex_status: "frustrated"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"

    # ApdexCalculator: 健康度状态计算器
    # Random
    - name: "apdex_calculator/random"
      config:
        calculator:
          type: "random"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"

    # ApdexCalculator: 健康度状态计算器
    # Standard
    - name: "apdex_calculator/standard"
      config:
        calculator:
          type: "standard"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"
            apdex_t: 20 # ms


    # AttributeFilter: 属性过滤处理器
    # AsString: 将 attributes 字段里的某些维度类型转为 string
    - name: "attribute_filter/common"
      config:
        as_string:
          keys:
            - "attributes.http.host"
        as_int:
          keys:
            - "attributes.http.status_code"
        from_token:
          biz_id: "bk_biz_id"
          app_name: "bk_app_name"
        assemble:
          - destination: "api_name"
            predicate_key: "attributes.http.scheme"  # HTTP
            default_from: "span_name"
            rules:
              - kind: "SPAN_KIND_CLIENT"
                keys:
                  - "attributes.http.method"
                  - "attributes.http.host"
                  - "attributes.http.target"
                separator: ":"
                placeholder: "Unknown"
              - kind: "SPAN_KIND_SERVER"
                keys:
                  - "attributes.http.method"
                  - "attributes.http.route"
                separator: ":"
                placeholder: "Unknown"

          - destination: "api_name"
            predicate_key: "attributes.rpc.system"  # RPC
            default_from: "span_name"
            rules:
              - kind: ""
                keys:
                  - "attributes.rpc.method"
                separator: ":"
                placeholder: "Unknown"

          - destination: "api_name"
            predicate_key: "attributes.db.system"  # DataBase
            default_from: "span_name"
            rules:
              - kind: ""
                first_upper:
                  - "attributes.db.system"
                keys:
                  - "attributes.db.system"
                  - "attributes.db.operation"
                  - "attributes.db.name"
                separator: ":"
                placeholder: "Unknown"

          - destination: "api_name"
            predicate_key: "attributes.messaging.system"  # Messaging
            default_from: "span_name"
            rules:
              - kind: "SPAN_KIND_PRODUCER"
                first_upper:
                  - "attributes.messaging.system"
                keys:
                  - "attributes.messaging.system"
                  - "const.producer"
                  - "attributes.topic"
                separator: ":"
                placeholder: "Unknown"
              - kind: "SPAN_KIND_CONSUMER"
                first_upper:
                  - "attributes.messaging.system"
                keys:
                  - "attributes.messaging.system"
                  - "const.consumer"
                  - "attributes.topic"
                separator: ":"
                placeholder: "Unknown"

    # Attribute_filter 应用层级的配置
    - name: "attribute_filter/app"
      config:

    # Probe_filter 探针采集过滤器
    - name: "probe_filter/common"
      config:


    # MetricsFilter: 指标过滤处理器
    # Drop
    - name: "metrics_filter/drop"
      config:
        drop:
          # metrics: metric name
          metrics:
            - "runtime.go.mem.live_objects"
            - "none.exist.metric"

    # MetricsFilter: 指标过滤处理器
    # Replace
    - name: "metrics_filter/replace"
      config:
        replace:
          - source: "previous_metric"       # 原字段
            destination: "current_metric"   # 新字段


    # RateLimiter: 流控处理器
    # TokenBucket: 令牌桶限流
    - name: "rate_limiter/token_bucket"
      config:
        type: token_bucket
        qps: 500
        burst: 1000

    # RateLimiter: 流控处理器
    # Noop: 放行所有请求
    - name: "rate_limiter/noop"
      config:
        type: noop

    # ResourceFilter: 资源过滤处理器
    # Drop
    - name: "resource_filter/drop"
      config:
        drop:
          keys:
            - "resource.service.name"
            - "resource.service.sdk"

    # ResourceFilter: 资源过滤处理器
    # Add
    - name: "resource_filter/add"
      config:
        add:
          # [{label: value1, label: value2}, ...]
          - label: "fake_new_key"
            value: "fake_new_value"

    # ResourceFilter: 资源过滤处理器
    # Replace
    - name: "resource_filter/replace"
      config:
        replace:
          # [{source: label_src, destination: label_dst}, ...]
          - source: "telemetry.sdk.version"
            destination: "telemetry.bksdk.version"

    # ResourceFilter: 资源过滤处理器
    - name: "resource_filter/common"
      config:
        assemble:
          - destination: "bk.instance.id" # 转换后名称
            separator: ":"
            keys:
              - "resource.telemetry.sdk.language"
              - "resource.service.name"
              - "resource.net.host.name"
              - "resource.net.host.ip"
              - "resource.net.host.port"
        drop:
          keys:
            - "resource.bk.data.token"

        from_record:
          - source: "request.client.ip"
            destination: "resource.client.ip"

    - name: "resource_filter/drop_token"
      config:
        assemble:
        drop:
          keys:
            - "resource.bk.data.token"

    # ResourceFilter: 资源过滤处理器
    - name: "resource_filter/metrics"
      config:
        assemble:
        drop:
          keys:
            - "resource.bk.data.token"
            - "resource.process.pid"

    # Sampler: 采样处理器
    # StatusCode
    - name: "sampler/status_code"
      config:
        type: "status_code"
        storage_policy: "full"
        max_spans: 100
        status_code:
          - "ERROR"


    # ServiceDiscover: 服务发现处理器
    - name: "service_discover/common"
      config:
        rules:
          - service: "my-service"
            type: "http"
            match_type: "manual"
            predicate_key: "attributes.http.method"
            kind: "SPAN_KIND_CLIENT"
            match_key: "attributes.http.url"
            match_groups:
              - source: "service"
                destination: "peer.service"
            rule:
              params:
                - name: "version"
                  operator: "eq"
                  value: "v1"
                - name: "user"
                  operator: "nq"
                  value: "mando"
              host:
                value: "https://doc.weixin.qq.com"
                operator: eq
              path:
                value: "/api/v1/users"
                operator: nq

          - service: "None"
            type: "http"
            match_type: "auto"
            predicate_key: "attributes.http.method"
            kind: "SPAN_KIND_CLIENT"
            match_key: "attributes.http.url"
            match_groups:
              - source: "peer_service"
                destination: "peer.service"
              - source: "span_name"
                destination: "span_name"
            rule:
              regex: "https://(?P<peer_service>[^/]+)/(?P<span_name>\\w+)/.+"

    # TokenChecker: 权限校验处理器
    # Aes256
    - name: "token_checker/aes256"

    # Beat 鉴权
    - name: "token_checker/beat"
      config:
        type: "beat"

    # LicenseChecker: 验证接入的节点数
    - name: "license_checker/common"

    # DbFilter: db 处理器
    - name: "db_filter/common"

    # TokenChecker: 权限校验处理器
    # Proxy
    - name: "token_checker/proxy"
      config:
        type: "proxy"

    # Pprof converter: pprof 协议转换器
    - name: "pprof_translator/common"
      config:
        type: "spy"

    # ProxyValidator: proxy 数据校验器
    - name: "proxy_validator/common"

    # Forwarder: 数据转发器
    # Traces
    - name: "forwarder/traces"
      config:
        resolver:
          identifier: "localhost:4316"
          type: "static"
          endpoints:
            - "localhost:4316"
            - "localhost:4315"


    # TracesDeriver: Traces 派生处理器
    # Count
    - name: "traces_deriver/count"
      config:
        operations:
          - type: "count"
            metric_name: "bk_apm_total"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Delta
    - name: "traces_deriver/delta"
      config:
        operations:
          - type: "delta"
            metric_name: "bk_apm_count"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    - name: "traces_deriver/delta_duration"
      config:
        operations:
          - type: "delta_duration"
            metric_name: "bk_apm_duration_delta"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Min
    - name: "traces_deriver/min"
      config:
        operations:
          - type: "min"
            metric_name: "bk_apm_duration_min"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Max
    - name: "traces_deriver/max"
      config:
        operations:
          - type: "max"
            metric_name: "bk_apm_duration_max"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Sum
    - name: "traces_deriver/sum"
      config:
        operations:
          - type: "sum"
            metric_name: "bk_apm_duration_sum"
            publish_interval: "10s"
            gc_interval: "1h"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Bucket
    - name: "traces_deriver/bucket"
      config:
        operations:
          - type: "bucket"
            metric_name: "bk_apm_duration_bucket"
            publish_interval: "10s"
            gc_interval: "1h"
            buckets: [ 0.01, 0.05, 0.1, 0.5, 1, 2, 5 ]
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"

    # TracesDeriver: Traces 派生处理器
    # Duration
    - name: "traces_deriver/duration"
      config:
        operations:
          - type: "duration"
            metric_name: "bk_apm_duration"
            gc_interval: "15s"
            max_series: 1000
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_SERVER"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.http.server_name"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.http.method"
                  - "attributes.http.scheme"
                  - "attributes.http.flavor"
                  - "attributes.http.status_code"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_SERVER"
                predicate_key: "attributes.rpc.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.rpc.method"
                  - "attributes.rpc.service"
                  - "attributes.rpc.system"
                  - "attributes.rpc.grpc.status_code"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.http.method"
                  - "attributes.http.status_code"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.rpc.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.rpc.method"
                  - "attributes.rpc.service"
                  - "attributes.rpc.system"
                  - "attributes.rpc.grpc.status_code"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.db.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.db.name"
                  - "attributes.db.operation"
                  - "attributes.db.system"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_PRODUCER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_PRODUCER"
                predicate_key: "attributes.messaging.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.messaging.system"
                  - "attributes.messaging.destination"
                  - "attributes.messaging.destination_kind"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
                  - "attributes.celery.action"
                  - "attributes.celery.task_name"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CONSUMER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.apdex_type"
              - kind: "SPAN_KIND_CONSUMER"
                predicate_key: "attributes.messaging.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.messaging.system"
                  - "attributes.celery.state"
                  - "attributes.celery.action"
                  - "attributes.apdex_type"


  # =============================== Pipeline =================================
  # name: pipeline 名称作为标识
  # type: pipeline 处理类型，可选项：traces/metrics/logs 以及派生项: traces.derived/metrics.derived/logs.derived
  # processors: 上述定义的处理器名称
  pipeline:
    - name: "traces_pipeline/common"
      type: "traces"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"
        #- "license_checker/common"
        #- "forwarder/traces"
        #- "sampler/status_code"
        - "resource_filter/common"
        - "attribute_filter/common"
        - "db_filter/common"
        - "attribute_filter/app"
        - "probe_filter/common"
        - "service_discover/common"
        - "apdex_calculator/standard"
        - "traces_deriver/duration"
        - "traces_deriver/delta"
        - "traces_deriver/delta_duration"
        - "traces_deriver/count"
        - "traces_deriver/min"
        - "traces_deriver/max"
        - "traces_deriver/sum"
        - "traces_deriver/bucket"

    - name: "logs_pipeline/common"
      type: "logs"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

    - name: "pushgateway_pipeline/common"
      type: "pushgateway"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

    - name: "remotewrite_pipeline/common"
      type: "remotewrite"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

    - name: "proxy_pipeline/common"
      type: "proxy"
      processors:
        - "token_checker/proxy"
        - "rate_limiter/token_bucket"
        - "proxy_validator/common"

    - name: "pingserver_pipeline/common"
      type: "pingserver"
      processors:

    - name: "metrics_pipeline/common"
      type: "metrics"
      processors:
        - "token_checker/aes256"
        - "resource_filter/metrics"
#        - "rate_limiter/token_bucket"
#        - "metrics_filter/drop"
#        - "resource_filter/add"
#        - "apdex_calculator/fixed"

    - name: "metrics_pipeline/derived"
      type: "metrics.derived"
      processors:
#        - "apdex_calculator/standard"

    - name: "profiles_pipeline/common"
      type: "profiles"
      processors:
        - "token_checker/aes256"
        - "pprof_translator/common"

    - name: "profiles_pipeline/common"
      type: "fta"
      processors:
          - "token_checker/aes256"
          - "rate_limiter/token_bucket"

    - name: "beat_pipeline/common"
      type: "beat"
      processors:
        - "token_checker/beat"
        - "rate_limiter/token_bucket"

    - name: "tars_pipeline/common"
      type: "tars"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

  # =============================== Exporter =================================
  exporter:
    slow_send:
      enabled: true
      # 慢发送检查周期
      check_interval: 31m
      # 慢发送 p95 阈值
      threshold: 5s
    queue:
      logs_batch_size: 1
      metrics_batch_size: 1
      traces_batch_size: 1
      flush_interval: 10s
