type: "platform"

processor:
  # TokenChecker: 权限校验处理器
  - name: "token_checker/aes256"
    config:
      type: "aes256"
      resource_key: "bk.data.token"
      salt: "bk" # 加盐解密标识
      decoded_iv: "bkbkbkbkbkbkbkbk"
      decoded_key: "81be7fc6-5476-4934-9417-6d4d593728db"


  - name: "resource_filter/common"
    config:
      assemble:
        - destination: "bk.instance.id" # 转换后名称
          separator: ":"
          keys:
            - "resource.telemetry.sdk.language"
            - "resource.service.name"
            - "resource.net.host.name"
            - "resource.net.host.ip"
            - "resource.net.host.port"
      drop:
        keys:
          - "resource.bk.data.token"
