# Beat self-config
# ================================ Logging ===================================
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/logp/config.go
logging:
  level: error
  rotateeverybytes: 10485760
  keepfiles: 7


# ================================ Output ====================================
# console for debugging
output.console:

# drop data for debugging
#output.dropper:

# bkpipe for production
#output.bkpipe:
#  endpoint: /var/run/ipc.state.report
#  synccfg: true


# ================================= Path =====================================
#path:
#  logs: /var/log/gse_opbk
#  data: /var/lib/gse_opbk
#  pid: /var/run/gse_opbk


# ============================ Publisher Queue ===============================
# publisher 发送队列配置
# repo: https://github.com/TencentBlueKing/beats
# path: libbeat/publisher/queue/memqueue/config.go
queue:
  mem:
    events: 1024
    flush.min_events: 0
    flush.timeout: "1s"


# ============================== Monitoring ==================================
xpack:
  monitoring:
    enabled: false


# ================================= Resource =================================
resource_limit:
  enabled: false
#  # CPU 资源限制 单位 core(float64)
#  cpu: 1
#  # 内存资源限制 单位 MB(int)
#  mem: 512


# bk-collector self-config
bk-collector:
  # =============================== SubConfig ================================
  apm:
    patterns:
      - "./bk-collector-platform.yml"
      - "./bk-collector-subconfig.yml"

  # =============================== Logging ==================================
  logging:
    stdout: true
    # optional: logfmt/json/console
    format: "console"
    level: debug
    path: /var/log/gse
    maxsize: 10
    maxage: 3
    backups: 5


  # =============================== Receiver =================================
  receiver:
    disabled: false
    # Http Server Config
    http_server:
      # 是否启动 Http 服务
      # default: false
      enabled: true
      # 服务监听端点
      # default: ""
      endpoint: ":4318"
      # 服务中间件，目前支持：logging/cors/content_decompressor
      middlewares:
        - "logging"
        - "cors"
        - "content_decompressor"

    # Grpc Server Config
    grpc_server:
      # 是否启动 Grpc 服务
      # default: false
      enabled: true
      # 传输协议，目前支持 tcp
      # default: ""
      transport: "tcp"
      # 服务监听端点
      # default: ""
      endpoint: ":4317"

    # Tars Server Config
    tars_server:
      # 是否启动 Tars 服务
      # default: false
      enabled: false
      # 传输协议，目前支持 tcp
      # default: ""
      transport: "tcp"
      # 服务监听端点
      # default: ""
      endpoint: ":4319"

    components:
      jaeger:
        enabled: true
      otlp:
        enabled: true
      pushgateway:
        enabled: true
      zipkin:
        enabled: false
      fta:
        enabled: true
      tars:
        enabled: false

  # =============================== Processor ================================
  # name: 名称规则为 ${processor}[/${id}]，id 字段为可选项
  # config: 配置内容
  # supported processors:
  # - apdex_calculator: [random, fixed, standard]
  # - attribute_filter: [as_string]
  # - metrics_filter: [drop, replace]
  # - rate_limiter: [noop, token_bucket]
  # - resource_filter: [drop, add, replace, assemble]
  # - sampler: [random]
  # - service_discover
  # - token_chcker: [fixed, random, aes256]
  # - traces_deriver: [duration]

  processor:
    # ApdexCalculator: 健康度状态计算器
    # Fixed
    - name: "apdex_calculator/fixed"
      config:
        calculator:
          type: "fixed"
          apdex_status: "frustrated"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"

    # ApdexCalculator: 健康度状态计算器
    # Random
    - name: "apdex_calculator/random"
      config:
        calculator:
          type: "random"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"

    # ApdexCalculator: 健康度状态计算器
    # Standard
    - name: "apdex_calculator/standard"
      config:
        calculator:
          type: "standard"
        rules:
          - kind: ""
            metric_name: "bk_apm_duration"
            destination: "apdex_type"
            apdex_t: 20 # ms


    # AttributeFilter: 属性过滤处理器
    # AsString: 将 attributes 字段里的某些维度类型转为 string
    - name: "attribute_filter/common"
      config:
        as_string:
          keys:
            - "attributes.http.host"
        from_token:
          biz_id: "bk_biz_id"
          app_name: "bk_app_name"


    # MetricsFilter: 指标过滤处理器
    # Drop
    - name: "metrics_filter/drop"
      config:
        drop:
          # metrics: metric name
          metrics:
            - "runtime.go.mem.live_objects"
            - "none.exist.metric"

    # MetricsFilter: 指标过滤处理器
    # Replace
    - name: "metrics_filter/replace"
      config:
        replace:
          - source: "previous_metric"       # 原字段
            destination: "current_metric"   # 新字段


    # RateLimiter: 流控处理器
    # TokenBucket: 令牌桶限流
    - name: "rate_limiter/token_bucket"
      config:
        type: token_bucket
        qps: 5
        burst: 10

    # RateLimiter: 流控处理器
    # Noop: 放行所有请求
    - name: "rate_limiter/noop"
      config:
        type: noop

    # ResourceFilter: 资源过滤处理器
    # Drop
    - name: "resource_filter/drop"
      config:
        drop:
          keys:
            - "resource.service.name"
            - "resource.service.sdk"

    # ResourceFilter: 资源过滤处理器
    # Add
    - name: "resource_filter/add"
      config:
        add:
          # [{label: value1, label: value2}, ...]
          - label: "fake_new_key"
            value: "fake_new_value"

    # ResourceFilter: 资源过滤处理器
    # Replace
    - name: "resource_filter/replace"
      config:
        replace:
          # [{source: label_src, destination: label_dst}, ...]
          - source: "telemetry.sdk.version"
            destination: "telemetry.bksdk.version"

    # ResourceFilter: 资源过滤处理器
    # Assemble
    - name: "resource_filter/assemble"
      config:
        assemble:
          - destination: "bk.instance.id" # 转换后名称
            separator: ":"
            keys:
              - "resource.telemetry.sdk.language"
              - "resource.service.name"
              - "resource.net.host.name"
              - "resource.net.host.ip"
              - "resource.net.host.port"
        drop:
          keys:
            - "bk.data.token"


    # Sampler: 采样处理器
    # Random
    - name: "sampler/random"
      config:
        type: "random"
        sampling_percentage: 100 # 上报全部数据


    # ServiceDiscover: 服务发现处理器
    - name: "service_discover/common"
      config:
        rules:
          - service: "my-service"
            type: "http"
            match_type: "manual"
            predicate_key: "attributes.http.method"
            kind: "SPAN_KIND_CLIENT"
            match_key: "attributes.http.url"
            match_groups:
              - source: "service"
                destination: "peer.service"
            rule:
              params:
                - name: "version"
                  operator: "eq"
                  value: "v1"
                - name: "user"
                  operator: "nq"
                  value: "mando"
              host:
                value: "https://doc.weixin.qq.com"
                operator: eq
              path:
                value: "/api/v1/users"
                operator: nq

          - service: "None"
            type: "http"
            match_type: "auto"
            predicate_key: "attributes.http.method"
            kind: "SPAN_KIND_CLIENT"
            match_key: "attributes.http.url"
            match_groups:
              - source: "peer_service"
                destination: "peer.service"
              - source: "span_name"
                destination: "span_name"
            rule:
              regex: "https://(?P<peer_service>[^/]+)/(?P<span_name>\\w+)/.+"


    # TokenChecker: 权限校验处理器
    # Fixed
    - name: "token_checker/fixed"
      config:
        type: "fixed"
        fixed_token: "token1"
        resource_key: "bk.data.token"
        traces_dataid: 1000   # default: 0
        metrics_dataid: 1001  # default: 0
        logs_dataid: 1002     # default: 0
        biz_id: 2
        app_name: "apm-test"

    # TokenChecker: 权限校验处理器
    # Random
    - name: "token_checker/random"
      config:
        type: "random"
        resource_key: "bk.data.token"
        max: 2000 # default: max.int32

    # TokenChecker: 权限校验处理器
    # Aes256
    - name: "token_checker/aes256"
      config:
        type: "aes256"
        resource_key: "bk.data.token"
        salt: "bk" # 加盐解密标识
        decoded_iv: "bkbkbkbkbkbkbkbk"
        decoded_key: "81be7fc6-5476-4934-9417-6d4d593728db"

    # Pprof converter: pprof 协议转换器
    - name: "pprof_translator/common"
      config:
        type: "spy"

    # TracesDeriver: Traces 派生处理器
    # Duration
    - name: "traces_deriver/duration"
      config:
        operations:
          - type: "duration"
            metric_name: "bk_apm_duration"
            rules:
              - kind: "SPAN_KIND_SERVER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
              - kind: "SPAN_KIND_SERVER"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.http.server_name"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.http.method"
                  - "attributes.http.scheme"
                  - "attributes.http.flavor"
                  - "attributes.http.status_code"
              - kind: "SPAN_KIND_SERVER"
                predicate_key: "attributes.rpc.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.rpc.method"
                  - "attributes.rpc.service"
                  - "attributes.rpc.system"
                  - "attributes.rpc.grpc.status_code"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.http.method"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.http.method"
                  - "attributes.http.status_code"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.rpc.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.rpc.method"
                  - "attributes.rpc.service"
                  - "attributes.rpc.system"
                  - "attributes.rpc.grpc.status_code"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
              - kind: "SPAN_KIND_CLIENT"
                predicate_key: "attributes.db.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.db.name"
                  - "attributes.db.operation"
                  - "attributes.db.system"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
              - kind: "SPAN_KIND_PRODUCER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
              - kind: "SPAN_KIND_PRODUCER"
                predicate_key: "attributes.messaging.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.messaging.system"
                  - "attributes.messaging.destination"
                  - "attributes.messaging.destination_kind"
                  - "attributes.net.peer.name"
                  - "attributes.net.peer.ip"
                  - "attributes.net.peer.port"
                  - "attributes.celery.action"
                  - "attributes.celery.task_name"
              - kind: "SPAN_KIND_CONSUMER"
                predicate_key: ""
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
              - kind: "SPAN_KIND_CONSUMER"
                predicate_key: "attributes.messaging.system"
                dimensions:
                  - "resource.bk.instance.id"
                  - "span_name"
                  - "kind"
                  - "status.code"
                  - "resource.service.name"
                  - "resource.service.version"
                  - "resource.telemetry.sdk.name"
                  - "resource.telemetry.sdk.version"
                  - "resource.telemetry.sdk.language"
                  - "attributes.peer.service"
                  - "attributes.net.host.name"
                  - "attributes.net.host.ip"
                  - "attributes.net.host.port"
                  - "attributes.messaging.system"
                  - "attributes.celery.state"
                  - "attributes.celery.action"



  # =============================== Pipeline =================================
  # name: pipeline 名称作为标识
  # type: pipeline 处理类型，可选项：traces/metrics/logs 以及派生项: traces.derived/metrics.derived/logs.derived
  # processors: 上述定义的处理器名称
  pipeline:
    - name: "traces_pipeline/common"
      type: "traces"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"
        - "resource_filter/assemble"
        - "attribute_filter/common"
        - "service_discover/common"
        - "traces_deriver/duration"

    - name: "pushgateway_pipeline/common"
      type: "pushgateway"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

    - name: "metrics_pipeline/common"
      type: "metrics"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"
        - "metrics_filter/drop"
        - "resource_filter/add"
        - "apdex_calculator/fixed"

    - name: "metrics_pipeline/derived"
      type: "metrics.derived"
      processors:
        - "apdex_calculator/standard"

    - name: "profiles_pipeline/common"
      type: "profiles"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"
        - "pprof_translator/common"

    - name: "fta_pipeline/common"
      type: "fta"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

    - name: "tars_pipeline/common"
      type: "tars"
      processors:
        - "token_checker/aes256"
        - "rate_limiter/token_bucket"

  # =============================== Exporter =================================
  exporter:
    queue:
      batch_size: 1  # default: 1
      flush_interval: 10s
