// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/cluster (interfaces: Cluster)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	http "net/http"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	cluster "github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/cluster"
)

// MockCluster is a mock of Cluster interface.
type MockCluster struct {
	ctrl     *gomock.Controller
	recorder *MockClusterMockRecorder
}

// MockClusterMockRecorder is the mock recorder for MockCluster.
type MockClusterMockRecorder struct {
	mock *MockCluster
}

// NewMockCluster creates a new mock instance.
func NewMockCluster(ctrl *gomock.Controller) *MockCluster {
	mock := &MockCluster{ctrl: ctrl}
	mock.recorder = &MockClusterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCluster) EXPECT() *MockClusterMockRecorder {
	return m.recorder
}

// CreateDatabase mocks base method.
func (m *MockCluster) CreateDatabase(arg0 uint64, arg1 *cluster.QueryParams, arg2 http.Header) (*cluster.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDatabase", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cluster.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDatabase indicates an expected call of CreateDatabase.
func (mr *MockClusterMockRecorder) CreateDatabase(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDatabase", reflect.TypeOf((*MockCluster)(nil).CreateDatabase), arg0, arg1, arg2)
}

// GetInfluxVersion mocks base method.
func (m *MockCluster) GetInfluxVersion() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInfluxVersion")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetInfluxVersion indicates an expected call of GetInfluxVersion.
func (mr *MockClusterMockRecorder) GetInfluxVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInfluxVersion", reflect.TypeOf((*MockCluster)(nil).GetInfluxVersion))
}

// GetName mocks base method.
func (m *MockCluster) GetName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetName")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetName indicates an expected call of GetName.
func (mr *MockClusterMockRecorder) GetName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetName", reflect.TypeOf((*MockCluster)(nil).GetName))
}

// Query mocks base method.
func (m *MockCluster) Query(arg0 uint64, arg1 *cluster.QueryParams, arg2 http.Header) (*cluster.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Query", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cluster.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockClusterMockRecorder) Query(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockCluster)(nil).Query), arg0, arg1, arg2)
}

// QueryInfo mocks base method.
func (m *MockCluster) QueryInfo(arg0 uint64, arg1 *cluster.QueryParams, arg2 http.Header) (*cluster.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cluster.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryInfo indicates an expected call of QueryInfo.
func (mr *MockClusterMockRecorder) QueryInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryInfo", reflect.TypeOf((*MockCluster)(nil).QueryInfo), arg0, arg1, arg2)
}

// RawQuery mocks base method.
func (m *MockCluster) RawQuery(arg0 uint64, arg1 *http.Request, arg2 []string) (*http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RawQuery", arg0, arg1, arg2)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RawQuery indicates an expected call of RawQuery.
func (mr *MockClusterMockRecorder) RawQuery(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawQuery", reflect.TypeOf((*MockCluster)(nil).RawQuery), arg0, arg1, arg2)
}

// Reset mocks base method.
func (m *MockCluster) Reset(arg0 string, arg1, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reset", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reset indicates an expected call of Reset.
func (mr *MockClusterMockRecorder) Reset(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockCluster)(nil).Reset), arg0, arg1, arg2)
}

// String mocks base method.
func (m *MockCluster) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockClusterMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockCluster)(nil).String))
}

// Wait mocks base method.
func (m *MockCluster) Wait() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Wait")
}

// Wait indicates an expected call of Wait.
func (mr *MockClusterMockRecorder) Wait() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Wait", reflect.TypeOf((*MockCluster)(nil).Wait))
}

// Write mocks base method.
func (m *MockCluster) Write(arg0 uint64, arg1 *cluster.WriteParams, arg2 http.Header) (*cluster.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cluster.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockClusterMockRecorder) Write(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockCluster)(nil).Write), arg0, arg1, arg2)
}
