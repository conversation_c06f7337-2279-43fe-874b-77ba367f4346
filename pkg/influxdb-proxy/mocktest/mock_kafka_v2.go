// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/backend/influxdb (interfaces: StorageBackup)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	influxdb "github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/backend/influxdb"
)

// MockStorageBackup is a mock of StorageBackup interface.
type MockStorageBackup struct {
	ctrl     *gomock.Controller
	recorder *MockStorageBackupMockRecorder
}

// MockStorageBackupMockRecorder is the mock recorder for MockStorageBackup.
type MockStorageBackupMockRecorder struct {
	mock *MockStorageBackup
}

// NewMockStorageBackup creates a new mock instance.
func NewMockStorageBackup(ctrl *gomock.Controller) *MockStorageBackup {
	mock := &MockStorageBackup{ctrl: ctrl}
	mock.recorder = &MockStorageBackupMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStorageBackup) EXPECT() *MockStorageBackupMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockStorageBackup) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockStorageBackupMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockStorageBackup)(nil).Close))
}

// GetOffsetSize mocks base method.
func (m *MockStorageBackup) GetOffsetSize() (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOffsetSize")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffsetSize indicates an expected call of GetOffsetSize.
func (mr *MockStorageBackupMockRecorder) GetOffsetSize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffsetSize", reflect.TypeOf((*MockStorageBackup)(nil).GetOffsetSize))
}

// HasData mocks base method.
func (m *MockStorageBackup) HasData() (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasData")
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasData indicates an expected call of HasData.
func (mr *MockStorageBackupMockRecorder) HasData() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasData", reflect.TypeOf((*MockStorageBackup)(nil).HasData))
}

// Pull mocks base method.
func (m *MockStorageBackup) Pull(arg0 context.Context, arg1 influxdb.DataHandler) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Pull", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Pull indicates an expected call of Pull.
func (mr *MockStorageBackupMockRecorder) Pull(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Pull", reflect.TypeOf((*MockStorageBackup)(nil).Pull), arg0, arg1)
}

// Push mocks base method.
func (m *MockStorageBackup) Push(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Push", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Push indicates an expected call of Push.
func (mr *MockStorageBackupMockRecorder) Push(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Push", reflect.TypeOf((*MockStorageBackup)(nil).Push), arg0)
}

// Topic mocks base method.
func (m *MockStorageBackup) Topic() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Topic")
	ret0, _ := ret[0].(string)
	return ret0
}

// Topic indicates an expected call of Topic.
func (mr *MockStorageBackupMockRecorder) Topic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Topic", reflect.TypeOf((*MockStorageBackup)(nil).Topic))
}
