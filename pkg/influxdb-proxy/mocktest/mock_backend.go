// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/backend (interfaces: Backend)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	http "net/http"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"

	backend "github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/backend"
)

// ProxyBackend is a mock of Backend interface.
type ProxyBackend struct {
	ctrl     *gomock.Controller
	recorder *ProxyBackendMockRecorder
}

// ProxyBackendMockRecorder is the mock recorder for ProxyBackend.
type ProxyBackendMockRecorder struct {
	mock *ProxyBackend
}

// NewProxyBackend creates a new mock instance.
func NewProxyBackend(ctrl *gomock.Controller) *ProxyBackend {
	mock := &ProxyBackend{ctrl: ctrl}
	mock.recorder = &ProxyBackendMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *ProxyBackend) EXPECT() *ProxyBackendMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *ProxyBackend) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *ProxyBackendMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*ProxyBackend)(nil).Close))
}

// CreateDatabase mocks base method.
func (m *ProxyBackend) CreateDatabase(arg0 uint64, arg1 *backend.QueryParams, arg2 http.Header) (*backend.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDatabase", arg0, arg1, arg2)
	ret0, _ := ret[0].(*backend.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDatabase indicates an expected call of CreateDatabase.
func (mr *ProxyBackendMockRecorder) CreateDatabase(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDatabase", reflect.TypeOf((*ProxyBackend)(nil).CreateDatabase), arg0, arg1, arg2)
}

// Disabled mocks base method.
func (m *ProxyBackend) Disabled() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Disabled")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Disabled indicates an expected call of Disabled.
func (mr *ProxyBackendMockRecorder) Disabled() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Disabled", reflect.TypeOf((*ProxyBackend)(nil).Disabled))
}

// GetVersion mocks base method.
func (m *ProxyBackend) GetVersion() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVersion")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetVersion indicates an expected call of GetVersion.
func (mr *ProxyBackendMockRecorder) GetVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVersion", reflect.TypeOf((*ProxyBackend)(nil).GetVersion))
}

// Name mocks base method.
func (m *ProxyBackend) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *ProxyBackendMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*ProxyBackend)(nil).Name))
}

// Ping mocks base method.
func (m *ProxyBackend) Ping(arg0 time.Duration) (time.Duration, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", arg0)
	ret0, _ := ret[0].(time.Duration)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Ping indicates an expected call of Ping.
func (mr *ProxyBackendMockRecorder) Ping(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*ProxyBackend)(nil).Ping), arg0)
}

// Query mocks base method.
func (m *ProxyBackend) Query(arg0 uint64, arg1 *backend.QueryParams, arg2 http.Header) (*backend.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Query", arg0, arg1, arg2)
	ret0, _ := ret[0].(*backend.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *ProxyBackendMockRecorder) Query(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*ProxyBackend)(nil).Query), arg0, arg1, arg2)
}

// RawQuery mocks base method.
func (m *ProxyBackend) RawQuery(arg0 uint64, arg1 *http.Request) (*http.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RawQuery", arg0, arg1)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RawQuery indicates an expected call of RawQuery.
func (mr *ProxyBackendMockRecorder) RawQuery(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawQuery", reflect.TypeOf((*ProxyBackend)(nil).RawQuery), arg0, arg1)
}

// Readable mocks base method.
func (m *ProxyBackend) Readable() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Readable")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Readable indicates an expected call of Readable.
func (mr *ProxyBackendMockRecorder) Readable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Readable", reflect.TypeOf((*ProxyBackend)(nil).Readable))
}

// Reset mocks base method.
func (m *ProxyBackend) Reset(arg0 *backend.BasicConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reset", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reset indicates an expected call of Reset.
func (mr *ProxyBackendMockRecorder) Reset(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*ProxyBackend)(nil).Reset), arg0)
}

// String mocks base method.
func (m *ProxyBackend) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *ProxyBackendMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*ProxyBackend)(nil).String))
}

// Wait mocks base method.
func (m *ProxyBackend) Wait() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Wait")
}

// Wait indicates an expected call of Wait.
func (mr *ProxyBackendMockRecorder) Wait() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Wait", reflect.TypeOf((*ProxyBackend)(nil).Wait))
}

// Write mocks base method.
func (m *ProxyBackend) Write(arg0 uint64, arg1 *backend.WriteParams, arg2 backend.CopyReader, arg3 http.Header) (*backend.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*backend.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *ProxyBackendMockRecorder) Write(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*ProxyBackend)(nil).Write), arg0, arg1, arg2, arg3)
}
