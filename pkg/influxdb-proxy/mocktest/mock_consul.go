// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/consul/base (interfaces: ConsulClient,KV,Plan,Agent,Session)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	log "log"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "github.com/hashicorp/consul/api"
)

// MockConsulClient is a mock of ConsulClient interface.
type MockConsulClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsulClientMockRecorder
}

// MockConsulClientMockRecorder is the mock recorder for MockConsulClient.
type MockConsulClientMockRecorder struct {
	mock *MockConsulClient
}

// NewMockConsulClient creates a new mock instance.
func NewMockConsulClient(ctrl *gomock.Controller) *MockConsulClient {
	mock := &MockConsulClient{ctrl: ctrl}
	mock.recorder = &MockConsulClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsulClient) EXPECT() *MockConsulClientMockRecorder {
	return m.recorder
}

// Acquire mocks base method.
func (m *MockConsulClient) Acquire(arg0, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Acquire", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Acquire indicates an expected call of Acquire.
func (mr *MockConsulClientMockRecorder) Acquire(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Acquire", reflect.TypeOf((*MockConsulClient)(nil).Acquire), arg0, arg1)
}

// CAS mocks base method.
func (m *MockConsulClient) CAS(arg0 string, arg1, arg2 []byte) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CAS", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CAS indicates an expected call of CAS.
func (mr *MockConsulClientMockRecorder) CAS(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CAS", reflect.TypeOf((*MockConsulClient)(nil).CAS), arg0, arg1, arg2)
}

// CheckDeregister mocks base method.
func (m *MockConsulClient) CheckDeregister(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDeregister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckDeregister indicates an expected call of CheckDeregister.
func (mr *MockConsulClientMockRecorder) CheckDeregister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDeregister", reflect.TypeOf((*MockConsulClient)(nil).CheckDeregister), arg0)
}

// CheckFail mocks base method.
func (m *MockConsulClient) CheckFail(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFail", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckFail indicates an expected call of CheckFail.
func (mr *MockConsulClientMockRecorder) CheckFail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFail", reflect.TypeOf((*MockConsulClient)(nil).CheckFail), arg0, arg1)
}

// CheckPass mocks base method.
func (m *MockConsulClient) CheckPass(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPass", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckPass indicates an expected call of CheckPass.
func (mr *MockConsulClientMockRecorder) CheckPass(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPass", reflect.TypeOf((*MockConsulClient)(nil).CheckPass), arg0, arg1)
}

// CheckRegister mocks base method.
func (m *MockConsulClient) CheckRegister(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRegister", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckRegister indicates an expected call of CheckRegister.
func (mr *MockConsulClientMockRecorder) CheckRegister(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRegister", reflect.TypeOf((*MockConsulClient)(nil).CheckRegister), arg0, arg1, arg2)
}

// CheckStatus mocks base method.
func (m *MockConsulClient) CheckStatus(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStatus", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStatus indicates an expected call of CheckStatus.
func (mr *MockConsulClientMockRecorder) CheckStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStatus", reflect.TypeOf((*MockConsulClient)(nil).CheckStatus), arg0)
}

// Close mocks base method.
func (m *MockConsulClient) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockConsulClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockConsulClient)(nil).Close))
}

// Delete mocks base method.
func (m *MockConsulClient) Delete(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockConsulClientMockRecorder) Delete(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockConsulClient)(nil).Delete), arg0)
}

// Get mocks base method.
func (m *MockConsulClient) Get(arg0 string) (*api.KVPair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0)
	ret0, _ := ret[0].(*api.KVPair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockConsulClientMockRecorder) Get(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockConsulClient)(nil).Get), arg0)
}

// GetChild mocks base method.
func (m *MockConsulClient) GetChild(arg0, arg1 string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChild", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChild indicates an expected call of GetChild.
func (mr *MockConsulClientMockRecorder) GetChild(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChild", reflect.TypeOf((*MockConsulClient)(nil).GetChild), arg0, arg1)
}

// GetPrefix mocks base method.
func (m *MockConsulClient) GetPrefix(arg0, arg1 string) (api.KVPairs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrefix", arg0, arg1)
	ret0, _ := ret[0].(api.KVPairs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrefix indicates an expected call of GetPrefix.
func (mr *MockConsulClientMockRecorder) GetPrefix(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrefix", reflect.TypeOf((*MockConsulClient)(nil).GetPrefix), arg0, arg1)
}

// NewSessionID mocks base method.
func (m *MockConsulClient) NewSessionID(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSessionID", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewSessionID indicates an expected call of NewSessionID.
func (mr *MockConsulClientMockRecorder) NewSessionID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSessionID", reflect.TypeOf((*MockConsulClient)(nil).NewSessionID), arg0)
}

// Put mocks base method.
func (m *MockConsulClient) Put(arg0 string, arg1 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Put", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Put indicates an expected call of Put.
func (mr *MockConsulClientMockRecorder) Put(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockConsulClient)(nil).Put), arg0, arg1)
}

// Release mocks base method.
func (m *MockConsulClient) Release(arg0, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Release indicates an expected call of Release.
func (mr *MockConsulClientMockRecorder) Release(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockConsulClient)(nil).Release), arg0, arg1)
}

// RenewSession mocks base method.
func (m *MockConsulClient) RenewSession(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenewSession", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RenewSession indicates an expected call of RenewSession.
func (mr *MockConsulClientMockRecorder) RenewSession(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenewSession", reflect.TypeOf((*MockConsulClient)(nil).RenewSession), arg0)
}

// ServiceAwake mocks base method.
func (m *MockConsulClient) ServiceAwake(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceAwake", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceAwake indicates an expected call of ServiceAwake.
func (mr *MockConsulClientMockRecorder) ServiceAwake(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceAwake", reflect.TypeOf((*MockConsulClient)(nil).ServiceAwake), arg0)
}

// ServiceDeregister mocks base method.
func (m *MockConsulClient) ServiceDeregister(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceDeregister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceDeregister indicates an expected call of ServiceDeregister.
func (mr *MockConsulClientMockRecorder) ServiceDeregister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceDeregister", reflect.TypeOf((*MockConsulClient)(nil).ServiceDeregister), arg0)
}

// ServiceRegister mocks base method.
func (m *MockConsulClient) ServiceRegister(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceRegister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceRegister indicates an expected call of ServiceRegister.
func (mr *MockConsulClientMockRecorder) ServiceRegister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceRegister", reflect.TypeOf((*MockConsulClient)(nil).ServiceRegister), arg0)
}

// StopWatch mocks base method.
func (m *MockConsulClient) StopWatch(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopWatch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopWatch indicates an expected call of StopWatch.
func (mr *MockConsulClientMockRecorder) StopWatch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopWatch", reflect.TypeOf((*MockConsulClient)(nil).StopWatch), arg0, arg1)
}

// Watch mocks base method.
func (m *MockConsulClient) Watch(arg0, arg1 string) (<-chan interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Watch", arg0, arg1)
	ret0, _ := ret[0].(<-chan interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Watch indicates an expected call of Watch.
func (mr *MockConsulClientMockRecorder) Watch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockConsulClient)(nil).Watch), arg0, arg1)
}

// MockAbstractKV is a mock of KV interface.
type MockAbstractKV struct {
	ctrl     *gomock.Controller
	recorder *MockAbstractKVMockRecorder
}

// MockAbstractKVMockRecorder is the mock recorder for MockAbstractKV.
type MockAbstractKVMockRecorder struct {
	mock *MockAbstractKV
}

// NewMockAbstractKV creates a new mock instance.
func NewMockAbstractKV(ctrl *gomock.Controller) *MockAbstractKV {
	mock := &MockAbstractKV{ctrl: ctrl}
	mock.recorder = &MockAbstractKVMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAbstractKV) EXPECT() *MockAbstractKVMockRecorder {
	return m.recorder
}

// Acquire mocks base method.
func (m *MockAbstractKV) Acquire(arg0 *api.KVPair, arg1 *api.WriteOptions) (bool, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Acquire", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Acquire indicates an expected call of Acquire.
func (mr *MockAbstractKVMockRecorder) Acquire(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Acquire", reflect.TypeOf((*MockAbstractKV)(nil).Acquire), arg0, arg1)
}

// CAS mocks base method.
func (m *MockAbstractKV) CAS(arg0 *api.KVPair, arg1 *api.WriteOptions) (bool, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CAS", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CAS indicates an expected call of CAS.
func (mr *MockAbstractKVMockRecorder) CAS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CAS", reflect.TypeOf((*MockAbstractKV)(nil).CAS), arg0, arg1)
}

// Delete mocks base method.
func (m *MockAbstractKV) Delete(arg0 string, arg1 *api.WriteOptions) (*api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, arg1)
	ret0, _ := ret[0].(*api.WriteMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockAbstractKVMockRecorder) Delete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAbstractKV)(nil).Delete), arg0, arg1)
}

// DeleteCAS mocks base method.
func (m *MockAbstractKV) DeleteCAS(arg0 *api.KVPair, arg1 *api.WriteOptions) (bool, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCAS", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// DeleteCAS indicates an expected call of DeleteCAS.
func (mr *MockAbstractKVMockRecorder) DeleteCAS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCAS", reflect.TypeOf((*MockAbstractKV)(nil).DeleteCAS), arg0, arg1)
}

// DeleteTree mocks base method.
func (m *MockAbstractKV) DeleteTree(arg0 string, arg1 *api.WriteOptions) (*api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTree", arg0, arg1)
	ret0, _ := ret[0].(*api.WriteMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTree indicates an expected call of DeleteTree.
func (mr *MockAbstractKVMockRecorder) DeleteTree(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTree", reflect.TypeOf((*MockAbstractKV)(nil).DeleteTree), arg0, arg1)
}

// Get mocks base method.
func (m *MockAbstractKV) Get(arg0 string, arg1 *api.QueryOptions) (*api.KVPair, *api.QueryMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*api.KVPair)
	ret1, _ := ret[1].(*api.QueryMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Get indicates an expected call of Get.
func (mr *MockAbstractKVMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAbstractKV)(nil).Get), arg0, arg1)
}

// Keys mocks base method.
func (m *MockAbstractKV) Keys(arg0, arg1 string, arg2 *api.QueryOptions) ([]string, *api.QueryMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Keys", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*api.QueryMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Keys indicates an expected call of Keys.
func (mr *MockAbstractKVMockRecorder) Keys(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Keys", reflect.TypeOf((*MockAbstractKV)(nil).Keys), arg0, arg1, arg2)
}

// List mocks base method.
func (m *MockAbstractKV) List(arg0 string, arg1 *api.QueryOptions) (api.KVPairs, *api.QueryMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, arg1)
	ret0, _ := ret[0].(api.KVPairs)
	ret1, _ := ret[1].(*api.QueryMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// List indicates an expected call of List.
func (mr *MockAbstractKVMockRecorder) List(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockAbstractKV)(nil).List), arg0, arg1)
}

// Put mocks base method.
func (m *MockAbstractKV) Put(arg0 *api.KVPair, arg1 *api.WriteOptions) (*api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Put", arg0, arg1)
	ret0, _ := ret[0].(*api.WriteMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Put indicates an expected call of Put.
func (mr *MockAbstractKVMockRecorder) Put(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockAbstractKV)(nil).Put), arg0, arg1)
}

// Release mocks base method.
func (m *MockAbstractKV) Release(arg0 *api.KVPair, arg1 *api.WriteOptions) (bool, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Release indicates an expected call of Release.
func (mr *MockAbstractKVMockRecorder) Release(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockAbstractKV)(nil).Release), arg0, arg1)
}

// Txn mocks base method.
func (m *MockAbstractKV) Txn(arg0 api.KVTxnOps, arg1 *api.QueryOptions) (bool, *api.KVTxnResponse, *api.QueryMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Txn", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*api.KVTxnResponse)
	ret2, _ := ret[2].(*api.QueryMeta)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// Txn indicates an expected call of Txn.
func (mr *MockAbstractKVMockRecorder) Txn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Txn", reflect.TypeOf((*MockAbstractKV)(nil).Txn), arg0, arg1)
}

// MockAbstractPlan is a mock of Plan interface.
type MockAbstractPlan struct {
	ctrl     *gomock.Controller
	recorder *MockAbstractPlanMockRecorder
}

// MockAbstractPlanMockRecorder is the mock recorder for MockAbstractPlan.
type MockAbstractPlanMockRecorder struct {
	mock *MockAbstractPlan
}

// NewMockAbstractPlan creates a new mock instance.
func NewMockAbstractPlan(ctrl *gomock.Controller) *MockAbstractPlan {
	mock := &MockAbstractPlan{ctrl: ctrl}
	mock.recorder = &MockAbstractPlanMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAbstractPlan) EXPECT() *MockAbstractPlanMockRecorder {
	return m.recorder
}

// IsStopped mocks base method.
func (m *MockAbstractPlan) IsStopped() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsStopped")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsStopped indicates an expected call of IsStopped.
func (mr *MockAbstractPlanMockRecorder) IsStopped() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsStopped", reflect.TypeOf((*MockAbstractPlan)(nil).IsStopped))
}

// Run mocks base method.
func (m *MockAbstractPlan) Run(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Run", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Run indicates an expected call of Run.
func (mr *MockAbstractPlanMockRecorder) Run(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockAbstractPlan)(nil).Run), arg0)
}

// RunWithClientAndLogger mocks base method.
func (m *MockAbstractPlan) RunWithClientAndLogger(arg0 *api.Client, arg1 *log.Logger) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunWithClientAndLogger", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunWithClientAndLogger indicates an expected call of RunWithClientAndLogger.
func (mr *MockAbstractPlanMockRecorder) RunWithClientAndLogger(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunWithClientAndLogger", reflect.TypeOf((*MockAbstractPlan)(nil).RunWithClientAndLogger), arg0, arg1)
}

// RunWithConfig mocks base method.
func (m *MockAbstractPlan) RunWithConfig(arg0 string, arg1 *api.Config) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunWithConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunWithConfig indicates an expected call of RunWithConfig.
func (mr *MockAbstractPlanMockRecorder) RunWithConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunWithConfig", reflect.TypeOf((*MockAbstractPlan)(nil).RunWithConfig), arg0, arg1)
}

// Stop mocks base method.
func (m *MockAbstractPlan) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockAbstractPlanMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockAbstractPlan)(nil).Stop))
}

// MockAbstractAgent is a mock of Agent interface.
type MockAbstractAgent struct {
	ctrl     *gomock.Controller
	recorder *MockAbstractAgentMockRecorder
}

// MockAbstractAgentMockRecorder is the mock recorder for MockAbstractAgent.
type MockAbstractAgentMockRecorder struct {
	mock *MockAbstractAgent
}

// NewMockAbstractAgent creates a new mock instance.
func NewMockAbstractAgent(ctrl *gomock.Controller) *MockAbstractAgent {
	mock := &MockAbstractAgent{ctrl: ctrl}
	mock.recorder = &MockAbstractAgentMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAbstractAgent) EXPECT() *MockAbstractAgentMockRecorder {
	return m.recorder
}

// AgentHealthServiceByID mocks base method.
func (m *MockAbstractAgent) AgentHealthServiceByID(arg0 string) (string, *api.AgentServiceChecksInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AgentHealthServiceByID", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*api.AgentServiceChecksInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// AgentHealthServiceByID indicates an expected call of AgentHealthServiceByID.
func (mr *MockAbstractAgentMockRecorder) AgentHealthServiceByID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AgentHealthServiceByID", reflect.TypeOf((*MockAbstractAgent)(nil).AgentHealthServiceByID), arg0)
}

// CheckDeregister mocks base method.
func (m *MockAbstractAgent) CheckDeregister(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDeregister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckDeregister indicates an expected call of CheckDeregister.
func (mr *MockAbstractAgentMockRecorder) CheckDeregister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDeregister", reflect.TypeOf((*MockAbstractAgent)(nil).CheckDeregister), arg0)
}

// CheckRegister mocks base method.
func (m *MockAbstractAgent) CheckRegister(arg0 *api.AgentCheckRegistration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRegister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckRegister indicates an expected call of CheckRegister.
func (mr *MockAbstractAgentMockRecorder) CheckRegister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRegister", reflect.TypeOf((*MockAbstractAgent)(nil).CheckRegister), arg0)
}

// ChecksWithFilter mocks base method.
func (m *MockAbstractAgent) ChecksWithFilter(arg0 string) (map[string]*api.AgentCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChecksWithFilter", arg0)
	ret0, _ := ret[0].(map[string]*api.AgentCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChecksWithFilter indicates an expected call of ChecksWithFilter.
func (mr *MockAbstractAgentMockRecorder) ChecksWithFilter(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChecksWithFilter", reflect.TypeOf((*MockAbstractAgent)(nil).ChecksWithFilter), arg0)
}

// FailTTL mocks base method.
func (m *MockAbstractAgent) FailTTL(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FailTTL", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// FailTTL indicates an expected call of FailTTL.
func (mr *MockAbstractAgentMockRecorder) FailTTL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FailTTL", reflect.TypeOf((*MockAbstractAgent)(nil).FailTTL), arg0, arg1)
}

// PassTTL mocks base method.
func (m *MockAbstractAgent) PassTTL(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PassTTL", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PassTTL indicates an expected call of PassTTL.
func (mr *MockAbstractAgentMockRecorder) PassTTL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassTTL", reflect.TypeOf((*MockAbstractAgent)(nil).PassTTL), arg0, arg1)
}

// ServiceDeregister mocks base method.
func (m *MockAbstractAgent) ServiceDeregister(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceDeregister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceDeregister indicates an expected call of ServiceDeregister.
func (mr *MockAbstractAgentMockRecorder) ServiceDeregister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceDeregister", reflect.TypeOf((*MockAbstractAgent)(nil).ServiceDeregister), arg0)
}

// ServiceRegister mocks base method.
func (m *MockAbstractAgent) ServiceRegister(arg0 *api.AgentServiceRegistration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ServiceRegister", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ServiceRegister indicates an expected call of ServiceRegister.
func (mr *MockAbstractAgentMockRecorder) ServiceRegister(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ServiceRegister", reflect.TypeOf((*MockAbstractAgent)(nil).ServiceRegister), arg0)
}

// MockSession is a mock of Session interface.
type MockSession struct {
	ctrl     *gomock.Controller
	recorder *MockSessionMockRecorder
}

// MockSessionMockRecorder is the mock recorder for MockSession.
type MockSessionMockRecorder struct {
	mock *MockSession
}

// NewMockSession creates a new mock instance.
func NewMockSession(ctrl *gomock.Controller) *MockSession {
	mock := &MockSession{ctrl: ctrl}
	mock.recorder = &MockSessionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSession) EXPECT() *MockSessionMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockSession) Create(arg0 *api.SessionEntry, arg1 *api.WriteOptions) (string, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Create indicates an expected call of Create.
func (mr *MockSessionMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockSession)(nil).Create), arg0, arg1)
}

// Destroy mocks base method.
func (m *MockSession) Destroy(arg0 string, arg1 *api.WriteOptions) (*api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Destroy", arg0, arg1)
	ret0, _ := ret[0].(*api.WriteMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Destroy indicates an expected call of Destroy.
func (mr *MockSessionMockRecorder) Destroy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Destroy", reflect.TypeOf((*MockSession)(nil).Destroy), arg0, arg1)
}

// Renew mocks base method.
func (m *MockSession) Renew(arg0 string, arg1 *api.WriteOptions) (*api.SessionEntry, *api.WriteMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Renew", arg0, arg1)
	ret0, _ := ret[0].(*api.SessionEntry)
	ret1, _ := ret[1].(*api.WriteMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Renew indicates an expected call of Renew.
func (mr *MockSessionMockRecorder) Renew(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Renew", reflect.TypeOf((*MockSession)(nil).Renew), arg0, arg1)
}
