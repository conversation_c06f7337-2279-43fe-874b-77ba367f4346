// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/Shopify/sarama (interfaces: Client,ConsumerGroup,ConsumerGroupSession,ConsumerGroupClaim,SyncProducer,OffsetManager,PartitionOffsetManager,ClusterAdmin)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	context "context"
	reflect "reflect"

	sarama "github.com/Shopify/sarama"
	gomock "github.com/golang/mock/gomock"
)

// KafkaClient is a mock of Client interface.
type KafkaClient struct {
	ctrl     *gomock.Controller
	recorder *KafkaClientMockRecorder
}

// KafkaClientMockRecorder is the mock recorder for KafkaClient.
type KafkaClientMockRecorder struct {
	mock *KafkaClient
}

// NewKafkaClient creates a new mock instance.
func NewKafkaClient(ctrl *gomock.Controller) *KafkaClient {
	mock := &KafkaClient{ctrl: ctrl}
	mock.recorder = &KafkaClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *KafkaClient) EXPECT() *KafkaClientMockRecorder {
	return m.recorder
}

// Brokers mocks base method.
func (m *KafkaClient) Brokers() []*sarama.Broker {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Brokers")
	ret0, _ := ret[0].([]*sarama.Broker)
	return ret0
}

// Brokers indicates an expected call of Brokers.
func (mr *KafkaClientMockRecorder) Brokers() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Brokers", reflect.TypeOf((*KafkaClient)(nil).Brokers))
}

// Close mocks base method.
func (m *KafkaClient) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *KafkaClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*KafkaClient)(nil).Close))
}

// Closed mocks base method.
func (m *KafkaClient) Closed() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Closed")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Closed indicates an expected call of Closed.
func (mr *KafkaClientMockRecorder) Closed() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Closed", reflect.TypeOf((*KafkaClient)(nil).Closed))
}

// Config mocks base method.
func (m *KafkaClient) Config() *sarama.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Config")
	ret0, _ := ret[0].(*sarama.Config)
	return ret0
}

// Config indicates an expected call of Config.
func (mr *KafkaClientMockRecorder) Config() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Config", reflect.TypeOf((*KafkaClient)(nil).Config))
}

// Controller mocks base method.
func (m *KafkaClient) Controller() (*sarama.Broker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Controller")
	ret0, _ := ret[0].(*sarama.Broker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Controller indicates an expected call of Controller.
func (mr *KafkaClientMockRecorder) Controller() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Controller", reflect.TypeOf((*KafkaClient)(nil).Controller))
}

// Coordinator mocks base method.
func (m *KafkaClient) Coordinator(arg0 string) (*sarama.Broker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Coordinator", arg0)
	ret0, _ := ret[0].(*sarama.Broker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Coordinator indicates an expected call of Coordinator.
func (mr *KafkaClientMockRecorder) Coordinator(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Coordinator", reflect.TypeOf((*KafkaClient)(nil).Coordinator), arg0)
}

// GetOffset mocks base method.
func (m *KafkaClient) GetOffset(arg0 string, arg1 int32, arg2 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOffset", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffset indicates an expected call of GetOffset.
func (mr *KafkaClientMockRecorder) GetOffset(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffset", reflect.TypeOf((*KafkaClient)(nil).GetOffset), arg0, arg1, arg2)
}

// InSyncReplicas mocks base method.
func (m *KafkaClient) InSyncReplicas(arg0 string, arg1 int32) ([]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InSyncReplicas", arg0, arg1)
	ret0, _ := ret[0].([]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InSyncReplicas indicates an expected call of InSyncReplicas.
func (mr *KafkaClientMockRecorder) InSyncReplicas(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InSyncReplicas", reflect.TypeOf((*KafkaClient)(nil).InSyncReplicas), arg0, arg1)
}

// InitProducerID mocks base method.
func (m *KafkaClient) InitProducerID() (*sarama.InitProducerIDResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitProducerID")
	ret0, _ := ret[0].(*sarama.InitProducerIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitProducerID indicates an expected call of InitProducerID.
func (mr *KafkaClientMockRecorder) InitProducerID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitProducerID", reflect.TypeOf((*KafkaClient)(nil).InitProducerID))
}

// Leader mocks base method.
func (m *KafkaClient) Leader(arg0 string, arg1 int32) (*sarama.Broker, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Leader", arg0, arg1)
	ret0, _ := ret[0].(*sarama.Broker)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Leader indicates an expected call of Leader.
func (mr *KafkaClientMockRecorder) Leader(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Leader", reflect.TypeOf((*KafkaClient)(nil).Leader), arg0, arg1)
}

// OfflineReplicas mocks base method.
func (m *KafkaClient) OfflineReplicas(arg0 string, arg1 int32) ([]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfflineReplicas", arg0, arg1)
	ret0, _ := ret[0].([]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfflineReplicas indicates an expected call of OfflineReplicas.
func (mr *KafkaClientMockRecorder) OfflineReplicas(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfflineReplicas", reflect.TypeOf((*KafkaClient)(nil).OfflineReplicas), arg0, arg1)
}

// Partitions mocks base method.
func (m *KafkaClient) Partitions(arg0 string) ([]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Partitions", arg0)
	ret0, _ := ret[0].([]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Partitions indicates an expected call of Partitions.
func (mr *KafkaClientMockRecorder) Partitions(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Partitions", reflect.TypeOf((*KafkaClient)(nil).Partitions), arg0)
}

// RefreshCoordinator mocks base method.
func (m *KafkaClient) RefreshCoordinator(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshCoordinator", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshCoordinator indicates an expected call of RefreshCoordinator.
func (mr *KafkaClientMockRecorder) RefreshCoordinator(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshCoordinator", reflect.TypeOf((*KafkaClient)(nil).RefreshCoordinator), arg0)
}

// RefreshMetadata mocks base method.
func (m *KafkaClient) RefreshMetadata(arg0 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range arg0 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RefreshMetadata", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshMetadata indicates an expected call of RefreshMetadata.
func (mr *KafkaClientMockRecorder) RefreshMetadata(arg0 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshMetadata", reflect.TypeOf((*KafkaClient)(nil).RefreshMetadata), arg0...)
}

// Replicas mocks base method.
func (m *KafkaClient) Replicas(arg0 string, arg1 int32) ([]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Replicas", arg0, arg1)
	ret0, _ := ret[0].([]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Replicas indicates an expected call of Replicas.
func (mr *KafkaClientMockRecorder) Replicas(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Replicas", reflect.TypeOf((*KafkaClient)(nil).Replicas), arg0, arg1)
}

// Topics mocks base method.
func (m *KafkaClient) Topics() ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Topics")
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Topics indicates an expected call of Topics.
func (mr *KafkaClientMockRecorder) Topics() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Topics", reflect.TypeOf((*KafkaClient)(nil).Topics))
}

// WritablePartitions mocks base method.
func (m *KafkaClient) WritablePartitions(arg0 string) ([]int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WritablePartitions", arg0)
	ret0, _ := ret[0].([]int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WritablePartitions indicates an expected call of WritablePartitions.
func (mr *KafkaClientMockRecorder) WritablePartitions(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WritablePartitions", reflect.TypeOf((*KafkaClient)(nil).WritablePartitions), arg0)
}

// MockConsumerGroup is a mock of ConsumerGroup interface.
type MockConsumerGroup struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerGroupMockRecorder
}

// MockConsumerGroupMockRecorder is the mock recorder for MockConsumerGroup.
type MockConsumerGroupMockRecorder struct {
	mock *MockConsumerGroup
}

// NewMockConsumerGroup creates a new mock instance.
func NewMockConsumerGroup(ctrl *gomock.Controller) *MockConsumerGroup {
	mock := &MockConsumerGroup{ctrl: ctrl}
	mock.recorder = &MockConsumerGroupMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerGroup) EXPECT() *MockConsumerGroupMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockConsumerGroup) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockConsumerGroupMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockConsumerGroup)(nil).Close))
}

// Consume mocks base method.
func (m *MockConsumerGroup) Consume(arg0 context.Context, arg1 []string, arg2 sarama.ConsumerGroupHandler) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Consume", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Consume indicates an expected call of Consume.
func (mr *MockConsumerGroupMockRecorder) Consume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Consume", reflect.TypeOf((*MockConsumerGroup)(nil).Consume), arg0, arg1, arg2)
}

// Errors mocks base method.
func (m *MockConsumerGroup) Errors() <-chan error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Errors")
	ret0, _ := ret[0].(<-chan error)
	return ret0
}

// Errors indicates an expected call of Errors.
func (mr *MockConsumerGroupMockRecorder) Errors() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Errors", reflect.TypeOf((*MockConsumerGroup)(nil).Errors))
}

// MockConsumerGroupSession is a mock of ConsumerGroupSession interface.
type MockConsumerGroupSession struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerGroupSessionMockRecorder
}

// MockConsumerGroupSessionMockRecorder is the mock recorder for MockConsumerGroupSession.
type MockConsumerGroupSessionMockRecorder struct {
	mock *MockConsumerGroupSession
}

// NewMockConsumerGroupSession creates a new mock instance.
func NewMockConsumerGroupSession(ctrl *gomock.Controller) *MockConsumerGroupSession {
	mock := &MockConsumerGroupSession{ctrl: ctrl}
	mock.recorder = &MockConsumerGroupSessionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerGroupSession) EXPECT() *MockConsumerGroupSessionMockRecorder {
	return m.recorder
}

// Claims mocks base method.
func (m *MockConsumerGroupSession) Claims() map[string][]int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Claims")
	ret0, _ := ret[0].(map[string][]int32)
	return ret0
}

// Claims indicates an expected call of Claims.
func (mr *MockConsumerGroupSessionMockRecorder) Claims() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Claims", reflect.TypeOf((*MockConsumerGroupSession)(nil).Claims))
}

// Context mocks base method.
func (m *MockConsumerGroupSession) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockConsumerGroupSessionMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockConsumerGroupSession)(nil).Context))
}

// GenerationID mocks base method.
func (m *MockConsumerGroupSession) GenerationID() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerationID")
	ret0, _ := ret[0].(int32)
	return ret0
}

// GenerationID indicates an expected call of GenerationID.
func (mr *MockConsumerGroupSessionMockRecorder) GenerationID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerationID", reflect.TypeOf((*MockConsumerGroupSession)(nil).GenerationID))
}

// MarkMessage mocks base method.
func (m *MockConsumerGroupSession) MarkMessage(arg0 *sarama.ConsumerMessage, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkMessage", arg0, arg1)
}

// MarkMessage indicates an expected call of MarkMessage.
func (mr *MockConsumerGroupSessionMockRecorder) MarkMessage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkMessage", reflect.TypeOf((*MockConsumerGroupSession)(nil).MarkMessage), arg0, arg1)
}

// MarkOffset mocks base method.
func (m *MockConsumerGroupSession) MarkOffset(arg0 string, arg1 int32, arg2 int64, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkOffset", arg0, arg1, arg2, arg3)
}

// MarkOffset indicates an expected call of MarkOffset.
func (mr *MockConsumerGroupSessionMockRecorder) MarkOffset(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOffset", reflect.TypeOf((*MockConsumerGroupSession)(nil).MarkOffset), arg0, arg1, arg2, arg3)
}

// MemberID mocks base method.
func (m *MockConsumerGroupSession) MemberID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MemberID")
	ret0, _ := ret[0].(string)
	return ret0
}

// MemberID indicates an expected call of MemberID.
func (mr *MockConsumerGroupSessionMockRecorder) MemberID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MemberID", reflect.TypeOf((*MockConsumerGroupSession)(nil).MemberID))
}

// ResetOffset mocks base method.
func (m *MockConsumerGroupSession) ResetOffset(arg0 string, arg1 int32, arg2 int64, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ResetOffset", arg0, arg1, arg2, arg3)
}

// ResetOffset indicates an expected call of ResetOffset.
func (mr *MockConsumerGroupSessionMockRecorder) ResetOffset(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetOffset", reflect.TypeOf((*MockConsumerGroupSession)(nil).ResetOffset), arg0, arg1, arg2, arg3)
}

// MockConsumerGroupClaim is a mock of ConsumerGroupClaim interface.
type MockConsumerGroupClaim struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerGroupClaimMockRecorder
}

// MockConsumerGroupClaimMockRecorder is the mock recorder for MockConsumerGroupClaim.
type MockConsumerGroupClaimMockRecorder struct {
	mock *MockConsumerGroupClaim
}

// NewMockConsumerGroupClaim creates a new mock instance.
func NewMockConsumerGroupClaim(ctrl *gomock.Controller) *MockConsumerGroupClaim {
	mock := &MockConsumerGroupClaim{ctrl: ctrl}
	mock.recorder = &MockConsumerGroupClaimMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerGroupClaim) EXPECT() *MockConsumerGroupClaimMockRecorder {
	return m.recorder
}

// HighWaterMarkOffset mocks base method.
func (m *MockConsumerGroupClaim) HighWaterMarkOffset() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HighWaterMarkOffset")
	ret0, _ := ret[0].(int64)
	return ret0
}

// HighWaterMarkOffset indicates an expected call of HighWaterMarkOffset.
func (mr *MockConsumerGroupClaimMockRecorder) HighWaterMarkOffset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HighWaterMarkOffset", reflect.TypeOf((*MockConsumerGroupClaim)(nil).HighWaterMarkOffset))
}

// InitialOffset mocks base method.
func (m *MockConsumerGroupClaim) InitialOffset() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitialOffset")
	ret0, _ := ret[0].(int64)
	return ret0
}

// InitialOffset indicates an expected call of InitialOffset.
func (mr *MockConsumerGroupClaimMockRecorder) InitialOffset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitialOffset", reflect.TypeOf((*MockConsumerGroupClaim)(nil).InitialOffset))
}

// Messages mocks base method.
func (m *MockConsumerGroupClaim) Messages() <-chan *sarama.ConsumerMessage {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Messages")
	ret0, _ := ret[0].(<-chan *sarama.ConsumerMessage)
	return ret0
}

// Messages indicates an expected call of Messages.
func (mr *MockConsumerGroupClaimMockRecorder) Messages() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Messages", reflect.TypeOf((*MockConsumerGroupClaim)(nil).Messages))
}

// Partition mocks base method.
func (m *MockConsumerGroupClaim) Partition() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Partition")
	ret0, _ := ret[0].(int32)
	return ret0
}

// Partition indicates an expected call of Partition.
func (mr *MockConsumerGroupClaimMockRecorder) Partition() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Partition", reflect.TypeOf((*MockConsumerGroupClaim)(nil).Partition))
}

// Topic mocks base method.
func (m *MockConsumerGroupClaim) Topic() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Topic")
	ret0, _ := ret[0].(string)
	return ret0
}

// Topic indicates an expected call of Topic.
func (mr *MockConsumerGroupClaimMockRecorder) Topic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Topic", reflect.TypeOf((*MockConsumerGroupClaim)(nil).Topic))
}

// MockSyncProducer is a mock of SyncProducer interface.
type MockSyncProducer struct {
	ctrl     *gomock.Controller
	recorder *MockSyncProducerMockRecorder
}

// MockSyncProducerMockRecorder is the mock recorder for MockSyncProducer.
type MockSyncProducerMockRecorder struct {
	mock *MockSyncProducer
}

// NewMockSyncProducer creates a new mock instance.
func NewMockSyncProducer(ctrl *gomock.Controller) *MockSyncProducer {
	mock := &MockSyncProducer{ctrl: ctrl}
	mock.recorder = &MockSyncProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSyncProducer) EXPECT() *MockSyncProducerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockSyncProducer) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockSyncProducerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockSyncProducer)(nil).Close))
}

// SendMessage mocks base method.
func (m *MockSyncProducer) SendMessage(arg0 *sarama.ProducerMessage) (int32, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", arg0)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockSyncProducerMockRecorder) SendMessage(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockSyncProducer)(nil).SendMessage), arg0)
}

// SendMessages mocks base method.
func (m *MockSyncProducer) SendMessages(arg0 []*sarama.ProducerMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessages", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessages indicates an expected call of SendMessages.
func (mr *MockSyncProducerMockRecorder) SendMessages(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessages", reflect.TypeOf((*MockSyncProducer)(nil).SendMessages), arg0)
}

// MockOffsetManager is a mock of OffsetManager interface.
type MockOffsetManager struct {
	ctrl     *gomock.Controller
	recorder *MockOffsetManagerMockRecorder
}

// MockOffsetManagerMockRecorder is the mock recorder for MockOffsetManager.
type MockOffsetManagerMockRecorder struct {
	mock *MockOffsetManager
}

// NewMockOffsetManager creates a new mock instance.
func NewMockOffsetManager(ctrl *gomock.Controller) *MockOffsetManager {
	mock := &MockOffsetManager{ctrl: ctrl}
	mock.recorder = &MockOffsetManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOffsetManager) EXPECT() *MockOffsetManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockOffsetManager) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockOffsetManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockOffsetManager)(nil).Close))
}

// ManagePartition mocks base method.
func (m *MockOffsetManager) ManagePartition(arg0 string, arg1 int32) (sarama.PartitionOffsetManager, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManagePartition", arg0, arg1)
	ret0, _ := ret[0].(sarama.PartitionOffsetManager)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManagePartition indicates an expected call of ManagePartition.
func (mr *MockOffsetManagerMockRecorder) ManagePartition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManagePartition", reflect.TypeOf((*MockOffsetManager)(nil).ManagePartition), arg0, arg1)
}

// MockPartitionOffsetManager is a mock of PartitionOffsetManager interface.
type MockPartitionOffsetManager struct {
	ctrl     *gomock.Controller
	recorder *MockPartitionOffsetManagerMockRecorder
}

// MockPartitionOffsetManagerMockRecorder is the mock recorder for MockPartitionOffsetManager.
type MockPartitionOffsetManagerMockRecorder struct {
	mock *MockPartitionOffsetManager
}

// NewMockPartitionOffsetManager creates a new mock instance.
func NewMockPartitionOffsetManager(ctrl *gomock.Controller) *MockPartitionOffsetManager {
	mock := &MockPartitionOffsetManager{ctrl: ctrl}
	mock.recorder = &MockPartitionOffsetManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPartitionOffsetManager) EXPECT() *MockPartitionOffsetManagerMockRecorder {
	return m.recorder
}

// AsyncClose mocks base method.
func (m *MockPartitionOffsetManager) AsyncClose() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncClose")
}

// AsyncClose indicates an expected call of AsyncClose.
func (mr *MockPartitionOffsetManagerMockRecorder) AsyncClose() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncClose", reflect.TypeOf((*MockPartitionOffsetManager)(nil).AsyncClose))
}

// Close mocks base method.
func (m *MockPartitionOffsetManager) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockPartitionOffsetManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockPartitionOffsetManager)(nil).Close))
}

// Errors mocks base method.
func (m *MockPartitionOffsetManager) Errors() <-chan *sarama.ConsumerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Errors")
	ret0, _ := ret[0].(<-chan *sarama.ConsumerError)
	return ret0
}

// Errors indicates an expected call of Errors.
func (mr *MockPartitionOffsetManagerMockRecorder) Errors() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Errors", reflect.TypeOf((*MockPartitionOffsetManager)(nil).Errors))
}

// MarkOffset mocks base method.
func (m *MockPartitionOffsetManager) MarkOffset(arg0 int64, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "MarkOffset", arg0, arg1)
}

// MarkOffset indicates an expected call of MarkOffset.
func (mr *MockPartitionOffsetManagerMockRecorder) MarkOffset(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOffset", reflect.TypeOf((*MockPartitionOffsetManager)(nil).MarkOffset), arg0, arg1)
}

// NextOffset mocks base method.
func (m *MockPartitionOffsetManager) NextOffset() (int64, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NextOffset")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// NextOffset indicates an expected call of NextOffset.
func (mr *MockPartitionOffsetManagerMockRecorder) NextOffset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NextOffset", reflect.TypeOf((*MockPartitionOffsetManager)(nil).NextOffset))
}

// ResetOffset mocks base method.
func (m *MockPartitionOffsetManager) ResetOffset(arg0 int64, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ResetOffset", arg0, arg1)
}

// ResetOffset indicates an expected call of ResetOffset.
func (mr *MockPartitionOffsetManagerMockRecorder) ResetOffset(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetOffset", reflect.TypeOf((*MockPartitionOffsetManager)(nil).ResetOffset), arg0, arg1)
}

// MockClusterAdmin is a mock of ClusterAdmin interface.
type MockClusterAdmin struct {
	ctrl     *gomock.Controller
	recorder *MockClusterAdminMockRecorder
}

// MockClusterAdminMockRecorder is the mock recorder for MockClusterAdmin.
type MockClusterAdminMockRecorder struct {
	mock *MockClusterAdmin
}

// NewMockClusterAdmin creates a new mock instance.
func NewMockClusterAdmin(ctrl *gomock.Controller) *MockClusterAdmin {
	mock := &MockClusterAdmin{ctrl: ctrl}
	mock.recorder = &MockClusterAdminMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClusterAdmin) EXPECT() *MockClusterAdminMockRecorder {
	return m.recorder
}

// AlterConfig mocks base method.
func (m *MockClusterAdmin) AlterConfig(arg0 sarama.ConfigResourceType, arg1 string, arg2 map[string]*string, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AlterConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AlterConfig indicates an expected call of AlterConfig.
func (mr *MockClusterAdminMockRecorder) AlterConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AlterConfig", reflect.TypeOf((*MockClusterAdmin)(nil).AlterConfig), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockClusterAdmin) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockClusterAdminMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockClusterAdmin)(nil).Close))
}

// CreateACL mocks base method.
func (m *MockClusterAdmin) CreateACL(arg0 sarama.Resource, arg1 sarama.Acl) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateACL", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateACL indicates an expected call of CreateACL.
func (mr *MockClusterAdminMockRecorder) CreateACL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateACL", reflect.TypeOf((*MockClusterAdmin)(nil).CreateACL), arg0, arg1)
}

// CreatePartitions mocks base method.
func (m *MockClusterAdmin) CreatePartitions(arg0 string, arg1 int32, arg2 [][]int32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePartitions", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePartitions indicates an expected call of CreatePartitions.
func (mr *MockClusterAdminMockRecorder) CreatePartitions(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePartitions", reflect.TypeOf((*MockClusterAdmin)(nil).CreatePartitions), arg0, arg1, arg2, arg3)
}

// CreateTopic mocks base method.
func (m *MockClusterAdmin) CreateTopic(arg0 string, arg1 *sarama.TopicDetail, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTopic", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTopic indicates an expected call of CreateTopic.
func (mr *MockClusterAdminMockRecorder) CreateTopic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTopic", reflect.TypeOf((*MockClusterAdmin)(nil).CreateTopic), arg0, arg1, arg2)
}

// DeleteACL mocks base method.
func (m *MockClusterAdmin) DeleteACL(arg0 sarama.AclFilter, arg1 bool) ([]sarama.MatchingAcl, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteACL", arg0, arg1)
	ret0, _ := ret[0].([]sarama.MatchingAcl)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteACL indicates an expected call of DeleteACL.
func (mr *MockClusterAdminMockRecorder) DeleteACL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteACL", reflect.TypeOf((*MockClusterAdmin)(nil).DeleteACL), arg0, arg1)
}

// DeleteRecords mocks base method.
func (m *MockClusterAdmin) DeleteRecords(arg0 string, arg1 map[int32]int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRecords", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRecords indicates an expected call of DeleteRecords.
func (mr *MockClusterAdminMockRecorder) DeleteRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecords", reflect.TypeOf((*MockClusterAdmin)(nil).DeleteRecords), arg0, arg1)
}

// DeleteTopic mocks base method.
func (m *MockClusterAdmin) DeleteTopic(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTopic", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTopic indicates an expected call of DeleteTopic.
func (mr *MockClusterAdminMockRecorder) DeleteTopic(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTopic", reflect.TypeOf((*MockClusterAdmin)(nil).DeleteTopic), arg0)
}

// DescribeCluster mocks base method.
func (m *MockClusterAdmin) DescribeCluster() ([]*sarama.Broker, int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeCluster")
	ret0, _ := ret[0].([]*sarama.Broker)
	ret1, _ := ret[1].(int32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// DescribeCluster indicates an expected call of DescribeCluster.
func (mr *MockClusterAdminMockRecorder) DescribeCluster() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeCluster", reflect.TypeOf((*MockClusterAdmin)(nil).DescribeCluster))
}

// DescribeConfig mocks base method.
func (m *MockClusterAdmin) DescribeConfig(arg0 sarama.ConfigResource) ([]sarama.ConfigEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeConfig", arg0)
	ret0, _ := ret[0].([]sarama.ConfigEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeConfig indicates an expected call of DescribeConfig.
func (mr *MockClusterAdminMockRecorder) DescribeConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeConfig", reflect.TypeOf((*MockClusterAdmin)(nil).DescribeConfig), arg0)
}

// DescribeConsumerGroups mocks base method.
func (m *MockClusterAdmin) DescribeConsumerGroups(arg0 []string) ([]*sarama.GroupDescription, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeConsumerGroups", arg0)
	ret0, _ := ret[0].([]*sarama.GroupDescription)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeConsumerGroups indicates an expected call of DescribeConsumerGroups.
func (mr *MockClusterAdminMockRecorder) DescribeConsumerGroups(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeConsumerGroups", reflect.TypeOf((*MockClusterAdmin)(nil).DescribeConsumerGroups), arg0)
}

// DescribeTopics mocks base method.
func (m *MockClusterAdmin) DescribeTopics(arg0 []string) ([]*sarama.TopicMetadata, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTopics", arg0)
	ret0, _ := ret[0].([]*sarama.TopicMetadata)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTopics indicates an expected call of DescribeTopics.
func (mr *MockClusterAdminMockRecorder) DescribeTopics(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTopics", reflect.TypeOf((*MockClusterAdmin)(nil).DescribeTopics), arg0)
}

// ListAcls mocks base method.
func (m *MockClusterAdmin) ListAcls(arg0 sarama.AclFilter) ([]sarama.ResourceAcls, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAcls", arg0)
	ret0, _ := ret[0].([]sarama.ResourceAcls)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAcls indicates an expected call of ListAcls.
func (mr *MockClusterAdminMockRecorder) ListAcls(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAcls", reflect.TypeOf((*MockClusterAdmin)(nil).ListAcls), arg0)
}

// ListConsumerGroupOffsets mocks base method.
func (m *MockClusterAdmin) ListConsumerGroupOffsets(arg0 string, arg1 map[string][]int32) (*sarama.OffsetFetchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConsumerGroupOffsets", arg0, arg1)
	ret0, _ := ret[0].(*sarama.OffsetFetchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConsumerGroupOffsets indicates an expected call of ListConsumerGroupOffsets.
func (mr *MockClusterAdminMockRecorder) ListConsumerGroupOffsets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConsumerGroupOffsets", reflect.TypeOf((*MockClusterAdmin)(nil).ListConsumerGroupOffsets), arg0, arg1)
}

// ListConsumerGroups mocks base method.
func (m *MockClusterAdmin) ListConsumerGroups() (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConsumerGroups")
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConsumerGroups indicates an expected call of ListConsumerGroups.
func (mr *MockClusterAdminMockRecorder) ListConsumerGroups() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConsumerGroups", reflect.TypeOf((*MockClusterAdmin)(nil).ListConsumerGroups))
}

// ListTopics mocks base method.
func (m *MockClusterAdmin) ListTopics() (map[string]sarama.TopicDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTopics")
	ret0, _ := ret[0].(map[string]sarama.TopicDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTopics indicates an expected call of ListTopics.
func (mr *MockClusterAdminMockRecorder) ListTopics() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTopics", reflect.TypeOf((*MockClusterAdmin)(nil).ListTopics))
}
