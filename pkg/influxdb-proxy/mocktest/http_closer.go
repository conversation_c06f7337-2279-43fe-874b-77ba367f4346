// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: io (interfaces: Closer)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// IOCloser is a mock of Closer interface
type IOCloser struct {
	ctrl     *gomock.Controller
	recorder *IOCloserMockRecorder
}

// IOCloserMockRecorder is the mock recorder for IOCloser
type IOCloserMockRecorder struct {
	mock *IOCloser
}

// NewIOCloser creates a new mock instance
func NewIOCloser(ctrl *gomock.Controller) *IOCloser {
	mock := &IOCloser{ctrl: ctrl}
	mock.recorder = &IOCloserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *IOCloser) EXPECT() *IOCloserMockRecorder {
	return m.recorder
}

// Close mocks base method
func (m *IOCloser) Close() error {
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close
func (mr *IOCloserMockRecorder) Close() *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*IOCloser)(nil).Close))
}
