// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/influxdata/influxdb/client/v2 (interfaces: Client,BatchPoints)

// Package mocktest is a generated GoMock package.
package mocktest

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	client "github.com/influxdata/influxdb/client/v2"
)

// InfluxClient is a mock of Client interface.
type InfluxClient struct {
	ctrl     *gomock.Controller
	recorder *InfluxClientMockRecorder
}

// InfluxClientMockRecorder is the mock recorder for InfluxClient.
type InfluxClientMockRecorder struct {
	mock *InfluxClient
}

// NewInfluxClient creates a new mock instance.
func NewInfluxClient(ctrl *gomock.Controller) *InfluxClient {
	mock := &InfluxClient{ctrl: ctrl}
	mock.recorder = &InfluxClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *InfluxClient) EXPECT() *InfluxClientMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *InfluxClient) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *InfluxClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*InfluxClient)(nil).Close))
}

// Ping mocks base method.
func (m *InfluxClient) Ping(arg0 time.Duration) (time.Duration, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", arg0)
	ret0, _ := ret[0].(time.Duration)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Ping indicates an expected call of Ping.
func (mr *InfluxClientMockRecorder) Ping(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*InfluxClient)(nil).Ping), arg0)
}

// Query mocks base method.
func (m *InfluxClient) Query(arg0 client.Query) (*client.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Query", arg0)
	ret0, _ := ret[0].(*client.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *InfluxClientMockRecorder) Query(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*InfluxClient)(nil).Query), arg0)
}

// QueryAsChunk mocks base method.
func (m *InfluxClient) QueryAsChunk(arg0 client.Query) (*client.ChunkedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAsChunk", arg0)
	ret0, _ := ret[0].(*client.ChunkedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAsChunk indicates an expected call of QueryAsChunk.
func (mr *InfluxClientMockRecorder) QueryAsChunk(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAsChunk", reflect.TypeOf((*InfluxClient)(nil).QueryAsChunk), arg0)
}

// QueryCtx mocks base method.
func (m *InfluxClient) QueryCtx(arg0 context.Context, arg1 client.Query) (*client.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryCtx", arg0, arg1)
	ret0, _ := ret[0].(*client.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryCtx indicates an expected call of QueryCtx.
func (mr *InfluxClientMockRecorder) QueryCtx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCtx", reflect.TypeOf((*InfluxClient)(nil).QueryCtx), arg0, arg1)
}

// Write mocks base method.
func (m *InfluxClient) Write(arg0 client.BatchPoints) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Write indicates an expected call of Write.
func (mr *InfluxClientMockRecorder) Write(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*InfluxClient)(nil).Write), arg0)
}

// MockBatchPoints is a mock of BatchPoints interface.
type MockBatchPoints struct {
	ctrl     *gomock.Controller
	recorder *MockBatchPointsMockRecorder
}

// MockBatchPointsMockRecorder is the mock recorder for MockBatchPoints.
type MockBatchPointsMockRecorder struct {
	mock *MockBatchPoints
}

// NewMockBatchPoints creates a new mock instance.
func NewMockBatchPoints(ctrl *gomock.Controller) *MockBatchPoints {
	mock := &MockBatchPoints{ctrl: ctrl}
	mock.recorder = &MockBatchPointsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBatchPoints) EXPECT() *MockBatchPointsMockRecorder {
	return m.recorder
}

// AddPoint mocks base method.
func (m *MockBatchPoints) AddPoint(arg0 *client.Point) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPoint", arg0)
}

// AddPoint indicates an expected call of AddPoint.
func (mr *MockBatchPointsMockRecorder) AddPoint(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPoint", reflect.TypeOf((*MockBatchPoints)(nil).AddPoint), arg0)
}

// AddPoints mocks base method.
func (m *MockBatchPoints) AddPoints(arg0 []*client.Point) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddPoints", arg0)
}

// AddPoints indicates an expected call of AddPoints.
func (mr *MockBatchPointsMockRecorder) AddPoints(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPoints", reflect.TypeOf((*MockBatchPoints)(nil).AddPoints), arg0)
}

// Database mocks base method.
func (m *MockBatchPoints) Database() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Database")
	ret0, _ := ret[0].(string)
	return ret0
}

// Database indicates an expected call of Database.
func (mr *MockBatchPointsMockRecorder) Database() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Database", reflect.TypeOf((*MockBatchPoints)(nil).Database))
}

// Points mocks base method.
func (m *MockBatchPoints) Points() []*client.Point {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Points")
	ret0, _ := ret[0].([]*client.Point)
	return ret0
}

// Points indicates an expected call of Points.
func (mr *MockBatchPointsMockRecorder) Points() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Points", reflect.TypeOf((*MockBatchPoints)(nil).Points))
}

// Precision mocks base method.
func (m *MockBatchPoints) Precision() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Precision")
	ret0, _ := ret[0].(string)
	return ret0
}

// Precision indicates an expected call of Precision.
func (mr *MockBatchPointsMockRecorder) Precision() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Precision", reflect.TypeOf((*MockBatchPoints)(nil).Precision))
}

// RetentionPolicy mocks base method.
func (m *MockBatchPoints) RetentionPolicy() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetentionPolicy")
	ret0, _ := ret[0].(string)
	return ret0
}

// RetentionPolicy indicates an expected call of RetentionPolicy.
func (mr *MockBatchPointsMockRecorder) RetentionPolicy() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetentionPolicy", reflect.TypeOf((*MockBatchPoints)(nil).RetentionPolicy))
}

// SetDatabase mocks base method.
func (m *MockBatchPoints) SetDatabase(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDatabase", arg0)
}

// SetDatabase indicates an expected call of SetDatabase.
func (mr *MockBatchPointsMockRecorder) SetDatabase(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDatabase", reflect.TypeOf((*MockBatchPoints)(nil).SetDatabase), arg0)
}

// SetPrecision mocks base method.
func (m *MockBatchPoints) SetPrecision(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPrecision", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPrecision indicates an expected call of SetPrecision.
func (mr *MockBatchPointsMockRecorder) SetPrecision(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPrecision", reflect.TypeOf((*MockBatchPoints)(nil).SetPrecision), arg0)
}

// SetRetentionPolicy mocks base method.
func (m *MockBatchPoints) SetRetentionPolicy(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetRetentionPolicy", arg0)
}

// SetRetentionPolicy indicates an expected call of SetRetentionPolicy.
func (mr *MockBatchPointsMockRecorder) SetRetentionPolicy(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRetentionPolicy", reflect.TypeOf((*MockBatchPoints)(nil).SetRetentionPolicy), arg0)
}

// SetWriteConsistency mocks base method.
func (m *MockBatchPoints) SetWriteConsistency(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetWriteConsistency", arg0)
}

// SetWriteConsistency indicates an expected call of SetWriteConsistency.
func (mr *MockBatchPointsMockRecorder) SetWriteConsistency(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteConsistency", reflect.TypeOf((*MockBatchPoints)(nil).SetWriteConsistency), arg0)
}

// WriteConsistency mocks base method.
func (m *MockBatchPoints) WriteConsistency() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteConsistency")
	ret0, _ := ret[0].(string)
	return ret0
}

// WriteConsistency indicates an expected call of WriteConsistency.
func (mr *MockBatchPointsMockRecorder) WriteConsistency() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteConsistency", reflect.TypeOf((*MockBatchPoints)(nil).WriteConsistency))
}
