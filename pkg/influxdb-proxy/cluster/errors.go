// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package cluster

import (
	"github.com/pkg/errors"
)

// :
var (
	// ErrDBNotMatch          = errors.New("DB route doesn't match")
	// ErrNotAvailableBackend = errors.New("no available backend")
	ErrMissingBackend = errors.New("found some backend is nil")

	// ErrWriteFail           = errors.New("some backends write or backup fail")
	// ErrMissingDB           = errors.New("missing db in url params")
	// ErrMissingSQL          = errors.New("missing sql in url params")
	ErrNoAvailableBackend = errors.New("no available backend now")
)

// :
var (
	ErrWriteFailed       = errors.New("write failed")
	ErrQueryFailed       = errors.New("query failed")
	ErrCreateDBFailed    = errors.New("create db failed")
	ErrGetTagValueFailed = errors.New("get tag values failed")
)

// :
var (
	ErrClusterNotExist = errors.New("cluster not found")
	ErrRefreshFailed   = errors.New("get error when refreshing")
	ErrBackupIsNil     = errors.New("backup is nil")
)
