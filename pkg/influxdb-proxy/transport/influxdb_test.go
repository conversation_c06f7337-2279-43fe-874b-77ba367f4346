// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package transport_test

import (
	"testing"
)

func TestQueryClient(t *testing.T) {
	// client, err := transport.GetClient("http://127.0.0.1:8086", "", "")
	// fmt.Println(err)
	// tags := common.Tags{
	// 	common.Tag{
	// 		Key:   []byte("mytag"),
	// 		Value: []byte("1"),
	// 	},
	// }
	// data, err := transport.QueryClientToWriteData("db1", "table1", tags, 0, 0, client)
	// fmt.Println(err)
	// fmt.Println(data)
}

func TestQueryTimestamp(t *testing.T) {
	// client, err := transport.GetClient("http://127.0.0.1:8086", "", "")
	// fmt.Println(err)
	// tags := common.Tags{
	// 	common.Tag{
	// 		Key:   []byte("mytag"),
	// 		Value: []byte("1"),
	// 	},
	// }
	// data, err := transport.QueryTimestamp("db1", "table1", tags, client)
	// fmt.Println(err)
	// fmt.Println(data)
}

func TestWrite(t *testing.T) {
	// client1, err := transport.GetClient("http://127.0.0.1:8086", "", "")
	// fmt.Println(err)
	// tags := common.Tags{
	// 	common.Tag{
	// 		Key:   []byte("mytag"),
	// 		Value: []byte("1"),
	// 	},
	// }
	// data, err := transport.QueryClientToWriteData("db1", "table1", tags, 0, 0, client1)
	// fmt.Println(err)
	// fmt.Println(data)

	// client2, err := transport.GetClient("http://127.0.0.1:8087", "", "")
	// fmt.Println(err)
	// err = transport.WriteClient("db1", data, client2)
	// fmt.Println(err)
}
