// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package common

import (
	"bytes"
	"encoding/json"

	"github.com/pkg/errors"
	"github.com/spf13/viper"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/event"
	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/golang/eventbus"
)

// ViperConfiguration :
type ViperConfiguration struct {
	*viper.Viper
}

// Sub :
func (c *ViperConfiguration) Sub(key string) Configuration {
	return NewViperConfiguration(c.Viper.Sub(key))
}

// Marshal :
func (c *ViperConfiguration) Marshal(v interface{}) error {
	data, err := json.Marshal(v)
	if err != nil {
		return err
	}

	c.SetConfigType("json")
	return c.ReadConfig(bytes.NewBuffer(data))
}

func (c *ViperConfiguration) optsTovopts(opts []interface{}) ([]viper.DecoderConfigOption, error) {
	vopts := make([]viper.DecoderConfigOption, len(opts))
	for i, opt := range opts {
		vopt, ok := opt.(viper.DecoderConfigOption)
		if !ok {
			return nil, errors.Wrap(ErrType, "failed to decode config")
		}
		vopts[i] = vopt
	}
	return vopts, nil
}

// UnmarshalKey :
func (c *ViperConfiguration) UnmarshalKey(key string, rawVal interface{}, opts ...interface{}) error {
	vopts, err := c.optsTovopts(opts)
	if err != nil {
		return err
	}
	return c.Viper.UnmarshalKey(key, rawVal, vopts...)
}

// Unmarshal :
func (c *ViperConfiguration) Unmarshal(rawVal interface{}, opts ...interface{}) error {
	vopts, err := c.optsTovopts(opts)
	if err != nil {
		return err
	}
	return c.Viper.Unmarshal(rawVal, vopts...)
}

// ReloadConfigFile :
func (c *ViperConfiguration) ReloadConfigFile() error {
	// required the lock , avoid any config reading
	Lock.Lock()
	defer Lock.Unlock()

	// publish event for config file pre-parse
	eventbus.Publish(event.EvSysConfigPreParse, c)
	err := Config.ReadInConfig()
	if err != nil {
		return err
	}
	// publish event for config file post-parse
	eventbus.Publish(event.EvSysConfigPostParse)

	return nil
}

// NewViperConfiguration : create a Configuration from viper
func NewViperConfiguration(v *viper.Viper) Configuration {
	return &ViperConfiguration{
		Viper: v,
	}
}
