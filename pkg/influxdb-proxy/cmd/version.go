// Ten<PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

var (
	BuildHash string
	Version   string
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print version info",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("%s\n", Version)
		all, _ := cmd.Flags().GetBool("all")
		if all {
			fmt.Printf("%s\n", BuildHash)
		}
	},
}

func init() {
	if Version == "" || BuildHash == "" {
		fmt.Print("version is empty, no version command available.")
		return
	}

	rootCmd.AddCommand(versionCmd)
	versionCmd.Flags().BoolP("all", "a", false, "show all version info")
}
