// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package http_test

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/influxdb-proxy/http"
)

func TestRegexpMatch(t *testing.T) {
	tempExp := regexp.MustCompile("^(?i:show)\\s+(?i:series)\\s+(?:(?i:on)\\s+(\\S+)\\s+)?(?i:from)\\s+(\\S+)[;]?")
	res := http.RegexpMatch(tempExp, "show series on db1 from ttt")
	assert.Equal(t, 2, len(res))
	res = http.RegexpMatch(tempExp, "show series from ttt")
	assert.Equal(t, 2, len(res))
	res = http.RegexpMatch(tempExp, "show series")
	assert.Equal(t, 0, len(res))
}
