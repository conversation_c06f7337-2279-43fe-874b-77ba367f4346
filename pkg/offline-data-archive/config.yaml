move:
  instance_name:
  cluster_name:
  tag_router:
  source_dir:
  target_name: cos
  target_dir:
  max_pool: 10
  interval: 1h
  distribute_lock:
    expiration: 10m
    renewal_duration: 1m
  influxdb:
    address:
    username:
    password:

rebuild:
  final_name: cos
  final_dir: demo/rebuild
  max_pool: 10
  interval: 1h
  distribute_lock:
    expiration: 10m
    renewal_duration: 1m

query:
  http:
    host: 0.0.0.0
    port: 8090
    read_timeout: 5s
    metric: /metric

common:
  temp_dir: dist/cos_temp

logger:
  level: info
cos:
  region: ap-guangzhou
  url:
  bucket:
  app_id:
  secret_id:
  secret_key:
  thread_pool_size: 5
  timeout: 30s
  max_retries: 3
redis:
  mode: standalone
  host: 127.0.0.1
  port: 6379
  password:
  master_name:
  sentinel_address:
    - 127.0.0.1:6379
  sentinel_password:
  database: 0
  dial_timeout: 1s
  read_timeout: 10s
  service_name: bkmonitorv3:archive
trace:
  service_name: offline-data-archive
  otlp:
    host:
    port:
    token:
    type: