// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package metadata

import (
	"context"
	"fmt"

	cache "github.com/patrickmn/go-cache"
	"go.opentelemetry.io/otel/trace"
)

var (
	mdc *mdCache
)

// mdCache 元数据存储
type mdCache struct {
	c *cache.Cache
}

// Get 通过 traceID + key 获取缓存
func (m *mdCache) get(ctx context.Context, key string) (interface{}, bool) {
	span := trace.SpanFromContext(ctx)
	traceID := span.SpanContext().TraceID().String()
	k := fmt.Sprintf("%s_%s", traceID, key)
	return m.c.Get(k)
}

// Set 通过 traceID + key 写入缓存
func (m *mdCache) set(ctx context.Context, key string, value interface{}) {
	span := trace.SpanFromContext(ctx)
	traceID := span.SpanContext().TraceID().String()
	k := fmt.Sprintf("%s_%s", traceID, key)
	m.c.SetDefault(k, value)
}
