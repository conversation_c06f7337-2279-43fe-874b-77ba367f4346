syntax = "proto3";

option go_package = "./;remoteRead";
package remoteRead;

service QueryTimeSeriesService {
  rpc Raw(ReadRequest) returns (stream TimeSeries) {};
}

message ReadRequest {
  string clusterName = 1;
  string tagRouter = 2;
  string db = 3;
  string rp = 4;
  string measurement = 5;
  string field = 6;
  int64 start = 7;
  int64 end = 8;
  string condition = 9;
  int64 limit = 10;
  int64 sLimit = 11;
}

message Sample {
  double value = 1;
  int64 timestamp_ms = 2;
}

message LabelPair {
  string name = 1;
  string value = 2;
}

message TimeSeries {
  repeated LabelPair labels = 1;
  // Sorted by time, oldest sample first.
  repeated Sample samples = 2;
}