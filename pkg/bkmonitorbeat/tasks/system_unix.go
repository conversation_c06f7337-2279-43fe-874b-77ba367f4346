// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.
//

//go:build aix || darwin || dragonfly || linux || netbsd || openbsd || solaris || zos

package tasks

import (
	"strings"

	"golang.org/x/sys/unix"
)

func getMachineHardwareIdentifier() (string, error) {
	buf := unix.Utsname{}
	err := unix.Uname(&buf)
	if err != nil {
		return "", err
	}
	// 转化byte数组为string
	machine := string(buf.Machine[:])
	return machine, nil
}

// GetSystemType 32-bit or 64-bit or unknown
func GetSystemType() string {
	machine, err := getMachineHardwareIdentifier()
	if err != nil {
		return "unknown"
	}

	switch {
	case strings.Contains(machine, "x86_64"):
		return "64-bit"
	case strings.Contains(machine, "aarch64"):
		return "arm-64bit"
	case strings.Contains(machine, "aarch32"):
		return "arm-32bit"
	default:
		return "32-bit"
	}
}
