// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

//go:build aix || darwin || dragonfly || freebsd || linux || netbsd || openbsd || solaris || zos

package toolkit

/*
get crontab from /var/spool/cron/ directory
*/

import (
	"os"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/utils/logger"
)

func ListCrontab() ([]Crontab, error) {
	ret := make([]Crontab, 0)

	files, err := os.ReadDir(CRONTAB_PATH)
	if err != nil {
		logger.Errorf("os.ReadDir %s failed", CRONTAB_PATH)
		return nil, err
	}

	for _, f := range files {
		bytes, err := os.ReadFile(CRONTAB_PATH + f.Name())
		if err != nil {
			logger.Errorf("os.ReadFile %s failed. %v", f.Name(), err)
			continue
		}
		cnt := Crontab{
			Name:    f.Name(),
			Content: string(bytes),
		}
		ret = append(ret, cnt)
	}

	return ret, nil
}
