#!/bin/bash
# <PERSON><PERSON> is pleased to support the open source community by making
# 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
# Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
# Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
# You may obtain a copy of the License at http://opensource.org/licenses/MIT
# Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.


source ./env.sh
chmod 0644 ${config}
chmod +x ${cmd}

# 获取非隔离CPU核心列表
# 从/sys/devices/system/cpu/online中获取在线CPU，从/sys/devices/system/cpu/isolated中排除隔离CPU
get_allowed_cpus() {
    local online_cpus=""
    local isolated_cpus=""

    if [ -f /sys/devices/system/cpu/online ]; then
        online_cpus=$(cat /sys/devices/system/cpu/online)
    fi

    if [ -f /sys/devices/system/cpu/isolated ]; then
        isolated_cpus=$(cat /sys/devices/system/cpu/isolated)
    fi

    # 如果没有隔离CPU，使用所有在线CPU
    if [ -z "$isolated_cpus" ]; then
        echo "$online_cpus"
        return
    fi

    # 解析CPU范围并计算允许的CPU
    python3 -c "
import sys
def parse_cpu_list(cpu_str):
    if not cpu_str.strip():
        return set()
    cpus = set()
    for part in cpu_str.split(','):
        if '-' in part:
            start, end = map(int, part.split('-'))
            cpus.update(range(start, end + 1))
        else:
            cpus.add(int(part))
    return cpus

online = parse_cpu_list('$online_cpus')
isolated = parse_cpu_list('$isolated_cpus')
allowed = sorted(online - isolated)

if allowed:
    # 格式化为taskset可接受的格式
    result = []
    i = 0
    while i < len(allowed):
        start = allowed[i]
        end = start
        while i + 1 < len(allowed) and allowed[i + 1] == allowed[i] + 1:
            i += 1
            end = allowed[i]
        if start == end:
            result.append(str(start))
        else:
            result.append(f'{start}-{end}')
        i += 1
    print(','.join(result))
else:
    print('$online_cpus')
"
}

# 获取允许的CPU列表
ALLOWED_CPUS=$(get_allowed_cpus)

# 使用taskset限制CPU亲和性启动进程
if [ -n "$ALLOWED_CPUS" ] && command -v taskset >/dev/null 2>&1; then
    echo "Starting bkmonitor-beat with CPU affinity: $ALLOWED_CPUS"
    nohup taskset -c "$ALLOWED_CPUS" ./${cmd} -httpprof localhost:6069 -E IDENT=${ident} -E VERSION=${version} -c ./${config} 1>/dev/null 2>&1 &
else
    echo "Starting bkmonitor-beat without CPU affinity restriction"
    nohup ./${cmd} -httpprof localhost:6069 -E IDENT=${ident} -E VERSION=${version} -c ./${config} 1>/dev/null 2>&1 &
fi
