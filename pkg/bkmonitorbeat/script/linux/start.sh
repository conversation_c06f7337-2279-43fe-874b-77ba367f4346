#!/bin/bash
# <PERSON><PERSON> is pleased to support the open source community by making
# 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
# Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
# Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
# You may obtain a copy of the License at http://opensource.org/licenses/MIT
# Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.


source ./env.sh
chmod 0644 ${config}
chmod +x ${cmd}

# 解析CPU列表字符串，如 "0-3,6,8-10" 转换为数组
parse_cpu_list() {
    local cpu_str="$1"
    local -a cpus=()

    if [ -z "$cpu_str" ]; then
        echo ""
        return
    fi

    # 分割逗号分隔的部分
    IFS=',' read -ra parts <<< "$cpu_str"
    for part in "${parts[@]}"; do
        part=$(echo "$part" | tr -d ' ')  # 去除空格
        if [[ "$part" == *-* ]]; then
            # 处理范围，如 "0-3"
            IFS='-' read -ra range <<< "$part"
            if [ ${#range[@]} -eq 2 ]; then
                start=${range[0]}
                end=${range[1]}
                for ((i=start; i<=end; i++)); do
                    cpus+=($i)
                done
            fi
        else
            # 处理单个CPU
            if [[ "$part" =~ ^[0-9]+$ ]]; then
                cpus+=($part)
            fi
        fi
    done

    # 输出数组，用空格分隔
    echo "${cpus[@]}"
}

# 数组差集运算：从第一个数组中移除第二个数组中的元素
array_diff() {
    local -a arr1=($1)
    local -a arr2=($2)
    local -a result=()

    for item1 in "${arr1[@]}"; do
        local found=0
        for item2 in "${arr2[@]}"; do
            if [ "$item1" -eq "$item2" ]; then
                found=1
                break
            fi
        done
        if [ $found -eq 0 ]; then
            result+=($item1)
        fi
    done

    echo "${result[@]}"
}

# 数组排序
array_sort() {
    local -a arr=($1)
    local -a sorted=($(printf '%s\n' "${arr[@]}" | sort -n))
    echo "${sorted[@]}"
}

# 将CPU数组格式化为taskset可接受的格式
format_cpu_list() {
    local -a cpus=($1)
    local -a result=()
    local i=0

    if [ ${#cpus[@]} -eq 0 ]; then
        echo ""
        return
    fi

    while [ $i -lt ${#cpus[@]} ]; do
        local start=${cpus[$i]}
        local end=$start

        # 查找连续的CPU
        while [ $((i + 1)) -lt ${#cpus[@]} ] && [ ${cpus[$((i + 1))]} -eq $((${cpus[$i]} + 1)) ]; do
            i=$((i + 1))
            end=${cpus[$i]}
        done

        if [ $start -eq $end ]; then
            result+=("$start")
        else
            result+=("$start-$end")
        fi
        i=$((i + 1))
    done

    # 用逗号连接
    IFS=','
    echo "${result[*]}"
    IFS=' '
}

# 获取非隔离CPU核心列表
# 从/sys/devices/system/cpu/online中获取在线CPU，从/sys/devices/system/cpu/isolated中排除隔离CPU
get_allowed_cpus() {
    local online_cpus=""
    local isolated_cpus=""

    if [ -f /sys/devices/system/cpu/online ]; then
        online_cpus=$(cat /sys/devices/system/cpu/online)
    fi

    if [ -f /sys/devices/system/cpu/isolated ]; then
        isolated_cpus=$(cat /sys/devices/system/cpu/isolated)
    fi

    # 如果没有隔离CPU，使用所有在线CPU
    if [ -z "$isolated_cpus" ]; then
        echo "$online_cpus"
        return
    fi

    # 解析CPU列表
    local online_array=($(parse_cpu_list "$online_cpus"))
    local isolated_array=($(parse_cpu_list "$isolated_cpus"))

    # 计算差集
    local allowed_array=($(array_diff "${online_array[*]}" "${isolated_array[*]}"))

    # 排序
    allowed_array=($(array_sort "${allowed_array[*]}"))

    if [ ${#allowed_array[@]} -gt 0 ]; then
        # 格式化输出
        format_cpu_list "${allowed_array[*]}"
    else
        echo "$online_cpus"
    fi
}

# 获取允许的CPU列表
ALLOWED_CPUS=$(get_allowed_cpus)

# 使用taskset限制CPU亲和性启动进程
if [ -n "$ALLOWED_CPUS" ] && command -v taskset >/dev/null 2>&1; then
    echo "Starting bkmonitor-beat with CPU affinity: $ALLOWED_CPUS"
    nohup taskset -c "$ALLOWED_CPUS" ./${cmd} -httpprof localhost:6069 -E IDENT=${ident} -E VERSION=${version} -c ./${config} 1>/dev/null 2>&1 &
else
    echo "Starting bkmonitor-beat without CPU affinity restriction"
    nohup ./${cmd} -httpprof localhost:6069 -E IDENT=${ident} -E VERSION=${version} -c ./${config} 1>/dev/null 2>&1 &
fi
