# FreeBSD

## 系统额外依赖

| 依赖        | 用途           | 安装                                                                                                                                           | 文档                                                                              |
|-----------|--------------|----------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------|
| linprocfs | 用于获取进程相关执行数据 | `mount -t linprocfs linproc /compat/linux/proc`<br/>同时 `/etc/fstab` 添加一行： <br/>`linproc         /compat/linux/proc      linprocfs       rw 0 0` | [linprocfs](https://www.freebsd.org/cgi/man.cgi?linprocfs(5))                   |
| lsof      | 用于获取监听端口数据   | `pkg install -y lsof`<br/>同时 `/etc/rc` 中 PATH 修改为 `PATH=/sbin:/bin:/usr/sbin:/usr/bin:/usr/local/sbin:/usr/local/bin:`                            | [lsof](https://www.freebsd.org/cgi/man.cgi?query=lsof&sektion=8&manpath=Darwin) |

## 缺失功能

| 功能         | 原因                       |
|------------|--------------------------|
| oom异常检测    | 现有逻辑仅适用 linux             |
| SWAP数据     | 测试设备无 SWAP 分区              |
| 系统fs-max指标 | 无 /proc/sys/fs/file-max 文件 |
| udp包统计     | 无 /proc/net/snmp 文件        |
| 进程文件句柄数    | 无 /proc/pid/fd 文件          |
