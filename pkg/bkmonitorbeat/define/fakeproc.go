// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package define

import (
	"bytes"
	"fmt"
	"sync"
)

type pidStore struct {
	mut   sync.Mutex
	store map[string]struct{}
}

// GlobalPidStore 全局pid信息
var GlobalPidStore = newPidStore()

func newPidStore() *pidStore {
	return &pidStore{
		store: map[string]struct{}{},
	}
}

// Set 按照originPid fakePid cmd去重
func (s *pidStore) Set(originPid, fakePid int, cmd string) {
	s.mut.Lock()
	defer s.mut.Unlock()

	s.store[fmt.Sprintf("%d %d/:%s", fakePid, originPid, cmd)] = struct{}{}
}

func (s *pidStore) Bytes() []byte {
	s.mut.Lock()
	defer s.mut.Unlock()

	buf := &bytes.Buffer{}
	for k := range s.store {
		buf.Write([]byte(k + "\n"))
	}

	s.store = map[string]struct{}{}
	return buf.Bytes()
}
