// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitorbeat/task/metricbeat (interfaces: MetricTool)

// Package mock_metricbeat is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	config "github.com/TencentBlueKing/bkmonitor-datalink/pkg/bkmonitorbeat/configs"
	define "github.com/TencentBlueKing/bkmonitor-datalink/pkg/bkmonitorbeat/define"
)

// MockMetricTool is a mock of MetricTool interface
type MockMetricTool struct {
	ctrl     *gomock.Controller
	recorder *MockMetricToolMockRecorder
}

// MockMetricToolMockRecorder is the mock recorder for MockMetricTool
type MockMetricToolMockRecorder struct {
	mock *MockMetricTool
}

// NewMockMetricTool creates a new mock instance
func NewMockMetricTool(ctrl *gomock.Controller) *MockMetricTool {
	mock := &MockMetricTool{ctrl: ctrl}
	mock.recorder = &MockMetricToolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockMetricTool) EXPECT() *MockMetricToolMockRecorder {
	return m.recorder
}

// Init mocks base method
func (m *MockMetricTool) Init(arg0 *config.MetricBeatConfig) error {
	ret := m.ctrl.Call(m, "Init", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init
func (mr *MockMetricToolMockRecorder) Init(arg0 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockMetricTool)(nil).Init), arg0)
}

// Run mocks base method
func (m *MockMetricTool) Run(arg0 context.Context, arg1 chan<- define.Event) error {
	ret := m.ctrl.Call(m, "Run", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Run indicates an expected call of Run
func (mr *MockMetricToolMockRecorder) Run(arg0, arg1 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockMetricTool)(nil).Run), arg0, arg1)
}
