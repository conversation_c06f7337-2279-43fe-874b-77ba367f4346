// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: task/ping/pingtool.go

// Package mock_ping is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	ping "github.com/TencentBlueKing/bkmonitor-datalink/pkg/bkmonitorbeat/tasks/ping"
)

// MockTool is a mock of Tool interface
type MockTool struct {
	ctrl     *gomock.Controller
	recorder *MockToolMockRecorder
}

// MockToolMockRecorder is the mock recorder for MockTool
type MockToolMockRecorder struct {
	mock *MockTool
}

// NewMockTool creates a new mock instance
func NewMockTool(ctrl *gomock.Controller) *MockTool {
	mock := &MockTool{ctrl: ctrl}
	mock.recorder = &MockToolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockTool) EXPECT() *MockToolMockRecorder {
	return m.recorder
}

// Ping mocks base method
func (m *MockTool) Ping(doFunc ping.DoFunc) error {
	ret := m.ctrl.Call(m, "Ping", doFunc)
	ret0, _ := ret[0].(error)
	return ret0
}

// Ping indicates an expected call of Ping
func (mr *MockToolMockRecorder) Ping(doFunc interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockTool)(nil).Ping), doFunc)
}

// MockTarget is a mock of Target interface
type MockTarget struct {
	ctrl     *gomock.Controller
	recorder *MockTargetMockRecorder
}

// MockTargetMockRecorder is the mock recorder for MockTarget
type MockTargetMockRecorder struct {
	mock *MockTarget
}

// NewMockTarget creates a new mock instance
func NewMockTarget(ctrl *gomock.Controller) *MockTarget {
	mock := &MockTarget{ctrl: ctrl}
	mock.recorder = &MockTargetMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockTarget) EXPECT() *MockTargetMockRecorder {
	return m.recorder
}

// GetTarget mocks base method
func (m *MockTarget) GetTarget() string {
	ret := m.ctrl.Call(m, "GetTarget")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTarget indicates an expected call of GetTarget
func (mr *MockTargetMockRecorder) GetTarget() *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTarget", reflect.TypeOf((*MockTarget)(nil).GetTarget))
}

// GetTargetType mocks base method
func (m *MockTarget) GetTargetType() string {
	ret := m.ctrl.Call(m, "GetTargetType")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTargetType indicates an expected call of GetTargetType
func (mr *MockTargetMockRecorder) GetTargetType() *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTargetType", reflect.TypeOf((*MockTarget)(nil).GetTargetType))
}
