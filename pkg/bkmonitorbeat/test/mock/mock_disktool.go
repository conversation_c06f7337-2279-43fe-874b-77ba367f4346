// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/TencentBlueKing/bkmonitorbeat/task/diskfull (interfaces: DiskTool)

// Package mock_diskfull is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockDiskTool is a mock of DiskTool interface
type MockDiskTool struct {
	ctrl     *gomock.Controller
	recorder *MockDiskToolMockRecorder
}

// MockDiskToolMockRecorder is the mock recorder for MockDiskTool
type MockDiskToolMockRecorder struct {
	mock *MockDiskTool
}

// NewMockDiskTool creates a new mock instance
func NewMockDiskTool(ctrl *gomock.Controller) *MockDiskTool {
	mock := &MockDiskTool{ctrl: ctrl}
	mock.recorder = &MockDiskToolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockDiskTool) EXPECT() *MockDiskToolMockRecorder {
	return m.recorder
}

// GetDiskInfo mocks base method
func (m *MockDiskTool) GetDiskInfo() ([]map[string]interface{}, error) {
	ret := m.ctrl.Call(m, "GetDiskInfo")
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskInfo indicates an expected call of GetDiskInfo
func (mr *MockDiskToolMockRecorder) GetDiskInfo() *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskInfo", reflect.TypeOf((*MockDiskTool)(nil).GetDiskInfo))
}

// Init mocks base method
func (m *MockDiskTool) Init(arg0 []string) error {
	ret := m.ctrl.Call(m, "Init", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init
func (mr *MockDiskToolMockRecorder) Init(arg0 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockDiskTool)(nil).Init), arg0)
}
