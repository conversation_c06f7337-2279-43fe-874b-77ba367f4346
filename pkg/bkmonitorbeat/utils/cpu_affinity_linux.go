//go:build linux
// +build linux

// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package utils

import (
	"fmt"
	"io/ioutil"
	"os"
	"runtime"
	"strconv"
	"strings"
	"syscall"
	"unsafe"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/utils/logger"
)

// SetCPUAffinityAvoidIsolated 设置CPU亲和性，避免使用隔离的CPU核心
func SetCPUAffinityAvoidIsolated() error {
	// 获取在线CPU列表
	onlineCPUs, err := getOnlineCPUs()
	if err != nil {
		logger.Warnf("failed to get online CPUs: %v", err)
		return err
	}

	// 获取隔离CPU列表
	isolatedCPUs, err := getIsolatedCPUs()
	if err != nil {
		logger.Debugf("failed to get isolated CPUs (this is normal if no CPUs are isolated): %v", err)
		isolatedCPUs = make(map[int]bool) // 如果没有隔离CPU，使用空map
	}

	// 计算允许的CPU列表（在线CPU - 隔离CPU）
	allowedCPUs := make([]int, 0)
	for cpu := range onlineCPUs {
		if !isolatedCPUs[cpu] {
			allowedCPUs = append(allowedCPUs, cpu)
		}
	}

	// 如果没有可用的CPU，使用所有在线CPU
	if len(allowedCPUs) == 0 {
		logger.Warn("no non-isolated CPUs found, using all online CPUs")
		for cpu := range onlineCPUs {
			allowedCPUs = append(allowedCPUs, cpu)
		}
	}

	logger.Infof("setting CPU affinity to CPUs: %v (avoiding isolated CPUs: %v)", allowedCPUs, getMapKeys(isolatedCPUs))

	// 设置CPU亲和性
	return setCPUAffinity(allowedCPUs)
}

// getOnlineCPUs 获取在线CPU列表
func getOnlineCPUs() (map[int]bool, error) {
	data, err := ioutil.ReadFile("/sys/devices/system/cpu/online")
	if err != nil {
		return nil, fmt.Errorf("failed to read online CPUs: %w", err)
	}

	return parseCPUList(strings.TrimSpace(string(data)))
}

// getIsolatedCPUs 获取隔离CPU列表
func getIsolatedCPUs() (map[int]bool, error) {
	data, err := ioutil.ReadFile("/sys/devices/system/cpu/isolated")
	if err != nil {
		return nil, fmt.Errorf("failed to read isolated CPUs: %w", err)
	}

	content := strings.TrimSpace(string(data))
	if content == "" {
		return make(map[int]bool), nil // 没有隔离CPU
	}

	return parseCPUList(content)
}

// parseCPUList 解析CPU列表字符串，如 "0-3,6,8-10"
func parseCPUList(cpuList string) (map[int]bool, error) {
	cpus := make(map[int]bool)
	if cpuList == "" {
		return cpus, nil
	}

	parts := strings.Split(cpuList, ",")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		if strings.Contains(part, "-") {
			// 处理范围，如 "0-3"
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) != 2 {
				return nil, fmt.Errorf("invalid CPU range: %s", part)
			}

			start, err := strconv.Atoi(rangeParts[0])
			if err != nil {
				return nil, fmt.Errorf("invalid CPU range start: %s", rangeParts[0])
			}

			end, err := strconv.Atoi(rangeParts[1])
			if err != nil {
				return nil, fmt.Errorf("invalid CPU range end: %s", rangeParts[1])
			}

			for i := start; i <= end; i++ {
				cpus[i] = true
			}
		} else {
			// 处理单个CPU
			cpu, err := strconv.Atoi(part)
			if err != nil {
				return nil, fmt.Errorf("invalid CPU number: %s", part)
			}
			cpus[cpu] = true
		}
	}

	return cpus, nil
}

// setCPUAffinity 使用系统调用设置CPU亲和性
func setCPUAffinity(cpus []int) error {
	if len(cpus) == 0 {
		return fmt.Errorf("no CPUs specified")
	}

	// 创建CPU集合
	var cpuSet syscall.CPUSet
	cpuSet.Zero()
	for _, cpu := range cpus {
		cpuSet.Set(cpu)
	}

	// 设置当前进程的CPU亲和性
	pid := os.Getpid()
	_, _, errno := syscall.Syscall(syscall.SYS_SCHED_SETAFFINITY, 
		uintptr(pid), 
		uintptr(unsafe.Sizeof(cpuSet)), 
		uintptr(unsafe.Pointer(&cpuSet)))

	if errno != 0 {
		return fmt.Errorf("failed to set CPU affinity: %v", errno)
	}

	// 同时设置Go运行时的MAXPROCS
	// 如果成功设置了CPU亲和性，限制Go调度器使用的CPU数量
	runtime.GOMAXPROCS(len(cpus))

	return nil
}

// getMapKeys 获取map的所有key
func getMapKeys(m map[int]bool) []int {
	keys := make([]int, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// GetCurrentCPUAffinity 获取当前进程的CPU亲和性
func GetCurrentCPUAffinity() ([]int, error) {
	var cpuSet syscall.CPUSet
	pid := os.Getpid()
	
	_, _, errno := syscall.Syscall(syscall.SYS_SCHED_GETAFFINITY,
		uintptr(pid),
		uintptr(unsafe.Sizeof(cpuSet)),
		uintptr(unsafe.Pointer(&cpuSet)))

	if errno != 0 {
		return nil, fmt.Errorf("failed to get CPU affinity: %v", errno)
	}

	var cpus []int
	for i := 0; i < runtime.NumCPU(); i++ {
		if cpuSet.IsSet(i) {
			cpus = append(cpus, i)
		}
	}

	return cpus, nil
}
