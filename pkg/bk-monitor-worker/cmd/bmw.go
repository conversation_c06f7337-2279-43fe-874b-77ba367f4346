// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	_ "go.uber.org/automaxprocs"

	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/bk-monitor-worker/cmd/apm"
	"github.com/TencentBlueKing/bkmonitor-datalink/pkg/bk-monitor-worker/config"
)

var rootCmd = &cobra.Command{
	Use:   "run",
	Short: "bk monitor task and worker",
	Long:  "worker module for blueking monitor",
}

// Execute 执行命令
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Printf("start bmw service error, %s", err)
		os.Exit(1)
	}
}

func init() {
	rootCmd.PersistentFlags().StringVar(
		&config.FilePath, "config", "./bmw.yaml", "path of project service config files",
	)
	rootCmd.AddCommand(apm.StartFromFileCmd())
}

func addFlag(configLoc string, flagName string, addVarFunc func()) {
	addVarFunc()
	err := viper.BindPFlag(configLoc, rootCmd.PersistentFlags().Lookup(flagName))
	if err != nil {
		panic(fmt.Errorf("lookup var failed: %s", flagName))
	}
}
