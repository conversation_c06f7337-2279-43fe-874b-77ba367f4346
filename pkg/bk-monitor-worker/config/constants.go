// Tencent is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package config

const (
	// TimeLayout time layout
	TimeLayout = "2006-01-02 15:04:05"
	// DefaultUsername default username
	DefaultUsername = "system"
	// DefaultDBFilterSize 查询数据库时，过滤条件in的数量
	DefaultDBFilterSize = 500
	// DefaultStringFilterSize url查询时，路径过滤条件长度
	DefaultStringFilterSize = 1024 * 3
)
