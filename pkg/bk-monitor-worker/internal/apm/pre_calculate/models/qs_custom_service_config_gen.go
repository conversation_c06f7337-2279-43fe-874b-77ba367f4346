// Code generated by go-queryset. DO NOT EDIT.
package models

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set CustomServiceConfigQuerySet

// CustomServiceConfigQuerySet is an queryset type for CustomServiceConfig
type CustomServiceConfigQuerySet struct {
	db *gorm.DB
}

// NewCustomServiceConfigQuerySet constructs new CustomServiceConfigQuerySet
func NewCustomServiceConfigQuerySet(db *gorm.DB) CustomServiceConfigQuerySet {
	return CustomServiceConfigQuerySet{
		db: db.Model(&CustomServiceConfig{}),
	}
}

func (qs CustomServiceConfigQuerySet) w(db *gorm.DB) CustomServiceConfigQuerySet {
	return NewCustomServiceConfigQuerySet(db)
}

func (qs CustomServiceConfigQuerySet) Select(fields ...CustomServiceConfigDBSchemaField) CustomServiceConfigQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *CustomServiceConfig) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *CustomServiceConfig) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) All(ret *[]CustomServiceConfig) error {
	return qs.db.Find(ret).Error
}

// AppNameEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameEq(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name = ?", appName))
}

// AppNameGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameGt(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name > ?", appName))
}

// AppNameGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameGte(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name >= ?", appName))
}

// AppNameIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameIn(appName ...string) CustomServiceConfigQuerySet {
	if len(appName) == 0 {
		qs.db.AddError(errors.New("must at least pass one appName in AppNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("app_name IN (?)", appName))
}

// AppNameLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameLike(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name LIKE ?", appName))
}

// AppNameLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameLt(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name < ?", appName))
}

// AppNameLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameLte(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name <= ?", appName))
}

// AppNameNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameNe(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name != ?", appName))
}

// AppNameNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameNotIn(appName ...string) CustomServiceConfigQuerySet {
	if len(appName) == 0 {
		qs.db.AddError(errors.New("must at least pass one appName in AppNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("app_name NOT IN (?)", appName))
}

// AppNameNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) AppNameNotlike(appName string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("app_name NOT LIKE ?", appName))
}

// BkBizIdEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdEq(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizId))
}

// BkBizIdGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdGt(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizId))
}

// BkBizIdGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdGte(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizId))
}

// BkBizIdIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdIn(bkBizId ...int) CustomServiceConfigQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizId))
}

// BkBizIdLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdLt(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizId))
}

// BkBizIdLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdLte(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizId))
}

// BkBizIdNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdNe(bkBizId int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizId))
}

// BkBizIdNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) BkBizIdNotIn(bkBizId ...int) CustomServiceConfigQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizId))
}

// ConfigKeyEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyEq(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key = ?", configKey))
}

// ConfigKeyGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyGt(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key > ?", configKey))
}

// ConfigKeyGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyGte(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key >= ?", configKey))
}

// ConfigKeyIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyIn(configKey ...string) CustomServiceConfigQuerySet {
	if len(configKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one configKey in ConfigKeyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config_key IN (?)", configKey))
}

// ConfigKeyLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyLike(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key LIKE ?", configKey))
}

// ConfigKeyLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyLt(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key < ?", configKey))
}

// ConfigKeyLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyLte(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key <= ?", configKey))
}

// ConfigKeyNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyNe(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key != ?", configKey))
}

// ConfigKeyNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyNotIn(configKey ...string) CustomServiceConfigQuerySet {
	if len(configKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one configKey in ConfigKeyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config_key NOT IN (?)", configKey))
}

// ConfigKeyNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigKeyNotlike(configKey string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_key NOT LIKE ?", configKey))
}

// ConfigLevelEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelEq(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level = ?", configLevel))
}

// ConfigLevelGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelGt(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level > ?", configLevel))
}

// ConfigLevelGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelGte(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level >= ?", configLevel))
}

// ConfigLevelIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelIn(configLevel ...string) CustomServiceConfigQuerySet {
	if len(configLevel) == 0 {
		qs.db.AddError(errors.New("must at least pass one configLevel in ConfigLevelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config_level IN (?)", configLevel))
}

// ConfigLevelLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelLike(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level LIKE ?", configLevel))
}

// ConfigLevelLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelLt(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level < ?", configLevel))
}

// ConfigLevelLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelLte(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level <= ?", configLevel))
}

// ConfigLevelNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelNe(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level != ?", configLevel))
}

// ConfigLevelNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelNotIn(configLevel ...string) CustomServiceConfigQuerySet {
	if len(configLevel) == 0 {
		qs.db.AddError(errors.New("must at least pass one configLevel in ConfigLevelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config_level NOT IN (?)", configLevel))
}

// ConfigLevelNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) ConfigLevelNotlike(configLevel string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("config_level NOT LIKE ?", configLevel))
}

// Count is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) Delete() error {
	return qs.db.Delete(CustomServiceConfig{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(CustomServiceConfig{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(CustomServiceConfig{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) GetUpdater() CustomServiceConfigUpdater {
	return NewCustomServiceConfigUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdEq(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdGt(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdGte(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdIn(id ...int) CustomServiceConfigQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdLt(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdLte(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdNe(id int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) IdNotIn(id ...int) CustomServiceConfigQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) Limit(limit int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// MatchTypeEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeEq(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type = ?", matchType))
}

// MatchTypeGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeGt(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type > ?", matchType))
}

// MatchTypeGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeGte(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type >= ?", matchType))
}

// MatchTypeIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeIn(matchType ...string) CustomServiceConfigQuerySet {
	if len(matchType) == 0 {
		qs.db.AddError(errors.New("must at least pass one matchType in MatchTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("match_type IN (?)", matchType))
}

// MatchTypeLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeLike(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type LIKE ?", matchType))
}

// MatchTypeLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeLt(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type < ?", matchType))
}

// MatchTypeLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeLte(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type <= ?", matchType))
}

// MatchTypeNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeNe(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type != ?", matchType))
}

// MatchTypeNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeNotIn(matchType ...string) CustomServiceConfigQuerySet {
	if len(matchType) == 0 {
		qs.db.AddError(errors.New("must at least pass one matchType in MatchTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("match_type NOT IN (?)", matchType))
}

// MatchTypeNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) MatchTypeNotlike(matchType string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("match_type NOT LIKE ?", matchType))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameEq(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameGt(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameGte(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameIn(name ...string) CustomServiceConfigQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameLike(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameLt(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameLte(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameNe(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameNotIn(name ...string) CustomServiceConfigQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) NameNotlike(name string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) Offset(offset int) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs CustomServiceConfigQuerySet) One(ret *CustomServiceConfig) error {
	return qs.db.First(ret).Error
}

// OrderAscByAppName is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByAppName() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("app_name ASC"))
}

// OrderAscByBkBizId is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByBkBizId() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByConfigKey is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByConfigKey() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("config_key ASC"))
}

// OrderAscByConfigLevel is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByConfigLevel() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("config_level ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscById() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByMatchType is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByMatchType() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("match_type ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByName() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByType is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderAscByType() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("type ASC"))
}

// OrderDescByAppName is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByAppName() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("app_name DESC"))
}

// OrderDescByBkBizId is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByBkBizId() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByConfigKey is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByConfigKey() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("config_key DESC"))
}

// OrderDescByConfigLevel is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByConfigLevel() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("config_level DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescById() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByMatchType is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByMatchType() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("match_type DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByName() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByType is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) OrderDescByType() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Order("type DESC"))
}

// PreloadRule is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) PreloadRule() CustomServiceConfigQuerySet {
	return qs.w(qs.db.Preload("Rule"))
}

// TypeEq is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeEq(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type = ?", typeValue))
}

// TypeGt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeGt(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type > ?", typeValue))
}

// TypeGte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeGte(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type >= ?", typeValue))
}

// TypeIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeIn(typeValue ...string) CustomServiceConfigQuerySet {
	if len(typeValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeValue in TypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type IN (?)", typeValue))
}

// TypeLike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeLike(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type LIKE ?", typeValue))
}

// TypeLt is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeLt(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type < ?", typeValue))
}

// TypeLte is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeLte(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type <= ?", typeValue))
}

// TypeNe is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeNe(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type != ?", typeValue))
}

// TypeNotIn is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeNotIn(typeValue ...string) CustomServiceConfigQuerySet {
	if len(typeValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeValue in TypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type NOT IN (?)", typeValue))
}

// TypeNotlike is an autogenerated method
// nolint: dupl
func (qs CustomServiceConfigQuerySet) TypeNotlike(typeValue string) CustomServiceConfigQuerySet {
	return qs.w(qs.db.Where("type NOT LIKE ?", typeValue))
}

// SetAppName is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetAppName(appName string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.AppName)] = appName
	return u
}

// SetBkBizId is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetBkBizId(bkBizId int) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.BkBizId)] = bkBizId
	return u
}

// SetConfigKey is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetConfigKey(configKey string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.ConfigKey)] = configKey
	return u
}

// SetConfigLevel is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetConfigLevel(configLevel string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.ConfigLevel)] = configLevel
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetId(id int) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.Id)] = id
	return u
}

// SetMatchType is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetMatchType(matchType string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.MatchType)] = matchType
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetName(name string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.Name)] = name
	return u
}

// SetRule is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetRule(rule CustomServiceRule) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.Rule)] = rule
	return u
}

// SetType is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) SetType(typeValue string) CustomServiceConfigUpdater {
	u.fields[string(CustomServiceConfigDBSchema.Type)] = typeValue
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u CustomServiceConfigUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set CustomServiceConfigQuerySet

// ===== BEGIN of CustomServiceConfig modifiers

// CustomServiceConfigDBSchemaField describes database schema field. It requires for method 'Update'
type CustomServiceConfigDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f CustomServiceConfigDBSchemaField) String() string {
	return string(f)
}

// CustomServiceConfigDBSchema stores db field names of CustomServiceConfig
var CustomServiceConfigDBSchema = struct {
	Id          CustomServiceConfigDBSchemaField
	BkBizId     CustomServiceConfigDBSchemaField
	AppName     CustomServiceConfigDBSchemaField
	ConfigLevel CustomServiceConfigDBSchemaField
	ConfigKey   CustomServiceConfigDBSchemaField
	Type        CustomServiceConfigDBSchemaField
	Name        CustomServiceConfigDBSchemaField
	Rule        CustomServiceConfigDBSchemaField
	MatchType   CustomServiceConfigDBSchemaField
}{

	Id:          CustomServiceConfigDBSchemaField("id"),
	BkBizId:     CustomServiceConfigDBSchemaField("bk_biz_id"),
	AppName:     CustomServiceConfigDBSchemaField("app_name"),
	ConfigLevel: CustomServiceConfigDBSchemaField("config_level"),
	ConfigKey:   CustomServiceConfigDBSchemaField("config_key"),
	Type:        CustomServiceConfigDBSchemaField("type"),
	Name:        CustomServiceConfigDBSchemaField("name"),
	Rule:        CustomServiceConfigDBSchemaField("rule"),
	MatchType:   CustomServiceConfigDBSchemaField("match_type"),
}

// Update updates CustomServiceConfig fields by primary key
// nolint: dupl
func (o *CustomServiceConfig) Update(db *gorm.DB, fields ...CustomServiceConfigDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":           o.Id,
		"bk_biz_id":    o.BkBizId,
		"app_name":     o.AppName,
		"config_level": o.ConfigLevel,
		"config_key":   o.ConfigKey,
		"type":         o.Type,
		"name":         o.Name,
		"rule":         o.Rule,
		"match_type":   o.MatchType,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update CustomServiceConfig %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// CustomServiceConfigUpdater is an CustomServiceConfig updates manager
type CustomServiceConfigUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewCustomServiceConfigUpdater creates new CustomServiceConfig updater
// nolint: dupl
func NewCustomServiceConfigUpdater(db *gorm.DB) CustomServiceConfigUpdater {
	return CustomServiceConfigUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&CustomServiceConfig{}),
	}
}

// ===== END of CustomServiceConfig modifiers

// ===== END of all query sets
