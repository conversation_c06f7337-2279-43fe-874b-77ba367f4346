# <PERSON><PERSON> is pleased to support the open source community by making
# 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
# Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
# Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
# You may obtain a copy of the License at http://opensource.org/licenses/MIT
# Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
# an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.

connections:
  - dataId: 999
    token: ""
    bkBizId: 2
    bkBizName: 测试业务名称
    appId: 1
    appName: 测试应用A
    kafkaHost: 127.0.0.1:9092
    kafkaUsername:
    kafkaPassword:
    kafkaTopic: pre_calculate_test_a
    traceEsIndexName: 2_bkapm_trace_bkmonitor_production*
    traceEsHost: http://127.0.0.1:9200
    traceEsUsername: elastic
    traceEsPassword: 123456
    saveEsIndexName: pre_calculate_test_a
    saveEsHost: http://127.0.0.1:9200
    saveEsUsername: elastic
    saveEsPassword: 123456