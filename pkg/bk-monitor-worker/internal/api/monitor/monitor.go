// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package monitor

import (
	"github.com/TencentBlueKing/bk-apigateway-sdks/core/bkapi"
	"github.com/TencentBlueKing/bk-apigateway-sdks/core/define"
)

// Client for metadata
type Client struct {
	define.BkApiClient
	useBkMonitorApigw bool
}

// New monitor client
func New(useBkMonitorApigw bool, configProvider define.ClientConfigProvider, opts ...define.BkApiClientOption) (*Client, error) {
	client, err := bkapi.NewBkApiClient("monitor_v3", configProvider, opts...)
	if err != nil {
		return nil, err
	}

	return &Client{BkApiClient: client, useBkMonitorApigw: useBkMonitorApigw}, nil
}

// SearchAlert for monitor resource search_alert
func (c *Client) SearchAlert(opts ...define.OperationOption) define.Operation {
	path := "search_alert"
	return c.BkApiClient.NewOperation(bkapi.OperationConfig{
		Name:   "search_alert",
		Method: "POST",
		Path:   path,
	}, opts...)
}
