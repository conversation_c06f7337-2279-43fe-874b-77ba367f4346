// Code generated by go-queryset. DO NOT EDIT.
package bcs

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ReplaceConfigQuerySet

// ReplaceConfigQuerySet is an queryset type for ReplaceConfig
type ReplaceConfigQuerySet struct {
	db *gorm.DB
}

// NewReplaceConfigQuerySet constructs new ReplaceConfigQuerySet
func NewReplaceConfigQuerySet(db *gorm.DB) ReplaceConfigQuerySet {
	return ReplaceConfigQuerySet{
		db: db.Model(&ReplaceConfig{}),
	}
}

func (qs ReplaceConfigQuerySet) w(db *gorm.DB) ReplaceConfigQuerySet {
	return NewReplaceConfigQuerySet(db)
}

func (qs ReplaceConfigQuerySet) Select(fields ...ReplaceConfigDBSchemaField) ReplaceConfigQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ReplaceConfig) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ReplaceConfig) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) All(ret *[]ReplaceConfig) error {
	return qs.db.Find(ret).Error
}

// ClusterIdEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdEq(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterId))
}

// ClusterIdGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdGt(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterId))
}

// ClusterIdGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdGte(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterId))
}

// ClusterIdIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdIn(clusterId ...string) ReplaceConfigQuerySet {
	if len(clusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterId in ClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterId))
}

// ClusterIdIsNotNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdIsNotNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id IS NOT NULL"))
}

// ClusterIdIsNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdIsNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id IS NULL"))
}

// ClusterIdLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdLike(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id LIKE ?", clusterId))
}

// ClusterIdLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdLt(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterId))
}

// ClusterIdLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdLte(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterId))
}

// ClusterIdNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdNe(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterId))
}

// ClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdNotIn(clusterId ...string) ReplaceConfigQuerySet {
	if len(clusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterId in ClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterId))
}

// ClusterIdNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ClusterIdNotlike(clusterId string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("cluster_id NOT LIKE ?", clusterId))
}

// Count is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CustomLevelEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelEq(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level = ?", customLevel))
}

// CustomLevelGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelGt(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level > ?", customLevel))
}

// CustomLevelGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelGte(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level >= ?", customLevel))
}

// CustomLevelIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelIn(customLevel ...string) ReplaceConfigQuerySet {
	if len(customLevel) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLevel in CustomLevelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_level IN (?)", customLevel))
}

// CustomLevelIsNotNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelIsNotNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level IS NOT NULL"))
}

// CustomLevelIsNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelIsNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level IS NULL"))
}

// CustomLevelLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelLike(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level LIKE ?", customLevel))
}

// CustomLevelLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelLt(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level < ?", customLevel))
}

// CustomLevelLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelLte(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level <= ?", customLevel))
}

// CustomLevelNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelNe(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level != ?", customLevel))
}

// CustomLevelNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelNotIn(customLevel ...string) ReplaceConfigQuerySet {
	if len(customLevel) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLevel in CustomLevelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_level NOT IN (?)", customLevel))
}

// CustomLevelNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) CustomLevelNotlike(customLevel string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("custom_level NOT LIKE ?", customLevel))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) Delete() error {
	return qs.db.Delete(ReplaceConfig{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ReplaceConfig{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ReplaceConfig{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) GetUpdater() ReplaceConfigUpdater {
	return NewReplaceConfigUpdater(qs.db)
}

// IsCommonEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) IsCommonEq(isCommon bool) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("is_common = ?", isCommon))
}

// IsCommonIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) IsCommonIn(isCommon ...bool) ReplaceConfigQuerySet {
	if len(isCommon) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommon in IsCommonIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common IN (?)", isCommon))
}

// IsCommonNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) IsCommonNe(isCommon bool) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("is_common != ?", isCommon))
}

// IsCommonNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) IsCommonNotIn(isCommon ...bool) ReplaceConfigQuerySet {
	if len(isCommon) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommon in IsCommonNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common NOT IN (?)", isCommon))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) Limit(limit int) ReplaceConfigQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) Offset(offset int) ReplaceConfigQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ReplaceConfigQuerySet) One(ret *ReplaceConfig) error {
	return qs.db.First(ret).Error
}

// OrderAscByClusterId is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByClusterId() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscByCustomLevel is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByCustomLevel() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("custom_level ASC"))
}

// OrderAscByIsCommon is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByIsCommon() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("is_common ASC"))
}

// OrderAscByReplaceType is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByReplaceType() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("replace_type ASC"))
}

// OrderAscByResourceName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByResourceName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_name ASC"))
}

// OrderAscByResourceNamespace is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByResourceNamespace() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_namespace ASC"))
}

// OrderAscByResourceType is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByResourceType() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_type ASC"))
}

// OrderAscByRuleName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByRuleName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("rule_name ASC"))
}

// OrderAscBySourceName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscBySourceName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("source_name ASC"))
}

// OrderAscByTargetName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderAscByTargetName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("target_name ASC"))
}

// OrderDescByClusterId is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByClusterId() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescByCustomLevel is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByCustomLevel() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("custom_level DESC"))
}

// OrderDescByIsCommon is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByIsCommon() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("is_common DESC"))
}

// OrderDescByReplaceType is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByReplaceType() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("replace_type DESC"))
}

// OrderDescByResourceName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByResourceName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_name DESC"))
}

// OrderDescByResourceNamespace is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByResourceNamespace() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_namespace DESC"))
}

// OrderDescByResourceType is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByResourceType() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("resource_type DESC"))
}

// OrderDescByRuleName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByRuleName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("rule_name DESC"))
}

// OrderDescBySourceName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescBySourceName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("source_name DESC"))
}

// OrderDescByTargetName is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) OrderDescByTargetName() ReplaceConfigQuerySet {
	return qs.w(qs.db.Order("target_name DESC"))
}

// ReplaceTypeEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeEq(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type = ?", replaceType))
}

// ReplaceTypeGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeGt(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type > ?", replaceType))
}

// ReplaceTypeGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeGte(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type >= ?", replaceType))
}

// ReplaceTypeIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeIn(replaceType ...string) ReplaceConfigQuerySet {
	if len(replaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one replaceType in ReplaceTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("replace_type IN (?)", replaceType))
}

// ReplaceTypeLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeLike(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type LIKE ?", replaceType))
}

// ReplaceTypeLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeLt(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type < ?", replaceType))
}

// ReplaceTypeLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeLte(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type <= ?", replaceType))
}

// ReplaceTypeNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeNe(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type != ?", replaceType))
}

// ReplaceTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeNotIn(replaceType ...string) ReplaceConfigQuerySet {
	if len(replaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one replaceType in ReplaceTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("replace_type NOT IN (?)", replaceType))
}

// ReplaceTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ReplaceTypeNotlike(replaceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("replace_type NOT LIKE ?", replaceType))
}

// ResourceNameEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameEq(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name = ?", resourceName))
}

// ResourceNameGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameGt(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name > ?", resourceName))
}

// ResourceNameGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameGte(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name >= ?", resourceName))
}

// ResourceNameIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameIn(resourceName ...string) ReplaceConfigQuerySet {
	if len(resourceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceName in ResourceNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_name IN (?)", resourceName))
}

// ResourceNameIsNotNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameIsNotNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name IS NOT NULL"))
}

// ResourceNameIsNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameIsNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name IS NULL"))
}

// ResourceNameLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameLike(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name LIKE ?", resourceName))
}

// ResourceNameLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameLt(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name < ?", resourceName))
}

// ResourceNameLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameLte(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name <= ?", resourceName))
}

// ResourceNameNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameNe(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name != ?", resourceName))
}

// ResourceNameNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameNotIn(resourceName ...string) ReplaceConfigQuerySet {
	if len(resourceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceName in ResourceNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_name NOT IN (?)", resourceName))
}

// ResourceNameNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNameNotlike(resourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_name NOT LIKE ?", resourceName))
}

// ResourceNamespaceEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceEq(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace = ?", resourceNamespace))
}

// ResourceNamespaceGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceGt(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace > ?", resourceNamespace))
}

// ResourceNamespaceGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceGte(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace >= ?", resourceNamespace))
}

// ResourceNamespaceIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceIn(resourceNamespace ...string) ReplaceConfigQuerySet {
	if len(resourceNamespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceNamespace in ResourceNamespaceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_namespace IN (?)", resourceNamespace))
}

// ResourceNamespaceIsNotNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceIsNotNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace IS NOT NULL"))
}

// ResourceNamespaceIsNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceIsNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace IS NULL"))
}

// ResourceNamespaceLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceLike(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace LIKE ?", resourceNamespace))
}

// ResourceNamespaceLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceLt(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace < ?", resourceNamespace))
}

// ResourceNamespaceLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceLte(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace <= ?", resourceNamespace))
}

// ResourceNamespaceNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceNe(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace != ?", resourceNamespace))
}

// ResourceNamespaceNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceNotIn(resourceNamespace ...string) ReplaceConfigQuerySet {
	if len(resourceNamespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceNamespace in ResourceNamespaceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_namespace NOT IN (?)", resourceNamespace))
}

// ResourceNamespaceNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceNamespaceNotlike(resourceNamespace string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_namespace NOT LIKE ?", resourceNamespace))
}

// ResourceTypeEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeEq(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type = ?", resourceType))
}

// ResourceTypeGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeGt(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type > ?", resourceType))
}

// ResourceTypeGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeGte(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type >= ?", resourceType))
}

// ResourceTypeIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeIn(resourceType ...string) ReplaceConfigQuerySet {
	if len(resourceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceType in ResourceTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_type IN (?)", resourceType))
}

// ResourceTypeIsNotNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeIsNotNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type IS NOT NULL"))
}

// ResourceTypeIsNull is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeIsNull() ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type IS NULL"))
}

// ResourceTypeLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeLike(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type LIKE ?", resourceType))
}

// ResourceTypeLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeLt(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type < ?", resourceType))
}

// ResourceTypeLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeLte(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type <= ?", resourceType))
}

// ResourceTypeNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeNe(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type != ?", resourceType))
}

// ResourceTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeNotIn(resourceType ...string) ReplaceConfigQuerySet {
	if len(resourceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one resourceType in ResourceTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("resource_type NOT IN (?)", resourceType))
}

// ResourceTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) ResourceTypeNotlike(resourceType string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("resource_type NOT LIKE ?", resourceType))
}

// RuleNameEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameEq(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name = ?", ruleName))
}

// RuleNameGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameGt(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name > ?", ruleName))
}

// RuleNameGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameGte(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name >= ?", ruleName))
}

// RuleNameIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameIn(ruleName ...string) ReplaceConfigQuerySet {
	if len(ruleName) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleName in RuleNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_name IN (?)", ruleName))
}

// RuleNameLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameLike(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name LIKE ?", ruleName))
}

// RuleNameLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameLt(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name < ?", ruleName))
}

// RuleNameLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameLte(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name <= ?", ruleName))
}

// RuleNameNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameNe(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name != ?", ruleName))
}

// RuleNameNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameNotIn(ruleName ...string) ReplaceConfigQuerySet {
	if len(ruleName) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleName in RuleNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_name NOT IN (?)", ruleName))
}

// RuleNameNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) RuleNameNotlike(ruleName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("rule_name NOT LIKE ?", ruleName))
}

// SourceNameEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameEq(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name = ?", sourceName))
}

// SourceNameGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameGt(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name > ?", sourceName))
}

// SourceNameGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameGte(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name >= ?", sourceName))
}

// SourceNameIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameIn(sourceName ...string) ReplaceConfigQuerySet {
	if len(sourceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceName in SourceNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_name IN (?)", sourceName))
}

// SourceNameLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameLike(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name LIKE ?", sourceName))
}

// SourceNameLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameLt(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name < ?", sourceName))
}

// SourceNameLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameLte(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name <= ?", sourceName))
}

// SourceNameNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameNe(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name != ?", sourceName))
}

// SourceNameNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameNotIn(sourceName ...string) ReplaceConfigQuerySet {
	if len(sourceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceName in SourceNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_name NOT IN (?)", sourceName))
}

// SourceNameNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) SourceNameNotlike(sourceName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("source_name NOT LIKE ?", sourceName))
}

// TargetNameEq is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameEq(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name = ?", targetName))
}

// TargetNameGt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameGt(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name > ?", targetName))
}

// TargetNameGte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameGte(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name >= ?", targetName))
}

// TargetNameIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameIn(targetName ...string) ReplaceConfigQuerySet {
	if len(targetName) == 0 {
		qs.db.AddError(errors.New("must at least pass one targetName in TargetNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("target_name IN (?)", targetName))
}

// TargetNameLike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameLike(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name LIKE ?", targetName))
}

// TargetNameLt is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameLt(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name < ?", targetName))
}

// TargetNameLte is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameLte(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name <= ?", targetName))
}

// TargetNameNe is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameNe(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name != ?", targetName))
}

// TargetNameNotIn is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameNotIn(targetName ...string) ReplaceConfigQuerySet {
	if len(targetName) == 0 {
		qs.db.AddError(errors.New("must at least pass one targetName in TargetNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("target_name NOT IN (?)", targetName))
}

// TargetNameNotlike is an autogenerated method
// nolint: dupl
func (qs ReplaceConfigQuerySet) TargetNameNotlike(targetName string) ReplaceConfigQuerySet {
	return qs.w(qs.db.Where("target_name NOT LIKE ?", targetName))
}

// SetClusterId is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetClusterId(clusterId *string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.ClusterId)] = clusterId
	return u
}

// SetCustomLevel is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetCustomLevel(customLevel *string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.CustomLevel)] = customLevel
	return u
}

// SetIsCommon is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetIsCommon(isCommon bool) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.IsCommon)] = isCommon
	return u
}

// SetReplaceType is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetReplaceType(replaceType string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.ReplaceType)] = replaceType
	return u
}

// SetResourceName is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetResourceName(resourceName *string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.ResourceName)] = resourceName
	return u
}

// SetResourceNamespace is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetResourceNamespace(resourceNamespace *string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.ResourceNamespace)] = resourceNamespace
	return u
}

// SetResourceType is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetResourceType(resourceType *string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.ResourceType)] = resourceType
	return u
}

// SetRuleName is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetRuleName(ruleName string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.RuleName)] = ruleName
	return u
}

// SetSourceName is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetSourceName(sourceName string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.SourceName)] = sourceName
	return u
}

// SetTargetName is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) SetTargetName(targetName string) ReplaceConfigUpdater {
	u.fields[string(ReplaceConfigDBSchema.TargetName)] = targetName
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ReplaceConfigUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ReplaceConfigQuerySet

// ===== BEGIN of ReplaceConfig modifiers

// ReplaceConfigDBSchemaField describes database schema field. It requires for method 'Update'
type ReplaceConfigDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ReplaceConfigDBSchemaField) String() string {
	return string(f)
}

// ReplaceConfigDBSchema stores db field names of ReplaceConfig
var ReplaceConfigDBSchema = struct {
	RuleName          ReplaceConfigDBSchemaField
	IsCommon          ReplaceConfigDBSchemaField
	SourceName        ReplaceConfigDBSchemaField
	TargetName        ReplaceConfigDBSchemaField
	ReplaceType       ReplaceConfigDBSchemaField
	CustomLevel       ReplaceConfigDBSchemaField
	ClusterId         ReplaceConfigDBSchemaField
	ResourceName      ReplaceConfigDBSchemaField
	ResourceType      ReplaceConfigDBSchemaField
	ResourceNamespace ReplaceConfigDBSchemaField
}{

	RuleName:          ReplaceConfigDBSchemaField("rule_name"),
	IsCommon:          ReplaceConfigDBSchemaField("is_common"),
	SourceName:        ReplaceConfigDBSchemaField("source_name"),
	TargetName:        ReplaceConfigDBSchemaField("target_name"),
	ReplaceType:       ReplaceConfigDBSchemaField("replace_type"),
	CustomLevel:       ReplaceConfigDBSchemaField("custom_level"),
	ClusterId:         ReplaceConfigDBSchemaField("cluster_id"),
	ResourceName:      ReplaceConfigDBSchemaField("resource_name"),
	ResourceType:      ReplaceConfigDBSchemaField("resource_type"),
	ResourceNamespace: ReplaceConfigDBSchemaField("resource_namespace"),
}

// Update updates ReplaceConfig fields by primary key
// nolint: dupl
func (o *ReplaceConfig) Update(db *gorm.DB, fields ...ReplaceConfigDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"rule_name":          o.RuleName,
		"is_common":          o.IsCommon,
		"source_name":        o.SourceName,
		"target_name":        o.TargetName,
		"replace_type":       o.ReplaceType,
		"custom_level":       o.CustomLevel,
		"cluster_id":         o.ClusterId,
		"resource_name":      o.ResourceName,
		"resource_type":      o.ResourceType,
		"resource_namespace": o.ResourceNamespace,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ReplaceConfig %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ReplaceConfigUpdater is an ReplaceConfig updates manager
type ReplaceConfigUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewReplaceConfigUpdater creates new ReplaceConfig updater
// nolint: dupl
func NewReplaceConfigUpdater(db *gorm.DB) ReplaceConfigUpdater {
	return ReplaceConfigUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ReplaceConfig{}),
	}
}

// ===== END of ReplaceConfig modifiers

// ===== END of all query sets
