// Code generated by go-queryset. DO NOT EDIT.
package bcs

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ServiceMonitorInfoQuerySet

// ServiceMonitorInfoQuerySet is an queryset type for ServiceMonitorInfo
type ServiceMonitorInfoQuerySet struct {
	db *gorm.DB
}

// NewServiceMonitorInfoQuerySet constructs new ServiceMonitorInfoQuerySet
func NewServiceMonitorInfoQuerySet(db *gorm.DB) ServiceMonitorInfoQuerySet {
	return ServiceMonitorInfoQuerySet{
		db: db.Model(&ServiceMonitorInfo{}),
	}
}

func (qs ServiceMonitorInfoQuerySet) w(db *gorm.DB) ServiceMonitorInfoQuerySet {
	return NewServiceMonitorInfoQuerySet(db)
}

func (qs ServiceMonitorInfoQuerySet) Select(fields ...ServiceMonitorInfoDBSchemaField) ServiceMonitorInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ServiceMonitorInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ServiceMonitorInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) All(ret *[]ServiceMonitorInfo) error {
	return qs.db.Find(ret).Error
}

// BkDataIdEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdEq(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataId))
}

// BkDataIdGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdGt(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataId))
}

// BkDataIdGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdGte(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataId))
}

// BkDataIdIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdIn(bkDataId ...uint) ServiceMonitorInfoQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataId))
}

// BkDataIdLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdLt(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataId))
}

// BkDataIdLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdLte(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataId))
}

// BkDataIdNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdNe(bkDataId uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataId))
}

// BkDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) BkDataIdNotIn(bkDataId ...uint) ServiceMonitorInfoQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataId))
}

// ClusterIDEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDEq(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterID))
}

// ClusterIDGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDGt(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterID))
}

// ClusterIDGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDGte(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterID))
}

// ClusterIDIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDIn(clusterID ...string) ServiceMonitorInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterID))
}

// ClusterIDLike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDLike(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id LIKE ?", clusterID))
}

// ClusterIDLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDLt(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterID))
}

// ClusterIDLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDLte(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterID))
}

// ClusterIDNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDNe(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterID))
}

// ClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDNotIn(clusterID ...string) ServiceMonitorInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterID))
}

// ClusterIDNotlike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ClusterIDNotlike(clusterID string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id NOT LIKE ?", clusterID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) Delete() error {
	return qs.db.Delete(ServiceMonitorInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ServiceMonitorInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ServiceMonitorInfo{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) GetUpdater() ServiceMonitorInfoUpdater {
	return NewServiceMonitorInfoUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdEq(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdGt(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdGte(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdIn(id ...uint) ServiceMonitorInfoQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdLt(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdLte(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdNe(id uint) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IdNotIn(id ...uint) ServiceMonitorInfoQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// IsCommonDataIdEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCommonDataIdEq(isCommonDataId bool) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_common_data_id = ?", isCommonDataId))
}

// IsCommonDataIdIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCommonDataIdIn(isCommonDataId ...bool) ServiceMonitorInfoQuerySet {
	if len(isCommonDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommonDataId in IsCommonDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common_data_id IN (?)", isCommonDataId))
}

// IsCommonDataIdNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCommonDataIdNe(isCommonDataId bool) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_common_data_id != ?", isCommonDataId))
}

// IsCommonDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCommonDataIdNotIn(isCommonDataId ...bool) ServiceMonitorInfoQuerySet {
	if len(isCommonDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommonDataId in IsCommonDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common_data_id NOT IN (?)", isCommonDataId))
}

// IsCustomResourceEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCustomResourceEq(isCustomResource bool) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_custom_resource = ?", isCustomResource))
}

// IsCustomResourceIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCustomResourceIn(isCustomResource ...bool) ServiceMonitorInfoQuerySet {
	if len(isCustomResource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomResource in IsCustomResourceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_resource IN (?)", isCustomResource))
}

// IsCustomResourceNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCustomResourceNe(isCustomResource bool) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_custom_resource != ?", isCustomResource))
}

// IsCustomResourceNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) IsCustomResourceNotIn(isCustomResource ...bool) ServiceMonitorInfoQuerySet {
	if len(isCustomResource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomResource in IsCustomResourceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_resource NOT IN (?)", isCustomResource))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) Limit(limit int) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameEq(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameGt(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameGte(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameIn(name ...string) ServiceMonitorInfoQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameLike(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameLt(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameLte(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameNe(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameNotIn(name ...string) ServiceMonitorInfoQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NameNotlike(name string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// NamespaceEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceEq(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace = ?", namespace))
}

// NamespaceGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceGt(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace > ?", namespace))
}

// NamespaceGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceGte(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace >= ?", namespace))
}

// NamespaceIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceIn(namespace ...string) ServiceMonitorInfoQuerySet {
	if len(namespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one namespace in NamespaceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("namespace IN (?)", namespace))
}

// NamespaceLike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceLike(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace LIKE ?", namespace))
}

// NamespaceLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceLt(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace < ?", namespace))
}

// NamespaceLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceLte(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace <= ?", namespace))
}

// NamespaceNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceNe(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace != ?", namespace))
}

// NamespaceNotIn is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceNotIn(namespace ...string) ServiceMonitorInfoQuerySet {
	if len(namespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one namespace in NamespaceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("namespace NOT IN (?)", namespace))
}

// NamespaceNotlike is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) NamespaceNotlike(namespace string) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace NOT LIKE ?", namespace))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) Offset(offset int) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ServiceMonitorInfoQuerySet) One(ret *ServiceMonitorInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataId is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByBkDataId() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByClusterID is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByClusterID() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscById() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByIsCommonDataId is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByIsCommonDataId() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_common_data_id ASC"))
}

// OrderAscByIsCustomResource is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByIsCustomResource() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_custom_resource ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByName() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByNamespace is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByNamespace() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("namespace ASC"))
}

// OrderAscByRecordCreateTime is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByRecordCreateTime() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("record_create_time ASC"))
}

// OrderAscByResourceCreateTime is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderAscByResourceCreateTime() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("resource_create_time ASC"))
}

// OrderDescByBkDataId is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByBkDataId() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByClusterID is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByClusterID() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescById() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByIsCommonDataId is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByIsCommonDataId() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_common_data_id DESC"))
}

// OrderDescByIsCustomResource is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByIsCustomResource() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_custom_resource DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByName() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByNamespace is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByNamespace() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("namespace DESC"))
}

// OrderDescByRecordCreateTime is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByRecordCreateTime() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("record_create_time DESC"))
}

// OrderDescByResourceCreateTime is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) OrderDescByResourceCreateTime() ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Order("resource_create_time DESC"))
}

// RecordCreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeEq(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time = ?", recordCreateTime))
}

// RecordCreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeGt(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time > ?", recordCreateTime))
}

// RecordCreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeGte(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time >= ?", recordCreateTime))
}

// RecordCreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeLt(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time < ?", recordCreateTime))
}

// RecordCreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeLte(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time <= ?", recordCreateTime))
}

// RecordCreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) RecordCreateTimeNe(recordCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time != ?", recordCreateTime))
}

// ResourceCreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeEq(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time = ?", resourceCreateTime))
}

// ResourceCreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeGt(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time > ?", resourceCreateTime))
}

// ResourceCreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeGte(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time >= ?", resourceCreateTime))
}

// ResourceCreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeLt(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time < ?", resourceCreateTime))
}

// ResourceCreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeLte(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time <= ?", resourceCreateTime))
}

// ResourceCreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ServiceMonitorInfoQuerySet) ResourceCreateTimeNe(resourceCreateTime time.Time) ServiceMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time != ?", resourceCreateTime))
}

// SetBkDataId is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetBkDataId(bkDataId uint) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.BkDataId)] = bkDataId
	return u
}

// SetClusterID is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetClusterID(clusterID string) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.ClusterID)] = clusterID
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetId(id uint) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.Id)] = id
	return u
}

// SetIsCommonDataId is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetIsCommonDataId(isCommonDataId bool) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.IsCommonDataId)] = isCommonDataId
	return u
}

// SetIsCustomResource is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetIsCustomResource(isCustomResource bool) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.IsCustomResource)] = isCustomResource
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetName(name string) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.Name)] = name
	return u
}

// SetNamespace is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetNamespace(namespace string) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.Namespace)] = namespace
	return u
}

// SetRecordCreateTime is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetRecordCreateTime(recordCreateTime time.Time) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.RecordCreateTime)] = recordCreateTime
	return u
}

// SetResourceCreateTime is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) SetResourceCreateTime(resourceCreateTime time.Time) ServiceMonitorInfoUpdater {
	u.fields[string(ServiceMonitorInfoDBSchema.ResourceCreateTime)] = resourceCreateTime
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ServiceMonitorInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ServiceMonitorInfoQuerySet

// ===== BEGIN of ServiceMonitorInfo modifiers

// ServiceMonitorInfoDBSchemaField describes database schema field. It requires for method 'Update'
type ServiceMonitorInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ServiceMonitorInfoDBSchemaField) String() string {
	return string(f)
}

// ServiceMonitorInfoDBSchema stores db field names of ServiceMonitorInfo
var ServiceMonitorInfoDBSchema = struct {
	Id                 ServiceMonitorInfoDBSchemaField
	ClusterID          ServiceMonitorInfoDBSchemaField
	Namespace          ServiceMonitorInfoDBSchemaField
	Name               ServiceMonitorInfoDBSchemaField
	BkDataId           ServiceMonitorInfoDBSchemaField
	IsCustomResource   ServiceMonitorInfoDBSchemaField
	IsCommonDataId     ServiceMonitorInfoDBSchemaField
	RecordCreateTime   ServiceMonitorInfoDBSchemaField
	ResourceCreateTime ServiceMonitorInfoDBSchemaField
}{

	Id:                 ServiceMonitorInfoDBSchemaField("id"),
	ClusterID:          ServiceMonitorInfoDBSchemaField("cluster_id"),
	Namespace:          ServiceMonitorInfoDBSchemaField("namespace"),
	Name:               ServiceMonitorInfoDBSchemaField("name"),
	BkDataId:           ServiceMonitorInfoDBSchemaField("bk_data_id"),
	IsCustomResource:   ServiceMonitorInfoDBSchemaField("is_custom_resource"),
	IsCommonDataId:     ServiceMonitorInfoDBSchemaField("is_common_data_id"),
	RecordCreateTime:   ServiceMonitorInfoDBSchemaField("record_create_time"),
	ResourceCreateTime: ServiceMonitorInfoDBSchemaField("resource_create_time"),
}

// Update updates ServiceMonitorInfo fields by primary key
// nolint: dupl
func (o *ServiceMonitorInfo) Update(db *gorm.DB, fields ...ServiceMonitorInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                   o.Id,
		"cluster_id":           o.ClusterID,
		"namespace":            o.Namespace,
		"name":                 o.Name,
		"bk_data_id":           o.BkDataId,
		"is_custom_resource":   o.IsCustomResource,
		"is_common_data_id":    o.IsCommonDataId,
		"record_create_time":   o.RecordCreateTime,
		"resource_create_time": o.ResourceCreateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ServiceMonitorInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ServiceMonitorInfoUpdater is an ServiceMonitorInfo updates manager
type ServiceMonitorInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewServiceMonitorInfoUpdater creates new ServiceMonitorInfo updater
// nolint: dupl
func NewServiceMonitorInfoUpdater(db *gorm.DB) ServiceMonitorInfoUpdater {
	return ServiceMonitorInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ServiceMonitorInfo{}),
	}
}

// ===== END of ServiceMonitorInfo modifiers

// ===== END of all query sets
