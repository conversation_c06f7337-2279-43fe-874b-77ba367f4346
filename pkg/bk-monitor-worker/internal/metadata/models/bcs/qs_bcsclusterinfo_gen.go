// Code generated by go-queryset. DO NOT EDIT.
package bcs

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set BCSClusterInfoQuerySet

// BCSClusterInfoQuerySet is an queryset type for BCSClusterInfo
type BCSClusterInfoQuerySet struct {
	db *gorm.DB
}

// NewBCSClusterInfoQuerySet constructs new BCSClusterInfoQuerySet
func NewBCSClusterInfoQuerySet(db *gorm.DB) BCSClusterInfoQuerySet {
	return BCSClusterInfoQuerySet{
		db: db.Model(&BCSClusterInfo{}),
	}
}

func (qs BCSClusterInfoQuerySet) w(db *gorm.DB) BCSClusterInfoQuerySet {
	return NewBCSClusterInfoQuerySet(db)
}

func (qs BCSClusterInfoQuerySet) Select(fields ...BCSClusterInfoDBSchemaField) BCSClusterInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *BCSClusterInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *BCSClusterInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) All(ret *[]BCSClusterInfo) error {
	return qs.db.Find(ret).Error
}

// ApiKeyContentEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentEq(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content = ?", apiKeyContent))
}

// ApiKeyContentGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentGt(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content > ?", apiKeyContent))
}

// ApiKeyContentGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentGte(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content >= ?", apiKeyContent))
}

// ApiKeyContentIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentIn(apiKeyContent ...string) BCSClusterInfoQuerySet {
	if len(apiKeyContent) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyContent in ApiKeyContentIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_content IN (?)", apiKeyContent))
}

// ApiKeyContentLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentLike(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content LIKE ?", apiKeyContent))
}

// ApiKeyContentLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentLt(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content < ?", apiKeyContent))
}

// ApiKeyContentLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentLte(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content <= ?", apiKeyContent))
}

// ApiKeyContentNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentNe(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content != ?", apiKeyContent))
}

// ApiKeyContentNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentNotIn(apiKeyContent ...string) BCSClusterInfoQuerySet {
	if len(apiKeyContent) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyContent in ApiKeyContentNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_content NOT IN (?)", apiKeyContent))
}

// ApiKeyContentNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyContentNotlike(apiKeyContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_content NOT LIKE ?", apiKeyContent))
}

// ApiKeyPrefixEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixEq(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix = ?", apiKeyPrefix))
}

// ApiKeyPrefixGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixGt(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix > ?", apiKeyPrefix))
}

// ApiKeyPrefixGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixGte(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix >= ?", apiKeyPrefix))
}

// ApiKeyPrefixIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixIn(apiKeyPrefix ...string) BCSClusterInfoQuerySet {
	if len(apiKeyPrefix) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyPrefix in ApiKeyPrefixIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_prefix IN (?)", apiKeyPrefix))
}

// ApiKeyPrefixLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixLike(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix LIKE ?", apiKeyPrefix))
}

// ApiKeyPrefixLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixLt(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix < ?", apiKeyPrefix))
}

// ApiKeyPrefixLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixLte(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix <= ?", apiKeyPrefix))
}

// ApiKeyPrefixNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixNe(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix != ?", apiKeyPrefix))
}

// ApiKeyPrefixNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixNotIn(apiKeyPrefix ...string) BCSClusterInfoQuerySet {
	if len(apiKeyPrefix) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyPrefix in ApiKeyPrefixNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_prefix NOT IN (?)", apiKeyPrefix))
}

// ApiKeyPrefixNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyPrefixNotlike(apiKeyPrefix string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_prefix NOT LIKE ?", apiKeyPrefix))
}

// ApiKeyTypeEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeEq(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type = ?", apiKeyType))
}

// ApiKeyTypeGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeGt(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type > ?", apiKeyType))
}

// ApiKeyTypeGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeGte(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type >= ?", apiKeyType))
}

// ApiKeyTypeIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeIn(apiKeyType ...string) BCSClusterInfoQuerySet {
	if len(apiKeyType) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyType in ApiKeyTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_type IN (?)", apiKeyType))
}

// ApiKeyTypeLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeLike(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type LIKE ?", apiKeyType))
}

// ApiKeyTypeLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeLt(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type < ?", apiKeyType))
}

// ApiKeyTypeLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeLte(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type <= ?", apiKeyType))
}

// ApiKeyTypeNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeNe(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type != ?", apiKeyType))
}

// ApiKeyTypeNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeNotIn(apiKeyType ...string) BCSClusterInfoQuerySet {
	if len(apiKeyType) == 0 {
		qs.db.AddError(errors.New("must at least pass one apiKeyType in ApiKeyTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("api_key_type NOT IN (?)", apiKeyType))
}

// ApiKeyTypeNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ApiKeyTypeNotlike(apiKeyType string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("api_key_type NOT LIKE ?", apiKeyType))
}

// BCSApiClusterIdEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdEq(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id = ?", bCSApiClusterId))
}

// BCSApiClusterIdGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdGt(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id > ?", bCSApiClusterId))
}

// BCSApiClusterIdGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdGte(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id >= ?", bCSApiClusterId))
}

// BCSApiClusterIdIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdIn(bCSApiClusterId ...string) BCSClusterInfoQuerySet {
	if len(bCSApiClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bCSApiClusterId in BCSApiClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bcs_api_cluster_id IN (?)", bCSApiClusterId))
}

// BCSApiClusterIdLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdLike(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id LIKE ?", bCSApiClusterId))
}

// BCSApiClusterIdLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdLt(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id < ?", bCSApiClusterId))
}

// BCSApiClusterIdLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdLte(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id <= ?", bCSApiClusterId))
}

// BCSApiClusterIdNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdNe(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id != ?", bCSApiClusterId))
}

// BCSApiClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdNotIn(bCSApiClusterId ...string) BCSClusterInfoQuerySet {
	if len(bCSApiClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bCSApiClusterId in BCSApiClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bcs_api_cluster_id NOT IN (?)", bCSApiClusterId))
}

// BCSApiClusterIdNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BCSApiClusterIdNotlike(bCSApiClusterId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bcs_api_cluster_id NOT LIKE ?", bCSApiClusterId))
}

// BkBizIdEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdEq(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizId))
}

// BkBizIdGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdGt(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizId))
}

// BkBizIdGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdGte(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizId))
}

// BkBizIdIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdIn(bkBizId ...int) BCSClusterInfoQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizId))
}

// BkBizIdLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdLt(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizId))
}

// BkBizIdLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdLte(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizId))
}

// BkBizIdNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdNe(bkBizId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizId))
}

// BkBizIdNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkBizIdNotIn(bkBizId ...int) BCSClusterInfoQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizId))
}

// BkCloudIdEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdEq(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id = ?", bkCloudId))
}

// BkCloudIdGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdGt(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id > ?", bkCloudId))
}

// BkCloudIdGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdGte(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id >= ?", bkCloudId))
}

// BkCloudIdIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdIn(bkCloudId ...int) BCSClusterInfoQuerySet {
	if len(bkCloudId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkCloudId in BkCloudIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_cloud_id IN (?)", bkCloudId))
}

// BkCloudIdIsNotNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdIsNotNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id IS NOT NULL"))
}

// BkCloudIdIsNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdIsNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id IS NULL"))
}

// BkCloudIdLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdLt(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id < ?", bkCloudId))
}

// BkCloudIdLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdLte(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id <= ?", bkCloudId))
}

// BkCloudIdNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdNe(bkCloudId int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id != ?", bkCloudId))
}

// BkCloudIdNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkCloudIdNotIn(bkCloudId ...int) BCSClusterInfoQuerySet {
	if len(bkCloudId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkCloudId in BkCloudIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_cloud_id NOT IN (?)", bkCloudId))
}

// BkEnvEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvEq(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env = ?", bkEnv))
}

// BkEnvGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvGt(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env > ?", bkEnv))
}

// BkEnvGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvGte(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env >= ?", bkEnv))
}

// BkEnvIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvIn(bkEnv ...string) BCSClusterInfoQuerySet {
	if len(bkEnv) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkEnv in BkEnvIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_env IN (?)", bkEnv))
}

// BkEnvIsNotNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvIsNotNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env IS NOT NULL"))
}

// BkEnvIsNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvIsNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env IS NULL"))
}

// BkEnvLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvLike(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env LIKE ?", bkEnv))
}

// BkEnvLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvLt(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env < ?", bkEnv))
}

// BkEnvLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvLte(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env <= ?", bkEnv))
}

// BkEnvNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvNe(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env != ?", bkEnv))
}

// BkEnvNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvNotIn(bkEnv ...string) BCSClusterInfoQuerySet {
	if len(bkEnv) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkEnv in BkEnvNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_env NOT IN (?)", bkEnv))
}

// BkEnvNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) BkEnvNotlike(bkEnv string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("bk_env NOT LIKE ?", bkEnv))
}

// CertContentEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentEq(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content = ?", certContent))
}

// CertContentGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentGt(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content > ?", certContent))
}

// CertContentGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentGte(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content >= ?", certContent))
}

// CertContentIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentIn(certContent ...string) BCSClusterInfoQuerySet {
	if len(certContent) == 0 {
		qs.db.AddError(errors.New("must at least pass one certContent in CertContentIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cert_content IN (?)", certContent))
}

// CertContentIsNotNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentIsNotNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content IS NOT NULL"))
}

// CertContentIsNull is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentIsNull() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content IS NULL"))
}

// CertContentLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentLike(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content LIKE ?", certContent))
}

// CertContentLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentLt(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content < ?", certContent))
}

// CertContentLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentLte(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content <= ?", certContent))
}

// CertContentNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentNe(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content != ?", certContent))
}

// CertContentNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentNotIn(certContent ...string) BCSClusterInfoQuerySet {
	if len(certContent) == 0 {
		qs.db.AddError(errors.New("must at least pass one certContent in CertContentNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cert_content NOT IN (?)", certContent))
}

// CertContentNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CertContentNotlike(certContent string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cert_content NOT LIKE ?", certContent))
}

// ClusterIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDEq(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterID))
}

// ClusterIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDGt(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterID))
}

// ClusterIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDGte(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterID))
}

// ClusterIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDIn(clusterID ...string) BCSClusterInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterID))
}

// ClusterIDLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDLike(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id LIKE ?", clusterID))
}

// ClusterIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDLt(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterID))
}

// ClusterIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDLte(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterID))
}

// ClusterIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDNe(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterID))
}

// ClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDNotIn(clusterID ...string) BCSClusterInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterID))
}

// ClusterIDNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ClusterIDNotlike(clusterID string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id NOT LIKE ?", clusterID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeEq(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeGt(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeGte(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeLt(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeLte(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreateTimeNe(createTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorEq(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorGt(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorGte(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorIn(creator ...string) BCSClusterInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorLike(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorLt(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorLte(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorNe(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorNotIn(creator ...string) BCSClusterInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CreatorNotlike(creator string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// CustomEventDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDEq(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID = ?", customEventDataID))
}

// CustomEventDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDGt(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID > ?", customEventDataID))
}

// CustomEventDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDGte(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID >= ?", customEventDataID))
}

// CustomEventDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDIn(customEventDataID ...uint) BCSClusterInfoQuerySet {
	if len(customEventDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customEventDataID in CustomEventDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomEventDataID IN (?)", customEventDataID))
}

// CustomEventDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDLt(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID < ?", customEventDataID))
}

// CustomEventDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDLte(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID <= ?", customEventDataID))
}

// CustomEventDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDNe(customEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomEventDataID != ?", customEventDataID))
}

// CustomEventDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomEventDataIDNotIn(customEventDataID ...uint) BCSClusterInfoQuerySet {
	if len(customEventDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customEventDataID in CustomEventDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomEventDataID NOT IN (?)", customEventDataID))
}

// CustomLogDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDEq(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID = ?", customLogDataID))
}

// CustomLogDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDGt(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID > ?", customLogDataID))
}

// CustomLogDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDGte(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID >= ?", customLogDataID))
}

// CustomLogDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDIn(customLogDataID ...uint) BCSClusterInfoQuerySet {
	if len(customLogDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLogDataID in CustomLogDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomLogDataID IN (?)", customLogDataID))
}

// CustomLogDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDLt(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID < ?", customLogDataID))
}

// CustomLogDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDLte(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID <= ?", customLogDataID))
}

// CustomLogDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDNe(customLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomLogDataID != ?", customLogDataID))
}

// CustomLogDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomLogDataIDNotIn(customLogDataID ...uint) BCSClusterInfoQuerySet {
	if len(customLogDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLogDataID in CustomLogDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomLogDataID NOT IN (?)", customLogDataID))
}

// CustomMetricDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDEq(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID = ?", customMetricDataID))
}

// CustomMetricDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDGt(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID > ?", customMetricDataID))
}

// CustomMetricDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDGte(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID >= ?", customMetricDataID))
}

// CustomMetricDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDIn(customMetricDataID ...uint) BCSClusterInfoQuerySet {
	if len(customMetricDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customMetricDataID in CustomMetricDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomMetricDataID IN (?)", customMetricDataID))
}

// CustomMetricDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDLt(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID < ?", customMetricDataID))
}

// CustomMetricDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDLte(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID <= ?", customMetricDataID))
}

// CustomMetricDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDNe(customMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("CustomMetricDataID != ?", customMetricDataID))
}

// CustomMetricDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) CustomMetricDataIDNotIn(customMetricDataID ...uint) BCSClusterInfoQuerySet {
	if len(customMetricDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one customMetricDataID in CustomMetricDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("CustomMetricDataID NOT IN (?)", customMetricDataID))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) Delete() error {
	return qs.db.Delete(BCSClusterInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(BCSClusterInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(BCSClusterInfo{})
	return db.RowsAffected, db.Error
}

// DomainNameEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameEq(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name = ?", domainName))
}

// DomainNameGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameGt(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name > ?", domainName))
}

// DomainNameGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameGte(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name >= ?", domainName))
}

// DomainNameIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameIn(domainName ...string) BCSClusterInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name IN (?)", domainName))
}

// DomainNameLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameLike(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name LIKE ?", domainName))
}

// DomainNameLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameLt(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name < ?", domainName))
}

// DomainNameLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameLte(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name <= ?", domainName))
}

// DomainNameNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameNe(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name != ?", domainName))
}

// DomainNameNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameNotIn(domainName ...string) BCSClusterInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name NOT IN (?)", domainName))
}

// DomainNameNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) DomainNameNotlike(domainName string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name NOT LIKE ?", domainName))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) GetUpdater() BCSClusterInfoUpdater {
	return NewBCSClusterInfoUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDEq(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDGt(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDGte(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDIn(ID ...uint) BCSClusterInfoQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDLt(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDLte(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDNe(ID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IDNotIn(ID ...uint) BCSClusterInfoQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// IsDeletedAllowViewEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsDeletedAllowViewEq(isDeletedAllowView bool) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_deleted_allow_view = ?", isDeletedAllowView))
}

// IsDeletedAllowViewIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsDeletedAllowViewIn(isDeletedAllowView ...bool) BCSClusterInfoQuerySet {
	if len(isDeletedAllowView) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeletedAllowView in IsDeletedAllowViewIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted_allow_view IN (?)", isDeletedAllowView))
}

// IsDeletedAllowViewNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsDeletedAllowViewNe(isDeletedAllowView bool) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_deleted_allow_view != ?", isDeletedAllowView))
}

// IsDeletedAllowViewNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsDeletedAllowViewNotIn(isDeletedAllowView ...bool) BCSClusterInfoQuerySet {
	if len(isDeletedAllowView) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeletedAllowView in IsDeletedAllowViewNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted_allow_view NOT IN (?)", isDeletedAllowView))
}

// IsSkipSslVerifyEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsSkipSslVerifyEq(isSkipSslVerify bool) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_skip_ssl_verify = ?", isSkipSslVerify))
}

// IsSkipSslVerifyIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsSkipSslVerifyIn(isSkipSslVerify ...bool) BCSClusterInfoQuerySet {
	if len(isSkipSslVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSkipSslVerify in IsSkipSslVerifyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_skip_ssl_verify IN (?)", isSkipSslVerify))
}

// IsSkipSslVerifyNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsSkipSslVerifyNe(isSkipSslVerify bool) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_skip_ssl_verify != ?", isSkipSslVerify))
}

// IsSkipSslVerifyNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) IsSkipSslVerifyNotIn(isSkipSslVerify ...bool) BCSClusterInfoQuerySet {
	if len(isSkipSslVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSkipSslVerify in IsSkipSslVerifyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_skip_ssl_verify NOT IN (?)", isSkipSslVerify))
}

// K8sEventDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDEq(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID = ?", k8sEventDataID))
}

// K8sEventDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDGt(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID > ?", k8sEventDataID))
}

// K8sEventDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDGte(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID >= ?", k8sEventDataID))
}

// K8sEventDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDIn(k8sEventDataID ...uint) BCSClusterInfoQuerySet {
	if len(k8sEventDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one k8sEventDataID in K8sEventDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("K8sEventDataID IN (?)", k8sEventDataID))
}

// K8sEventDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDLt(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID < ?", k8sEventDataID))
}

// K8sEventDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDLte(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID <= ?", k8sEventDataID))
}

// K8sEventDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDNe(k8sEventDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sEventDataID != ?", k8sEventDataID))
}

// K8sEventDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sEventDataIDNotIn(k8sEventDataID ...uint) BCSClusterInfoQuerySet {
	if len(k8sEventDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one k8sEventDataID in K8sEventDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("K8sEventDataID NOT IN (?)", k8sEventDataID))
}

// K8sMetricDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDEq(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID = ?", k8sMetricDataID))
}

// K8sMetricDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDGt(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID > ?", k8sMetricDataID))
}

// K8sMetricDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDGte(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID >= ?", k8sMetricDataID))
}

// K8sMetricDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDIn(k8sMetricDataID ...uint) BCSClusterInfoQuerySet {
	if len(k8sMetricDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one k8sMetricDataID in K8sMetricDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("K8sMetricDataID IN (?)", k8sMetricDataID))
}

// K8sMetricDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDLt(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID < ?", k8sMetricDataID))
}

// K8sMetricDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDLte(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID <= ?", k8sMetricDataID))
}

// K8sMetricDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDNe(k8sMetricDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("K8sMetricDataID != ?", k8sMetricDataID))
}

// K8sMetricDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) K8sMetricDataIDNotIn(k8sMetricDataID ...uint) BCSClusterInfoQuerySet {
	if len(k8sMetricDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one k8sMetricDataID in K8sMetricDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("K8sMetricDataID NOT IN (?)", k8sMetricDataID))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeEq(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeGt(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeGte(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeLt(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeLte(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyTimeNe(lastModifyTime time.Time) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserEq(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserGt(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserGte(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserIn(lastModifyUser ...string) BCSClusterInfoQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserLike(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserLt(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserLte(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserNe(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserNotIn(lastModifyUser ...string) BCSClusterInfoQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) LastModifyUserNotlike(lastModifyUser string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) Limit(limit int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) Offset(offset int) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs BCSClusterInfoQuerySet) One(ret *BCSClusterInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByApiKeyContent is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByApiKeyContent() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_content ASC"))
}

// OrderAscByApiKeyPrefix is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByApiKeyPrefix() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_prefix ASC"))
}

// OrderAscByApiKeyType is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByApiKeyType() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_type ASC"))
}

// OrderAscByBCSApiClusterId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByBCSApiClusterId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bcs_api_cluster_id ASC"))
}

// OrderAscByBkBizId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByBkBizId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByBkCloudId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByBkCloudId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_cloud_id ASC"))
}

// OrderAscByBkEnv is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByBkEnv() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_env ASC"))
}

// OrderAscByCertContent is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCertContent() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("cert_content ASC"))
}

// OrderAscByClusterID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByClusterID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCreateTime() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCreator() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByCustomEventDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCustomEventDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomEventDataID ASC"))
}

// OrderAscByCustomLogDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCustomLogDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomLogDataID ASC"))
}

// OrderAscByCustomMetricDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByCustomMetricDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomMetricDataID ASC"))
}

// OrderAscByDomainName is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByDomainName() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("domain_name ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByIsDeletedAllowView is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByIsDeletedAllowView() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_deleted_allow_view ASC"))
}

// OrIsDeletedAllowView adds an OR condition for `is_deleted_allow_view = true`.
func (qs BCSClusterInfoQuerySet) OrIsDeletedAllowView(value bool) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Or("is_deleted_allow_view = ?", value))
}
// OrderAscByIsSkipSslVerify is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByIsSkipSslVerify() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_skip_ssl_verify ASC"))
}

// OrderAscByK8sEventDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByK8sEventDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("K8sEventDataID ASC"))
}

// OrderAscByK8sMetricDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByK8sMetricDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("K8sMetricDataID ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByLastModifyTime() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByLastModifyUser() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByPort is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByPort() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("port ASC"))
}

// OrderAscByProjectId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByProjectId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("project_id ASC"))
}

// OrderAscByServerAddressPath is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByServerAddressPath() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("server_address_path ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscByStatus() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscBySystemLogDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderAscBySystemLogDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("SystemLogDataID ASC"))
}

// OrderDescByApiKeyContent is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByApiKeyContent() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_content DESC"))
}

// OrderDescByApiKeyPrefix is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByApiKeyPrefix() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_prefix DESC"))
}

// OrderDescByApiKeyType is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByApiKeyType() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("api_key_type DESC"))
}

// OrderDescByBCSApiClusterId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByBCSApiClusterId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bcs_api_cluster_id DESC"))
}

// OrderDescByBkBizId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByBkBizId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByBkCloudId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByBkCloudId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_cloud_id DESC"))
}

// OrderDescByBkEnv is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByBkEnv() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("bk_env DESC"))
}

// OrderDescByCertContent is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCertContent() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("cert_content DESC"))
}

// OrderDescByClusterID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByClusterID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCreateTime() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCreator() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByCustomEventDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCustomEventDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomEventDataID DESC"))
}

// OrderDescByCustomLogDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCustomLogDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomLogDataID DESC"))
}

// OrderDescByCustomMetricDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByCustomMetricDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("CustomMetricDataID DESC"))
}

// OrderDescByDomainName is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByDomainName() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("domain_name DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByIsDeletedAllowView is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByIsDeletedAllowView() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_deleted_allow_view DESC"))
}

// OrderDescByIsSkipSslVerify is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByIsSkipSslVerify() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_skip_ssl_verify DESC"))
}

// OrderDescByK8sEventDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByK8sEventDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("K8sEventDataID DESC"))
}

// OrderDescByK8sMetricDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByK8sMetricDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("K8sMetricDataID DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByLastModifyTime() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByLastModifyUser() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByPort is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByPort() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("port DESC"))
}

// OrderDescByProjectId is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByProjectId() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("project_id DESC"))
}

// OrderDescByServerAddressPath is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByServerAddressPath() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("server_address_path DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescByStatus() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescBySystemLogDataID is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) OrderDescBySystemLogDataID() BCSClusterInfoQuerySet {
	return qs.w(qs.db.Order("SystemLogDataID DESC"))
}

// PortEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortEq(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port = ?", port))
}

// PortGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortGt(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port > ?", port))
}

// PortGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortGte(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port >= ?", port))
}

// PortIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortIn(port ...uint) BCSClusterInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port IN (?)", port))
}

// PortLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortLt(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port < ?", port))
}

// PortLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortLte(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port <= ?", port))
}

// PortNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortNe(port uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("port != ?", port))
}

// PortNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) PortNotIn(port ...uint) BCSClusterInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port NOT IN (?)", port))
}

// ProjectIdEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdEq(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id = ?", projectId))
}

// ProjectIdGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdGt(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id > ?", projectId))
}

// ProjectIdGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdGte(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id >= ?", projectId))
}

// ProjectIdIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdIn(projectId ...string) BCSClusterInfoQuerySet {
	if len(projectId) == 0 {
		qs.db.AddError(errors.New("must at least pass one projectId in ProjectIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("project_id IN (?)", projectId))
}

// ProjectIdLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdLike(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id LIKE ?", projectId))
}

// ProjectIdLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdLt(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id < ?", projectId))
}

// ProjectIdLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdLte(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id <= ?", projectId))
}

// ProjectIdNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdNe(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id != ?", projectId))
}

// ProjectIdNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdNotIn(projectId ...string) BCSClusterInfoQuerySet {
	if len(projectId) == 0 {
		qs.db.AddError(errors.New("must at least pass one projectId in ProjectIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("project_id NOT IN (?)", projectId))
}

// ProjectIdNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ProjectIdNotlike(projectId string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("project_id NOT LIKE ?", projectId))
}

// ServerAddressPathEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathEq(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path = ?", serverAddressPath))
}

// ServerAddressPathGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathGt(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path > ?", serverAddressPath))
}

// ServerAddressPathGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathGte(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path >= ?", serverAddressPath))
}

// ServerAddressPathIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathIn(serverAddressPath ...string) BCSClusterInfoQuerySet {
	if len(serverAddressPath) == 0 {
		qs.db.AddError(errors.New("must at least pass one serverAddressPath in ServerAddressPathIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("server_address_path IN (?)", serverAddressPath))
}

// ServerAddressPathLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathLike(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path LIKE ?", serverAddressPath))
}

// ServerAddressPathLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathLt(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path < ?", serverAddressPath))
}

// ServerAddressPathLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathLte(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path <= ?", serverAddressPath))
}

// ServerAddressPathNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathNe(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path != ?", serverAddressPath))
}

// ServerAddressPathNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathNotIn(serverAddressPath ...string) BCSClusterInfoQuerySet {
	if len(serverAddressPath) == 0 {
		qs.db.AddError(errors.New("must at least pass one serverAddressPath in ServerAddressPathNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("server_address_path NOT IN (?)", serverAddressPath))
}

// ServerAddressPathNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) ServerAddressPathNotlike(serverAddressPath string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("server_address_path NOT LIKE ?", serverAddressPath))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusEq(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusGt(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status > ?", status))
}

// StatusGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusGte(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status >= ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusIn(status ...string) BCSClusterInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusLike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusLike(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status LIKE ?", status))
}

// StatusLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusLt(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status < ?", status))
}

// StatusLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusLte(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status <= ?", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusNe(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusNotIn(status ...string) BCSClusterInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// StatusNotlike is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) StatusNotlike(status string) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("status NOT LIKE ?", status))
}

// SystemLogDataIDEq is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDEq(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID = ?", systemLogDataID))
}

// SystemLogDataIDGt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDGt(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID > ?", systemLogDataID))
}

// SystemLogDataIDGte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDGte(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID >= ?", systemLogDataID))
}

// SystemLogDataIDIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDIn(systemLogDataID ...uint) BCSClusterInfoQuerySet {
	if len(systemLogDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one systemLogDataID in SystemLogDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("SystemLogDataID IN (?)", systemLogDataID))
}

// SystemLogDataIDLt is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDLt(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID < ?", systemLogDataID))
}

// SystemLogDataIDLte is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDLte(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID <= ?", systemLogDataID))
}

// SystemLogDataIDNe is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDNe(systemLogDataID uint) BCSClusterInfoQuerySet {
	return qs.w(qs.db.Where("SystemLogDataID != ?", systemLogDataID))
}

// SystemLogDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BCSClusterInfoQuerySet) SystemLogDataIDNotIn(systemLogDataID ...uint) BCSClusterInfoQuerySet {
	if len(systemLogDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one systemLogDataID in SystemLogDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("SystemLogDataID NOT IN (?)", systemLogDataID))
}

// SetApiKeyContent is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetApiKeyContent(apiKeyContent string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ApiKeyContent)] = apiKeyContent
	return u
}

// SetApiKeyPrefix is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetApiKeyPrefix(apiKeyPrefix string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ApiKeyPrefix)] = apiKeyPrefix
	return u
}

// SetApiKeyType is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetApiKeyType(apiKeyType string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ApiKeyType)] = apiKeyType
	return u
}

// SetBCSApiClusterId is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetBCSApiClusterId(bCSApiClusterId string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.BCSApiClusterId)] = bCSApiClusterId
	return u
}

// SetBkBizId is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetBkBizId(bkBizId int) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.BkBizId)] = bkBizId
	return u
}

// SetBkCloudId is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetBkCloudId(bkCloudId *int) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.BkCloudId)] = bkCloudId
	return u
}

// SetBkEnv is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetBkEnv(bkEnv *string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.BkEnv)] = bkEnv
	return u
}

// SetCertContent is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCertContent(certContent *string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.CertContent)] = certContent
	return u
}

// SetClusterID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetClusterID(clusterID string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ClusterID)] = clusterID
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCreateTime(createTime time.Time) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCreator(creator string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.Creator)] = creator
	return u
}

// SetCustomEventDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCustomEventDataID(customEventDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.CustomEventDataID)] = customEventDataID
	return u
}

// SetCustomLogDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCustomLogDataID(customLogDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.CustomLogDataID)] = customLogDataID
	return u
}

// SetCustomMetricDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetCustomMetricDataID(customMetricDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.CustomMetricDataID)] = customMetricDataID
	return u
}

// SetDomainName is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetDomainName(domainName string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.DomainName)] = domainName
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetID(ID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ID)] = ID
	return u
}

// SetIsDeletedAllowView is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetIsDeletedAllowView(isDeletedAllowView bool) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.IsDeletedAllowView)] = isDeletedAllowView
	return u
}

// SetIsSkipSslVerify is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetIsSkipSslVerify(isSkipSslVerify bool) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.IsSkipSslVerify)] = isSkipSslVerify
	return u
}

// SetK8sEventDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetK8sEventDataID(k8sEventDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.K8sEventDataID)] = k8sEventDataID
	return u
}

// SetK8sMetricDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetK8sMetricDataID(k8sMetricDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.K8sMetricDataID)] = k8sMetricDataID
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetLastModifyTime(lastModifyTime time.Time) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetLastModifyUser(lastModifyUser string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetPort is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetPort(port uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.Port)] = port
	return u
}

// SetProjectId is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetProjectId(projectId string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ProjectId)] = projectId
	return u
}

// SetServerAddressPath is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetServerAddressPath(serverAddressPath string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.ServerAddressPath)] = serverAddressPath
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetStatus(status string) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.Status)] = status
	return u
}

// SetSystemLogDataID is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) SetSystemLogDataID(systemLogDataID uint) BCSClusterInfoUpdater {
	u.fields[string(BCSClusterInfoDBSchema.SystemLogDataID)] = systemLogDataID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u BCSClusterInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set BCSClusterInfoQuerySet

// ===== BEGIN of BCSClusterInfo modifiers

// BCSClusterInfoDBSchemaField describes database schema field. It requires for method 'Update'
type BCSClusterInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f BCSClusterInfoDBSchemaField) String() string {
	return string(f)
}

// BCSClusterInfoDBSchema stores db field names of BCSClusterInfo
var BCSClusterInfoDBSchema = struct {
	ID                 BCSClusterInfoDBSchemaField
	ClusterID          BCSClusterInfoDBSchemaField
	BCSApiClusterId    BCSClusterInfoDBSchemaField
	BkBizId            BCSClusterInfoDBSchemaField
	BkCloudId          BCSClusterInfoDBSchemaField
	ProjectId          BCSClusterInfoDBSchemaField
	Status             BCSClusterInfoDBSchemaField
	DomainName         BCSClusterInfoDBSchemaField
	Port               BCSClusterInfoDBSchemaField
	ServerAddressPath  BCSClusterInfoDBSchemaField
	ApiKeyType         BCSClusterInfoDBSchemaField
	ApiKeyContent      BCSClusterInfoDBSchemaField
	ApiKeyPrefix       BCSClusterInfoDBSchemaField
	IsSkipSslVerify    BCSClusterInfoDBSchemaField
	CertContent        BCSClusterInfoDBSchemaField
	K8sMetricDataID    BCSClusterInfoDBSchemaField
	CustomMetricDataID BCSClusterInfoDBSchemaField
	K8sEventDataID     BCSClusterInfoDBSchemaField
	CustomEventDataID  BCSClusterInfoDBSchemaField
	SystemLogDataID    BCSClusterInfoDBSchemaField
	CustomLogDataID    BCSClusterInfoDBSchemaField
	BkEnv              BCSClusterInfoDBSchemaField
	Creator            BCSClusterInfoDBSchemaField
	CreateTime         BCSClusterInfoDBSchemaField
	LastModifyTime     BCSClusterInfoDBSchemaField
	LastModifyUser     BCSClusterInfoDBSchemaField
	IsDeletedAllowView BCSClusterInfoDBSchemaField
}{

	ID:                 BCSClusterInfoDBSchemaField("id"),
	ClusterID:          BCSClusterInfoDBSchemaField("cluster_id"),
	BCSApiClusterId:    BCSClusterInfoDBSchemaField("bcs_api_cluster_id"),
	BkBizId:            BCSClusterInfoDBSchemaField("bk_biz_id"),
	BkCloudId:          BCSClusterInfoDBSchemaField("bk_cloud_id"),
	ProjectId:          BCSClusterInfoDBSchemaField("project_id"),
	Status:             BCSClusterInfoDBSchemaField("status"),
	DomainName:         BCSClusterInfoDBSchemaField("domain_name"),
	Port:               BCSClusterInfoDBSchemaField("port"),
	ServerAddressPath:  BCSClusterInfoDBSchemaField("server_address_path"),
	ApiKeyType:         BCSClusterInfoDBSchemaField("api_key_type"),
	ApiKeyContent:      BCSClusterInfoDBSchemaField("api_key_content"),
	ApiKeyPrefix:       BCSClusterInfoDBSchemaField("api_key_prefix"),
	IsSkipSslVerify:    BCSClusterInfoDBSchemaField("is_skip_ssl_verify"),
	CertContent:        BCSClusterInfoDBSchemaField("cert_content"),
	K8sMetricDataID:    BCSClusterInfoDBSchemaField("K8sMetricDataID"),
	CustomMetricDataID: BCSClusterInfoDBSchemaField("CustomMetricDataID"),
	K8sEventDataID:     BCSClusterInfoDBSchemaField("K8sEventDataID"),
	CustomEventDataID:  BCSClusterInfoDBSchemaField("CustomEventDataID"),
	SystemLogDataID:    BCSClusterInfoDBSchemaField("SystemLogDataID"),
	CustomLogDataID:    BCSClusterInfoDBSchemaField("CustomLogDataID"),
	BkEnv:              BCSClusterInfoDBSchemaField("bk_env"),
	Creator:            BCSClusterInfoDBSchemaField("creator"),
	CreateTime:         BCSClusterInfoDBSchemaField("create_time"),
	LastModifyTime:     BCSClusterInfoDBSchemaField("last_modify_time"),
	LastModifyUser:     BCSClusterInfoDBSchemaField("last_modify_user"),
	IsDeletedAllowView: BCSClusterInfoDBSchemaField("is_deleted_allow_view"),
}

// Update updates BCSClusterInfo fields by primary key
// nolint: dupl
func (o *BCSClusterInfo) Update(db *gorm.DB, fields ...BCSClusterInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                    o.ID,
		"cluster_id":            o.ClusterID,
		"bcs_api_cluster_id":    o.BCSApiClusterId,
		"bk_biz_id":             o.BkBizId,
		"bk_cloud_id":           o.BkCloudId,
		"project_id":            o.ProjectId,
		"status":                o.Status,
		"domain_name":           o.DomainName,
		"port":                  o.Port,
		"server_address_path":   o.ServerAddressPath,
		"api_key_type":          o.ApiKeyType,
		"api_key_content":       o.ApiKeyContent,
		"api_key_prefix":        o.ApiKeyPrefix,
		"is_skip_ssl_verify":    o.IsSkipSslVerify,
		"cert_content":          o.CertContent,
		"K8sMetricDataID":       o.K8sMetricDataID,
		"CustomMetricDataID":    o.CustomMetricDataID,
		"K8sEventDataID":        o.K8sEventDataID,
		"CustomEventDataID":     o.CustomEventDataID,
		"SystemLogDataID":       o.SystemLogDataID,
		"CustomLogDataID":       o.CustomLogDataID,
		"bk_env":                o.BkEnv,
		"creator":               o.Creator,
		"create_time":           o.CreateTime,
		"last_modify_time":      o.LastModifyTime,
		"last_modify_user":      o.LastModifyUser,
		"is_deleted_allow_view": o.IsDeletedAllowView,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update BCSClusterInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// BCSClusterInfoUpdater is an BCSClusterInfo updates manager
type BCSClusterInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewBCSClusterInfoUpdater creates new BCSClusterInfo updater
// nolint: dupl
func NewBCSClusterInfoUpdater(db *gorm.DB) BCSClusterInfoUpdater {
	return BCSClusterInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&BCSClusterInfo{}),
	}
}

// ===== END of BCSClusterInfo modifiers

// ===== END of all query sets
