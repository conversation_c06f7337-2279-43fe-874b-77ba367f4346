// Code generated by go-queryset. DO NOT EDIT.
package bcs

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set PodMonitorInfoQuerySet

// PodMonitorInfoQuerySet is an queryset type for PodMonitorInfo
type PodMonitorInfoQuerySet struct {
	db *gorm.DB
}

// NewPodMonitorInfoQuerySet constructs new PodMonitorInfoQuerySet
func NewPodMonitorInfoQuerySet(db *gorm.DB) PodMonitorInfoQuerySet {
	return PodMonitorInfoQuerySet{
		db: db.Model(&PodMonitorInfo{}),
	}
}

func (qs PodMonitorInfoQuerySet) w(db *gorm.DB) PodMonitorInfoQuerySet {
	return NewPodMonitorInfoQuerySet(db)
}

func (qs PodMonitorInfoQuerySet) Select(fields ...PodMonitorInfoDBSchemaField) PodMonitorInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *PodMonitorInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *PodMonitorInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) All(ret *[]PodMonitorInfo) error {
	return qs.db.Find(ret).Error
}

// BkDataIdEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdEq(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataId))
}

// BkDataIdGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdGt(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataId))
}

// BkDataIdGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdGte(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataId))
}

// BkDataIdIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdIn(bkDataId ...uint) PodMonitorInfoQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataId))
}

// BkDataIdLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdLt(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataId))
}

// BkDataIdLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdLte(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataId))
}

// BkDataIdNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdNe(bkDataId uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataId))
}

// BkDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) BkDataIdNotIn(bkDataId ...uint) PodMonitorInfoQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataId))
}

// ClusterIDEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDEq(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterID))
}

// ClusterIDGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDGt(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterID))
}

// ClusterIDGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDGte(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterID))
}

// ClusterIDIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDIn(clusterID ...string) PodMonitorInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterID))
}

// ClusterIDLike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDLike(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id LIKE ?", clusterID))
}

// ClusterIDLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDLt(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterID))
}

// ClusterIDLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDLte(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterID))
}

// ClusterIDNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDNe(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterID))
}

// ClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDNotIn(clusterID ...string) PodMonitorInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterID))
}

// ClusterIDNotlike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ClusterIDNotlike(clusterID string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id NOT LIKE ?", clusterID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) Delete() error {
	return qs.db.Delete(PodMonitorInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(PodMonitorInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(PodMonitorInfo{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) GetUpdater() PodMonitorInfoUpdater {
	return NewPodMonitorInfoUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdEq(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdGt(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdGte(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdIn(id ...uint) PodMonitorInfoQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdLt(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdLte(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdNe(id uint) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IdNotIn(id ...uint) PodMonitorInfoQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// IsCommonDataIdEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCommonDataIdEq(isCommonDataId bool) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_common_data_id = ?", isCommonDataId))
}

// IsCommonDataIdIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCommonDataIdIn(isCommonDataId ...bool) PodMonitorInfoQuerySet {
	if len(isCommonDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommonDataId in IsCommonDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common_data_id IN (?)", isCommonDataId))
}

// IsCommonDataIdNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCommonDataIdNe(isCommonDataId bool) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_common_data_id != ?", isCommonDataId))
}

// IsCommonDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCommonDataIdNotIn(isCommonDataId ...bool) PodMonitorInfoQuerySet {
	if len(isCommonDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCommonDataId in IsCommonDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_common_data_id NOT IN (?)", isCommonDataId))
}

// IsCustomResourceEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCustomResourceEq(isCustomResource bool) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_custom_resource = ?", isCustomResource))
}

// IsCustomResourceIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCustomResourceIn(isCustomResource ...bool) PodMonitorInfoQuerySet {
	if len(isCustomResource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomResource in IsCustomResourceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_resource IN (?)", isCustomResource))
}

// IsCustomResourceNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCustomResourceNe(isCustomResource bool) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("is_custom_resource != ?", isCustomResource))
}

// IsCustomResourceNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) IsCustomResourceNotIn(isCustomResource ...bool) PodMonitorInfoQuerySet {
	if len(isCustomResource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomResource in IsCustomResourceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_resource NOT IN (?)", isCustomResource))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) Limit(limit int) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameEq(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameGt(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameGte(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameIn(name ...string) PodMonitorInfoQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameLike(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameLt(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameLte(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameNe(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameNotIn(name ...string) PodMonitorInfoQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NameNotlike(name string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// NamespaceEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceEq(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace = ?", namespace))
}

// NamespaceGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceGt(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace > ?", namespace))
}

// NamespaceGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceGte(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace >= ?", namespace))
}

// NamespaceIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceIn(namespace ...string) PodMonitorInfoQuerySet {
	if len(namespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one namespace in NamespaceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("namespace IN (?)", namespace))
}

// NamespaceLike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceLike(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace LIKE ?", namespace))
}

// NamespaceLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceLt(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace < ?", namespace))
}

// NamespaceLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceLte(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace <= ?", namespace))
}

// NamespaceNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceNe(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace != ?", namespace))
}

// NamespaceNotIn is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceNotIn(namespace ...string) PodMonitorInfoQuerySet {
	if len(namespace) == 0 {
		qs.db.AddError(errors.New("must at least pass one namespace in NamespaceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("namespace NOT IN (?)", namespace))
}

// NamespaceNotlike is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) NamespaceNotlike(namespace string) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("namespace NOT LIKE ?", namespace))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) Offset(offset int) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs PodMonitorInfoQuerySet) One(ret *PodMonitorInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataId is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByBkDataId() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByClusterID is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByClusterID() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscById() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByIsCommonDataId is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByIsCommonDataId() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_common_data_id ASC"))
}

// OrderAscByIsCustomResource is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByIsCustomResource() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_custom_resource ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByName() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByNamespace is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByNamespace() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("namespace ASC"))
}

// OrderAscByRecordCreateTime is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByRecordCreateTime() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("record_create_time ASC"))
}

// OrderAscByResourceCreateTime is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderAscByResourceCreateTime() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("resource_create_time ASC"))
}

// OrderDescByBkDataId is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByBkDataId() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByClusterID is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByClusterID() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescById() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByIsCommonDataId is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByIsCommonDataId() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_common_data_id DESC"))
}

// OrderDescByIsCustomResource is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByIsCustomResource() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("is_custom_resource DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByName() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByNamespace is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByNamespace() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("namespace DESC"))
}

// OrderDescByRecordCreateTime is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByRecordCreateTime() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("record_create_time DESC"))
}

// OrderDescByResourceCreateTime is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) OrderDescByResourceCreateTime() PodMonitorInfoQuerySet {
	return qs.w(qs.db.Order("resource_create_time DESC"))
}

// RecordCreateTimeEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeEq(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time = ?", recordCreateTime))
}

// RecordCreateTimeGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeGt(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time > ?", recordCreateTime))
}

// RecordCreateTimeGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeGte(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time >= ?", recordCreateTime))
}

// RecordCreateTimeLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeLt(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time < ?", recordCreateTime))
}

// RecordCreateTimeLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeLte(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time <= ?", recordCreateTime))
}

// RecordCreateTimeNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) RecordCreateTimeNe(recordCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("record_create_time != ?", recordCreateTime))
}

// ResourceCreateTimeEq is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeEq(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time = ?", resourceCreateTime))
}

// ResourceCreateTimeGt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeGt(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time > ?", resourceCreateTime))
}

// ResourceCreateTimeGte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeGte(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time >= ?", resourceCreateTime))
}

// ResourceCreateTimeLt is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeLt(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time < ?", resourceCreateTime))
}

// ResourceCreateTimeLte is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeLte(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time <= ?", resourceCreateTime))
}

// ResourceCreateTimeNe is an autogenerated method
// nolint: dupl
func (qs PodMonitorInfoQuerySet) ResourceCreateTimeNe(resourceCreateTime time.Time) PodMonitorInfoQuerySet {
	return qs.w(qs.db.Where("resource_create_time != ?", resourceCreateTime))
}

// SetBkDataId is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetBkDataId(bkDataId uint) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.BkDataId)] = bkDataId
	return u
}

// SetClusterID is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetClusterID(clusterID string) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.ClusterID)] = clusterID
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetId(id uint) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.Id)] = id
	return u
}

// SetIsCommonDataId is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetIsCommonDataId(isCommonDataId bool) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.IsCommonDataId)] = isCommonDataId
	return u
}

// SetIsCustomResource is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetIsCustomResource(isCustomResource bool) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.IsCustomResource)] = isCustomResource
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetName(name string) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.Name)] = name
	return u
}

// SetNamespace is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetNamespace(namespace string) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.Namespace)] = namespace
	return u
}

// SetRecordCreateTime is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetRecordCreateTime(recordCreateTime time.Time) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.RecordCreateTime)] = recordCreateTime
	return u
}

// SetResourceCreateTime is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) SetResourceCreateTime(resourceCreateTime time.Time) PodMonitorInfoUpdater {
	u.fields[string(PodMonitorInfoDBSchema.ResourceCreateTime)] = resourceCreateTime
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u PodMonitorInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set PodMonitorInfoQuerySet

// ===== BEGIN of PodMonitorInfo modifiers

// PodMonitorInfoDBSchemaField describes database schema field. It requires for method 'Update'
type PodMonitorInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f PodMonitorInfoDBSchemaField) String() string {
	return string(f)
}

// PodMonitorInfoDBSchema stores db field names of PodMonitorInfo
var PodMonitorInfoDBSchema = struct {
	Id                 PodMonitorInfoDBSchemaField
	ClusterID          PodMonitorInfoDBSchemaField
	Namespace          PodMonitorInfoDBSchemaField
	Name               PodMonitorInfoDBSchemaField
	BkDataId           PodMonitorInfoDBSchemaField
	IsCustomResource   PodMonitorInfoDBSchemaField
	IsCommonDataId     PodMonitorInfoDBSchemaField
	RecordCreateTime   PodMonitorInfoDBSchemaField
	ResourceCreateTime PodMonitorInfoDBSchemaField
}{

	Id:                 PodMonitorInfoDBSchemaField("id"),
	ClusterID:          PodMonitorInfoDBSchemaField("cluster_id"),
	Namespace:          PodMonitorInfoDBSchemaField("namespace"),
	Name:               PodMonitorInfoDBSchemaField("name"),
	BkDataId:           PodMonitorInfoDBSchemaField("bk_data_id"),
	IsCustomResource:   PodMonitorInfoDBSchemaField("is_custom_resource"),
	IsCommonDataId:     PodMonitorInfoDBSchemaField("is_common_data_id"),
	RecordCreateTime:   PodMonitorInfoDBSchemaField("record_create_time"),
	ResourceCreateTime: PodMonitorInfoDBSchemaField("resource_create_time"),
}

// Update updates PodMonitorInfo fields by primary key
// nolint: dupl
func (o *PodMonitorInfo) Update(db *gorm.DB, fields ...PodMonitorInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                   o.Id,
		"cluster_id":           o.ClusterID,
		"namespace":            o.Namespace,
		"name":                 o.Name,
		"bk_data_id":           o.BkDataId,
		"is_custom_resource":   o.IsCustomResource,
		"is_common_data_id":    o.IsCommonDataId,
		"record_create_time":   o.RecordCreateTime,
		"resource_create_time": o.ResourceCreateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update PodMonitorInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// PodMonitorInfoUpdater is an PodMonitorInfo updates manager
type PodMonitorInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewPodMonitorInfoUpdater creates new PodMonitorInfo updater
// nolint: dupl
func NewPodMonitorInfoUpdater(db *gorm.DB) PodMonitorInfoUpdater {
	return PodMonitorInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&PodMonitorInfo{}),
	}
}

// ===== END of PodMonitorInfo modifiers

// ===== END of all query sets
