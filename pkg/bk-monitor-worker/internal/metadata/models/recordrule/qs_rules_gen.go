// Code generated by go-queryset. DO NOT EDIT.
package recordrule

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set RecordRuleQuerySet

// RecordRuleQuerySet is an queryset type for RecordRule
type RecordRuleQuerySet struct {
	db *gorm.DB
}

// NewRecordRuleQuerySet constructs new RecordRuleQuerySet
func NewRecordRuleQuerySet(db *gorm.DB) RecordRuleQuerySet {
	return RecordRuleQuerySet{
		db: db.Model(&RecordRule{}),
	}
}

func (qs RecordRuleQuerySet) w(db *gorm.DB) RecordRuleQuerySet {
	return NewRecordRuleQuerySet(db)
}

func (qs RecordRuleQuerySet) Select(fields ...RecordRuleDBSchemaField) RecordRuleQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *RecordRule) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *RecordRule) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) All(ret *[]RecordRule) error {
	return qs.db.Find(ret).Error
}

// BkSqlConfigEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigEq(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config = ?", bkSqlConfig))
}

// BkSqlConfigGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigGt(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config > ?", bkSqlConfig))
}

// BkSqlConfigGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigGte(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config >= ?", bkSqlConfig))
}

// BkSqlConfigIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigIn(bkSqlConfig ...string) RecordRuleQuerySet {
	if len(bkSqlConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkSqlConfig in BkSqlConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_sql_config IN (?)", bkSqlConfig))
}

// BkSqlConfigLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigLike(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config LIKE ?", bkSqlConfig))
}

// BkSqlConfigLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigLt(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config < ?", bkSqlConfig))
}

// BkSqlConfigLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigLte(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config <= ?", bkSqlConfig))
}

// BkSqlConfigNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigNe(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config != ?", bkSqlConfig))
}

// BkSqlConfigNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigNotIn(bkSqlConfig ...string) RecordRuleQuerySet {
	if len(bkSqlConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkSqlConfig in BkSqlConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_sql_config NOT IN (?)", bkSqlConfig))
}

// BkSqlConfigNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) BkSqlConfigNotlike(bkSqlConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("bk_sql_config NOT LIKE ?", bkSqlConfig))
}

// Count is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateAtEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtEq(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at = ?", createAt))
}

// CreateAtGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtGt(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at > ?", createAt))
}

// CreateAtGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtGte(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at >= ?", createAt))
}

// CreateAtLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtLt(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at < ?", createAt))
}

// CreateAtLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtLte(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at <= ?", createAt))
}

// CreateAtNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreateAtNe(createAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("create_at != ?", createAt))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorEq(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorGt(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorGte(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorIn(creator ...string) RecordRuleQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorLike(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorLt(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorLte(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorNe(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorNotIn(creator ...string) RecordRuleQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) CreatorNotlike(creator string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) Delete() error {
	return qs.db.Delete(RecordRule{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(RecordRule{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(RecordRule{})
	return db.RowsAffected, db.Error
}

// DstVmTableIdEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdEq(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id = ?", dstVmTableId))
}

// DstVmTableIdGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdGt(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id > ?", dstVmTableId))
}

// DstVmTableIdGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdGte(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id >= ?", dstVmTableId))
}

// DstVmTableIdIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdIn(dstVmTableId ...string) RecordRuleQuerySet {
	if len(dstVmTableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one dstVmTableId in DstVmTableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("dst_vm_table_id IN (?)", dstVmTableId))
}

// DstVmTableIdLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdLike(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id LIKE ?", dstVmTableId))
}

// DstVmTableIdLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdLt(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id < ?", dstVmTableId))
}

// DstVmTableIdLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdLte(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id <= ?", dstVmTableId))
}

// DstVmTableIdNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdNe(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id != ?", dstVmTableId))
}

// DstVmTableIdNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdNotIn(dstVmTableId ...string) RecordRuleQuerySet {
	if len(dstVmTableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one dstVmTableId in DstVmTableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("dst_vm_table_id NOT IN (?)", dstVmTableId))
}

// DstVmTableIdNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) DstVmTableIdNotlike(dstVmTableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("dst_vm_table_id NOT LIKE ?", dstVmTableId))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) GetUpdater() RecordRuleUpdater {
	return NewRecordRuleUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdEq(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdGt(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdGte(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdIn(id ...int) RecordRuleQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdLt(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdLte(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdNe(id int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) IdNotIn(id ...int) RecordRuleQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) Limit(limit int) RecordRuleQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) Offset(offset int) RecordRuleQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs RecordRuleQuerySet) One(ret *RecordRule) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkSqlConfig is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByBkSqlConfig() RecordRuleQuerySet {
	return qs.w(qs.db.Order("bk_sql_config ASC"))
}

// OrderAscByCreateAt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByCreateAt() RecordRuleQuerySet {
	return qs.w(qs.db.Order("create_at ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByCreator() RecordRuleQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByDstVmTableId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByDstVmTableId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("dst_vm_table_id ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscById() RecordRuleQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByRecordName is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByRecordName() RecordRuleQuerySet {
	return qs.w(qs.db.Order("record_name ASC"))
}

// OrderAscByRuleConfig is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByRuleConfig() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_config ASC"))
}

// OrderAscByRuleMetrics is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByRuleMetrics() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_metrics ASC"))
}

// OrderAscByRuleType is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByRuleType() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_type ASC"))
}

// OrderAscBySpaceId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscBySpaceId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("space_id ASC"))
}

// OrderAscBySpaceType is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscBySpaceType() RecordRuleQuerySet {
	return qs.w(qs.db.Order("space_type ASC"))
}

// OrderAscBySrcVmTableIds is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscBySrcVmTableIds() RecordRuleQuerySet {
	return qs.w(qs.db.Order("src_vm_table_ids ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByStatus() RecordRuleQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscByTableId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByTableId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByUpdateAt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByUpdateAt() RecordRuleQuerySet {
	return qs.w(qs.db.Order("update_at ASC"))
}

// OrderAscByUpdater is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByUpdater() RecordRuleQuerySet {
	return qs.w(qs.db.Order("updater ASC"))
}

// OrderAscByVmClusterId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderAscByVmClusterId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("vm_cluster_id ASC"))
}

// OrderDescByBkSqlConfig is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByBkSqlConfig() RecordRuleQuerySet {
	return qs.w(qs.db.Order("bk_sql_config DESC"))
}

// OrderDescByCreateAt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByCreateAt() RecordRuleQuerySet {
	return qs.w(qs.db.Order("create_at DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByCreator() RecordRuleQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByDstVmTableId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByDstVmTableId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("dst_vm_table_id DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescById() RecordRuleQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByRecordName is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByRecordName() RecordRuleQuerySet {
	return qs.w(qs.db.Order("record_name DESC"))
}

// OrderDescByRuleConfig is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByRuleConfig() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_config DESC"))
}

// OrderDescByRuleMetrics is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByRuleMetrics() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_metrics DESC"))
}

// OrderDescByRuleType is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByRuleType() RecordRuleQuerySet {
	return qs.w(qs.db.Order("rule_type DESC"))
}

// OrderDescBySpaceId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescBySpaceId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("space_id DESC"))
}

// OrderDescBySpaceType is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescBySpaceType() RecordRuleQuerySet {
	return qs.w(qs.db.Order("space_type DESC"))
}

// OrderDescBySrcVmTableIds is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescBySrcVmTableIds() RecordRuleQuerySet {
	return qs.w(qs.db.Order("src_vm_table_ids DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByStatus() RecordRuleQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescByTableId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByTableId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByUpdateAt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByUpdateAt() RecordRuleQuerySet {
	return qs.w(qs.db.Order("update_at DESC"))
}

// OrderDescByUpdater is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByUpdater() RecordRuleQuerySet {
	return qs.w(qs.db.Order("updater DESC"))
}

// OrderDescByVmClusterId is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) OrderDescByVmClusterId() RecordRuleQuerySet {
	return qs.w(qs.db.Order("vm_cluster_id DESC"))
}

// RecordNameEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameEq(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name = ?", recordName))
}

// RecordNameGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameGt(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name > ?", recordName))
}

// RecordNameGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameGte(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name >= ?", recordName))
}

// RecordNameIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameIn(recordName ...string) RecordRuleQuerySet {
	if len(recordName) == 0 {
		qs.db.AddError(errors.New("must at least pass one recordName in RecordNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("record_name IN (?)", recordName))
}

// RecordNameLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameLike(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name LIKE ?", recordName))
}

// RecordNameLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameLt(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name < ?", recordName))
}

// RecordNameLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameLte(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name <= ?", recordName))
}

// RecordNameNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameNe(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name != ?", recordName))
}

// RecordNameNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameNotIn(recordName ...string) RecordRuleQuerySet {
	if len(recordName) == 0 {
		qs.db.AddError(errors.New("must at least pass one recordName in RecordNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("record_name NOT IN (?)", recordName))
}

// RecordNameNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RecordNameNotlike(recordName string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("record_name NOT LIKE ?", recordName))
}

// RuleConfigEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigEq(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config = ?", ruleConfig))
}

// RuleConfigGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigGt(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config > ?", ruleConfig))
}

// RuleConfigGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigGte(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config >= ?", ruleConfig))
}

// RuleConfigIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigIn(ruleConfig ...string) RecordRuleQuerySet {
	if len(ruleConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleConfig in RuleConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_config IN (?)", ruleConfig))
}

// RuleConfigLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigLike(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config LIKE ?", ruleConfig))
}

// RuleConfigLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigLt(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config < ?", ruleConfig))
}

// RuleConfigLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigLte(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config <= ?", ruleConfig))
}

// RuleConfigNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigNe(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config != ?", ruleConfig))
}

// RuleConfigNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigNotIn(ruleConfig ...string) RecordRuleQuerySet {
	if len(ruleConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleConfig in RuleConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_config NOT IN (?)", ruleConfig))
}

// RuleConfigNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleConfigNotlike(ruleConfig string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_config NOT LIKE ?", ruleConfig))
}

// RuleMetricsEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsEq(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics = ?", ruleMetrics))
}

// RuleMetricsGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsGt(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics > ?", ruleMetrics))
}

// RuleMetricsGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsGte(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics >= ?", ruleMetrics))
}

// RuleMetricsIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsIn(ruleMetrics ...string) RecordRuleQuerySet {
	if len(ruleMetrics) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleMetrics in RuleMetricsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_metrics IN (?)", ruleMetrics))
}

// RuleMetricsLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsLike(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics LIKE ?", ruleMetrics))
}

// RuleMetricsLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsLt(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics < ?", ruleMetrics))
}

// RuleMetricsLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsLte(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics <= ?", ruleMetrics))
}

// RuleMetricsNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsNe(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics != ?", ruleMetrics))
}

// RuleMetricsNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsNotIn(ruleMetrics ...string) RecordRuleQuerySet {
	if len(ruleMetrics) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleMetrics in RuleMetricsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_metrics NOT IN (?)", ruleMetrics))
}

// RuleMetricsNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleMetricsNotlike(ruleMetrics string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_metrics NOT LIKE ?", ruleMetrics))
}

// RuleTypeEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeEq(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type = ?", ruleType))
}

// RuleTypeGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeGt(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type > ?", ruleType))
}

// RuleTypeGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeGte(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type >= ?", ruleType))
}

// RuleTypeIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeIn(ruleType ...string) RecordRuleQuerySet {
	if len(ruleType) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleType in RuleTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_type IN (?)", ruleType))
}

// RuleTypeLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeLike(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type LIKE ?", ruleType))
}

// RuleTypeLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeLt(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type < ?", ruleType))
}

// RuleTypeLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeLte(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type <= ?", ruleType))
}

// RuleTypeNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeNe(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type != ?", ruleType))
}

// RuleTypeNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeNotIn(ruleType ...string) RecordRuleQuerySet {
	if len(ruleType) == 0 {
		qs.db.AddError(errors.New("must at least pass one ruleType in RuleTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("rule_type NOT IN (?)", ruleType))
}

// RuleTypeNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) RuleTypeNotlike(ruleType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("rule_type NOT LIKE ?", ruleType))
}

// SpaceIdEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdEq(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id = ?", spaceId))
}

// SpaceIdGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdGt(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id > ?", spaceId))
}

// SpaceIdGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdGte(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id >= ?", spaceId))
}

// SpaceIdIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdIn(spaceId ...string) RecordRuleQuerySet {
	if len(spaceId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceId in SpaceIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id IN (?)", spaceId))
}

// SpaceIdLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdLike(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id LIKE ?", spaceId))
}

// SpaceIdLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdLt(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id < ?", spaceId))
}

// SpaceIdLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdLte(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id <= ?", spaceId))
}

// SpaceIdNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdNe(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id != ?", spaceId))
}

// SpaceIdNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdNotIn(spaceId ...string) RecordRuleQuerySet {
	if len(spaceId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceId in SpaceIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id NOT IN (?)", spaceId))
}

// SpaceIdNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceIdNotlike(spaceId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_id NOT LIKE ?", spaceId))
}

// SpaceTypeEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeEq(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type = ?", spaceType))
}

// SpaceTypeGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeGt(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type > ?", spaceType))
}

// SpaceTypeGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeGte(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type >= ?", spaceType))
}

// SpaceTypeIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeIn(spaceType ...string) RecordRuleQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type IN (?)", spaceType))
}

// SpaceTypeLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeLike(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type LIKE ?", spaceType))
}

// SpaceTypeLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeLt(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type < ?", spaceType))
}

// SpaceTypeLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeLte(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type <= ?", spaceType))
}

// SpaceTypeNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeNe(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type != ?", spaceType))
}

// SpaceTypeNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeNotIn(spaceType ...string) RecordRuleQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type NOT IN (?)", spaceType))
}

// SpaceTypeNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SpaceTypeNotlike(spaceType string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("space_type NOT LIKE ?", spaceType))
}

// SrcVmTableIdsEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsEq(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids = ?", srcVmTableIds))
}

// SrcVmTableIdsGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsGt(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids > ?", srcVmTableIds))
}

// SrcVmTableIdsGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsGte(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids >= ?", srcVmTableIds))
}

// SrcVmTableIdsIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsIn(srcVmTableIds ...string) RecordRuleQuerySet {
	if len(srcVmTableIds) == 0 {
		qs.db.AddError(errors.New("must at least pass one srcVmTableIds in SrcVmTableIdsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("src_vm_table_ids IN (?)", srcVmTableIds))
}

// SrcVmTableIdsLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsLike(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids LIKE ?", srcVmTableIds))
}

// SrcVmTableIdsLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsLt(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids < ?", srcVmTableIds))
}

// SrcVmTableIdsLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsLte(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids <= ?", srcVmTableIds))
}

// SrcVmTableIdsNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsNe(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids != ?", srcVmTableIds))
}

// SrcVmTableIdsNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsNotIn(srcVmTableIds ...string) RecordRuleQuerySet {
	if len(srcVmTableIds) == 0 {
		qs.db.AddError(errors.New("must at least pass one srcVmTableIds in SrcVmTableIdsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("src_vm_table_ids NOT IN (?)", srcVmTableIds))
}

// SrcVmTableIdsNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) SrcVmTableIdsNotlike(srcVmTableIds string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("src_vm_table_ids NOT LIKE ?", srcVmTableIds))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusEq(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusGt(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status > ?", status))
}

// StatusGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusGte(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status >= ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusIn(status ...string) RecordRuleQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusLike(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status LIKE ?", status))
}

// StatusLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusLt(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status < ?", status))
}

// StatusLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusLte(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status <= ?", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusNe(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusNotIn(status ...string) RecordRuleQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// StatusNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) StatusNotlike(status string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("status NOT LIKE ?", status))
}

// TableIdEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdEq(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableId))
}

// TableIdGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdGt(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableId))
}

// TableIdGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdGte(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableId))
}

// TableIdIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdIn(tableId ...string) RecordRuleQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableId))
}

// TableIdLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdLike(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableId))
}

// TableIdLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdLt(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableId))
}

// TableIdLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdLte(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableId))
}

// TableIdNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdNe(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableId))
}

// TableIdNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdNotIn(tableId ...string) RecordRuleQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableId))
}

// TableIdNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) TableIdNotlike(tableId string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableId))
}

// UpdateAtEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtEq(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at = ?", updateAt))
}

// UpdateAtGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtGt(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at > ?", updateAt))
}

// UpdateAtGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtGte(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at >= ?", updateAt))
}

// UpdateAtLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtLt(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at < ?", updateAt))
}

// UpdateAtLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtLte(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at <= ?", updateAt))
}

// UpdateAtNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdateAtNe(updateAt time.Time) RecordRuleQuerySet {
	return qs.w(qs.db.Where("update_at != ?", updateAt))
}

// UpdaterEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterEq(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater = ?", updater))
}

// UpdaterGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterGt(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater > ?", updater))
}

// UpdaterGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterGte(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater >= ?", updater))
}

// UpdaterIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterIn(updater ...string) RecordRuleQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater IN (?)", updater))
}

// UpdaterLike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterLike(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater LIKE ?", updater))
}

// UpdaterLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterLt(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater < ?", updater))
}

// UpdaterLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterLte(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater <= ?", updater))
}

// UpdaterNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterNe(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater != ?", updater))
}

// UpdaterNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterNotIn(updater ...string) RecordRuleQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater NOT IN (?)", updater))
}

// UpdaterNotlike is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) UpdaterNotlike(updater string) RecordRuleQuerySet {
	return qs.w(qs.db.Where("updater NOT LIKE ?", updater))
}

// VmClusterIdEq is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdEq(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id = ?", vmClusterId))
}

// VmClusterIdGt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdGt(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id > ?", vmClusterId))
}

// VmClusterIdGte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdGte(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id >= ?", vmClusterId))
}

// VmClusterIdIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdIn(vmClusterId ...int) RecordRuleQuerySet {
	if len(vmClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one vmClusterId in VmClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_cluster_id IN (?)", vmClusterId))
}

// VmClusterIdLt is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdLt(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id < ?", vmClusterId))
}

// VmClusterIdLte is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdLte(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id <= ?", vmClusterId))
}

// VmClusterIdNe is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdNe(vmClusterId int) RecordRuleQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id != ?", vmClusterId))
}

// VmClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs RecordRuleQuerySet) VmClusterIdNotIn(vmClusterId ...int) RecordRuleQuerySet {
	if len(vmClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one vmClusterId in VmClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_cluster_id NOT IN (?)", vmClusterId))
}

// SetBkSqlConfig is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetBkSqlConfig(bkSqlConfig string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.BkSqlConfig)] = bkSqlConfig
	return u
}

// SetCreateAt is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetCreateAt(createAt time.Time) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.CreateAt)] = createAt
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetCreator(creator string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.Creator)] = creator
	return u
}

// SetDstVmTableId is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetDstVmTableId(dstVmTableId string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.DstVmTableId)] = dstVmTableId
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetId(id int) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.Id)] = id
	return u
}

// SetRecordName is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetRecordName(recordName string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.RecordName)] = recordName
	return u
}

// SetRuleConfig is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetRuleConfig(ruleConfig string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.RuleConfig)] = ruleConfig
	return u
}

// SetRuleMetrics is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetRuleMetrics(ruleMetrics string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.RuleMetrics)] = ruleMetrics
	return u
}

// SetRuleType is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetRuleType(ruleType string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.RuleType)] = ruleType
	return u
}

// SetSpaceId is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetSpaceId(spaceId string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.SpaceId)] = spaceId
	return u
}

// SetSpaceType is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetSpaceType(spaceType string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.SpaceType)] = spaceType
	return u
}

// SetSrcVmTableIds is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetSrcVmTableIds(srcVmTableIds string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.SrcVmTableIds)] = srcVmTableIds
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetStatus(status string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.Status)] = status
	return u
}

// SetTableId is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetTableId(tableId string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.TableId)] = tableId
	return u
}

// SetUpdateAt is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetUpdateAt(updateAt time.Time) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.UpdateAt)] = updateAt
	return u
}

// SetUpdater is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetUpdater(updater string) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.Updater)] = updater
	return u
}

// SetVmClusterId is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) SetVmClusterId(vmClusterId int) RecordRuleUpdater {
	u.fields[string(RecordRuleDBSchema.VmClusterId)] = vmClusterId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u RecordRuleUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set RecordRuleQuerySet

// ===== BEGIN of RecordRule modifiers

// RecordRuleDBSchemaField describes database schema field. It requires for method 'Update'
type RecordRuleDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f RecordRuleDBSchemaField) String() string {
	return string(f)
}

// RecordRuleDBSchema stores db field names of RecordRule
var RecordRuleDBSchema = struct {
	Id            RecordRuleDBSchemaField
	SpaceType     RecordRuleDBSchemaField
	SpaceId       RecordRuleDBSchemaField
	TableId       RecordRuleDBSchemaField
	RecordName    RecordRuleDBSchemaField
	RuleType      RecordRuleDBSchemaField
	RuleConfig    RecordRuleDBSchemaField
	BkSqlConfig   RecordRuleDBSchemaField
	RuleMetrics   RecordRuleDBSchemaField
	SrcVmTableIds RecordRuleDBSchemaField
	VmClusterId   RecordRuleDBSchemaField
	DstVmTableId  RecordRuleDBSchemaField
	Status        RecordRuleDBSchemaField
	Creator       RecordRuleDBSchemaField
	CreateAt      RecordRuleDBSchemaField
	Updater       RecordRuleDBSchemaField
	UpdateAt      RecordRuleDBSchemaField
}{

	Id:            RecordRuleDBSchemaField("id"),
	SpaceType:     RecordRuleDBSchemaField("space_type"),
	SpaceId:       RecordRuleDBSchemaField("space_id"),
	TableId:       RecordRuleDBSchemaField("table_id"),
	RecordName:    RecordRuleDBSchemaField("record_name"),
	RuleType:      RecordRuleDBSchemaField("rule_type"),
	RuleConfig:    RecordRuleDBSchemaField("rule_config"),
	BkSqlConfig:   RecordRuleDBSchemaField("bk_sql_config"),
	RuleMetrics:   RecordRuleDBSchemaField("rule_metrics"),
	SrcVmTableIds: RecordRuleDBSchemaField("src_vm_table_ids"),
	VmClusterId:   RecordRuleDBSchemaField("vm_cluster_id"),
	DstVmTableId:  RecordRuleDBSchemaField("dst_vm_table_id"),
	Status:        RecordRuleDBSchemaField("status"),
	Creator:       RecordRuleDBSchemaField("creator"),
	CreateAt:      RecordRuleDBSchemaField("create_at"),
	Updater:       RecordRuleDBSchemaField("updater"),
	UpdateAt:      RecordRuleDBSchemaField("update_at"),
}

// Update updates RecordRule fields by primary key
// nolint: dupl
func (o *RecordRule) Update(db *gorm.DB, fields ...RecordRuleDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":               o.Id,
		"space_type":       o.SpaceType,
		"space_id":         o.SpaceId,
		"table_id":         o.TableId,
		"record_name":      o.RecordName,
		"rule_type":        o.RuleType,
		"rule_config":      o.RuleConfig,
		"bk_sql_config":    o.BkSqlConfig,
		"rule_metrics":     o.RuleMetrics,
		"src_vm_table_ids": o.SrcVmTableIds,
		"vm_cluster_id":    o.VmClusterId,
		"dst_vm_table_id":  o.DstVmTableId,
		"status":           o.Status,
		"creator":          o.Creator,
		"create_at":        o.CreateAt,
		"updater":          o.Updater,
		"update_at":        o.UpdateAt,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update RecordRule %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// RecordRuleUpdater is an RecordRule updates manager
type RecordRuleUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewRecordRuleUpdater creates new RecordRule updater
// nolint: dupl
func NewRecordRuleUpdater(db *gorm.DB) RecordRuleUpdater {
	return RecordRuleUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&RecordRule{}),
	}
}

// ===== END of RecordRule modifiers

// ===== END of all query sets
