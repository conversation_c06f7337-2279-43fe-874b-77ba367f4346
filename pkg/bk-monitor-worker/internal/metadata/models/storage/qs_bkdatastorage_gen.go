// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set BkDataStorageQuerySet

// BkDataStorageQuerySet is an queryset type for BkDataStorage
type BkDataStorageQuerySet struct {
	db *gorm.DB
}

// NewBkDataStorageQuerySet constructs new BkDataStorageQuerySet
func NewBkDataStorageQuerySet(db *gorm.DB) BkDataStorageQuerySet {
	return BkDataStorageQuerySet{
		db: db.Model(&BkDataStorage{}),
	}
}

func (qs BkDataStorageQuerySet) w(db *gorm.DB) BkDataStorageQuerySet {
	return NewBkDataStorageQuerySet(db)
}

func (qs BkDataStorageQuerySet) Select(fields ...BkDataStorageDBSchemaField) BkDataStorageQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *BkDataStorage) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *BkDataStorage) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) All(ret *[]BkDataStorage) error {
	return qs.db.Find(ret).Error
}

// BkDataResultTableIDEq is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDEq(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id = ?", bkDataResultTableID))
}

// BkDataResultTableIDGt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDGt(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id > ?", bkDataResultTableID))
}

// BkDataResultTableIDGte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDGte(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id >= ?", bkDataResultTableID))
}

// BkDataResultTableIDIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDIn(bkDataResultTableID ...string) BkDataStorageQuerySet {
	if len(bkDataResultTableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataResultTableID in BkDataResultTableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_result_table_id IN (?)", bkDataResultTableID))
}

// BkDataResultTableIDLike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDLike(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id LIKE ?", bkDataResultTableID))
}

// BkDataResultTableIDLt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDLt(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id < ?", bkDataResultTableID))
}

// BkDataResultTableIDLte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDLte(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id <= ?", bkDataResultTableID))
}

// BkDataResultTableIDNe is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDNe(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id != ?", bkDataResultTableID))
}

// BkDataResultTableIDNotIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDNotIn(bkDataResultTableID ...string) BkDataStorageQuerySet {
	if len(bkDataResultTableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataResultTableID in BkDataResultTableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_result_table_id NOT IN (?)", bkDataResultTableID))
}

// BkDataResultTableIDNotlike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) BkDataResultTableIDNotlike(bkDataResultTableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("bk_data_result_table_id NOT LIKE ?", bkDataResultTableID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) Delete() error {
	return qs.db.Delete(BkDataStorage{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(BkDataStorage{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(BkDataStorage{})
	return db.RowsAffected, db.Error
}

// EtlJSONConfigEq is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigEq(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config = ?", etlJSONConfig))
}

// EtlJSONConfigGt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigGt(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config > ?", etlJSONConfig))
}

// EtlJSONConfigGte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigGte(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config >= ?", etlJSONConfig))
}

// EtlJSONConfigIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigIn(etlJSONConfig ...string) BkDataStorageQuerySet {
	if len(etlJSONConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one etlJSONConfig in EtlJSONConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("etl_json_config IN (?)", etlJSONConfig))
}

// EtlJSONConfigLike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigLike(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config LIKE ?", etlJSONConfig))
}

// EtlJSONConfigLt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigLt(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config < ?", etlJSONConfig))
}

// EtlJSONConfigLte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigLte(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config <= ?", etlJSONConfig))
}

// EtlJSONConfigNe is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigNe(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config != ?", etlJSONConfig))
}

// EtlJSONConfigNotIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigNotIn(etlJSONConfig ...string) BkDataStorageQuerySet {
	if len(etlJSONConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one etlJSONConfig in EtlJSONConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("etl_json_config NOT IN (?)", etlJSONConfig))
}

// EtlJSONConfigNotlike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) EtlJSONConfigNotlike(etlJSONConfig string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("etl_json_config NOT LIKE ?", etlJSONConfig))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) GetUpdater() BkDataStorageUpdater {
	return NewBkDataStorageUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) Limit(limit int) BkDataStorageQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) Offset(offset int) BkDataStorageQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs BkDataStorageQuerySet) One(ret *BkDataStorage) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataResultTableID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderAscByBkDataResultTableID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("bk_data_result_table_id ASC"))
}

// OrderAscByEtlJSONConfig is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderAscByEtlJSONConfig() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("etl_json_config ASC"))
}

// OrderAscByRawDataID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderAscByRawDataID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("raw_data_id ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderAscByTableID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByBkDataResultTableID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderDescByBkDataResultTableID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("bk_data_result_table_id DESC"))
}

// OrderDescByEtlJSONConfig is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderDescByEtlJSONConfig() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("etl_json_config DESC"))
}

// OrderDescByRawDataID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderDescByRawDataID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("raw_data_id DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) OrderDescByTableID() BkDataStorageQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// RawDataIDEq is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDEq(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id = ?", rawDataID))
}

// RawDataIDGt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDGt(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id > ?", rawDataID))
}

// RawDataIDGte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDGte(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id >= ?", rawDataID))
}

// RawDataIDIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDIn(rawDataID ...int) BkDataStorageQuerySet {
	if len(rawDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one rawDataID in RawDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("raw_data_id IN (?)", rawDataID))
}

// RawDataIDLt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDLt(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id < ?", rawDataID))
}

// RawDataIDLte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDLte(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id <= ?", rawDataID))
}

// RawDataIDNe is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDNe(rawDataID int) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("raw_data_id != ?", rawDataID))
}

// RawDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) RawDataIDNotIn(rawDataID ...int) BkDataStorageQuerySet {
	if len(rawDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one rawDataID in RawDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("raw_data_id NOT IN (?)", rawDataID))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDEq(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDGt(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDGte(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDIn(tableID ...string) BkDataStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDLike(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDLt(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDLte(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDNe(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDNotIn(tableID ...string) BkDataStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs BkDataStorageQuerySet) TableIDNotlike(tableID string) BkDataStorageQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// SetBkDataResultTableID is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) SetBkDataResultTableID(bkDataResultTableID string) BkDataStorageUpdater {
	u.fields[string(BkDataStorageDBSchema.BkDataResultTableID)] = bkDataResultTableID
	return u
}

// SetEtlJSONConfig is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) SetEtlJSONConfig(etlJSONConfig string) BkDataStorageUpdater {
	u.fields[string(BkDataStorageDBSchema.EtlJSONConfig)] = etlJSONConfig
	return u
}

// SetRawDataID is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) SetRawDataID(rawDataID int) BkDataStorageUpdater {
	u.fields[string(BkDataStorageDBSchema.RawDataID)] = rawDataID
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) SetTableID(tableID string) BkDataStorageUpdater {
	u.fields[string(BkDataStorageDBSchema.TableID)] = tableID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u BkDataStorageUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set BkDataStorageQuerySet

// ===== BEGIN of BkDataStorage modifiers

// BkDataStorageDBSchemaField describes database schema field. It requires for method 'Update'
type BkDataStorageDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f BkDataStorageDBSchemaField) String() string {
	return string(f)
}

// BkDataStorageDBSchema stores db field names of BkDataStorage
var BkDataStorageDBSchema = struct {
	TableID             BkDataStorageDBSchemaField
	RawDataID           BkDataStorageDBSchemaField
	EtlJSONConfig       BkDataStorageDBSchemaField
	BkDataResultTableID BkDataStorageDBSchemaField
}{

	TableID:             BkDataStorageDBSchemaField("table_id"),
	RawDataID:           BkDataStorageDBSchemaField("raw_data_id"),
	EtlJSONConfig:       BkDataStorageDBSchemaField("etl_json_config"),
	BkDataResultTableID: BkDataStorageDBSchemaField("bk_data_result_table_id"),
}

// Update updates BkDataStorage fields by primary key
// nolint: dupl
func (o *BkDataStorage) Update(db *gorm.DB, fields ...BkDataStorageDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":                o.TableID,
		"raw_data_id":             o.RawDataID,
		"etl_json_config":         o.EtlJSONConfig,
		"bk_data_result_table_id": o.BkDataResultTableID,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update BkDataStorage %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// BkDataStorageUpdater is an BkDataStorage updates manager
type BkDataStorageUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewBkDataStorageUpdater creates new BkDataStorage updater
// nolint: dupl
func NewBkDataStorageUpdater(db *gorm.DB) BkDataStorageUpdater {
	return BkDataStorageUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&BkDataStorage{}),
	}
}

// ===== END of BkDataStorage modifiers

// ===== END of all query sets
