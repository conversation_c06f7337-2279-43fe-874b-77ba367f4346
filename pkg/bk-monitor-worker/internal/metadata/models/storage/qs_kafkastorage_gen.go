// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set KafkaStorageQuerySet

// KafkaStorageQuerySet is an queryset type for KafkaStorage
type KafkaStorageQuerySet struct {
	db *gorm.DB
}

// NewKafkaStorageQuerySet constructs new KafkaStorageQuerySet
func NewKafkaStorageQuerySet(db *gorm.DB) KafkaStorageQuerySet {
	return KafkaStorageQuerySet{
		db: db.Model(&KafkaStorage{}),
	}
}

func (qs KafkaStorageQuerySet) w(db *gorm.DB) KafkaStorageQuerySet {
	return NewKafkaStorageQuerySet(db)
}

func (qs KafkaStorageQuerySet) Select(fields ...KafkaStorageDBSchemaField) KafkaStorageQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *KafkaStorage) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *KafkaStorage) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) All(ret *[]KafkaStorage) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) Delete() error {
	return qs.db.Delete(KafkaStorage{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(KafkaStorage{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(KafkaStorage{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) GetUpdater() KafkaStorageUpdater {
	return NewKafkaStorageUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) Limit(limit int) KafkaStorageQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) Offset(offset int) KafkaStorageQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs KafkaStorageQuerySet) One(ret *KafkaStorage) error {
	return qs.db.First(ret).Error
}

// OrderAscByPartition is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderAscByPartition() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("partition ASC"))
}

// OrderAscByRetention is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderAscByRetention() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("retention ASC"))
}

// OrderAscByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderAscByStorageClusterID() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderAscByTableID() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTopic is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderAscByTopic() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("topic ASC"))
}

// OrderDescByPartition is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderDescByPartition() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("partition DESC"))
}

// OrderDescByRetention is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderDescByRetention() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("retention DESC"))
}

// OrderDescByStorageClusterID is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderDescByStorageClusterID() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("storage_cluster_id DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderDescByTableID() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTopic is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) OrderDescByTopic() KafkaStorageQuerySet {
	return qs.w(qs.db.Order("topic DESC"))
}

// PartitionEq is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionEq(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition = ?", partition))
}

// PartitionGt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionGt(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition > ?", partition))
}

// PartitionGte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionGte(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition >= ?", partition))
}

// PartitionIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionIn(partition ...uint) KafkaStorageQuerySet {
	if len(partition) == 0 {
		qs.db.AddError(errors.New("must at least pass one partition in PartitionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("partition IN (?)", partition))
}

// PartitionLt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionLt(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition < ?", partition))
}

// PartitionLte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionLte(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition <= ?", partition))
}

// PartitionNe is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionNe(partition uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("partition != ?", partition))
}

// PartitionNotIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) PartitionNotIn(partition ...uint) KafkaStorageQuerySet {
	if len(partition) == 0 {
		qs.db.AddError(errors.New("must at least pass one partition in PartitionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("partition NOT IN (?)", partition))
}

// RetentionEq is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionEq(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention = ?", retention))
}

// RetentionGt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionGt(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention > ?", retention))
}

// RetentionGte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionGte(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention >= ?", retention))
}

// RetentionIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionIn(retention ...int64) KafkaStorageQuerySet {
	if len(retention) == 0 {
		qs.db.AddError(errors.New("must at least pass one retention in RetentionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("retention IN (?)", retention))
}

// RetentionLt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionLt(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention < ?", retention))
}

// RetentionLte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionLte(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention <= ?", retention))
}

// RetentionNe is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionNe(retention int64) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("retention != ?", retention))
}

// RetentionNotIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) RetentionNotIn(retention ...int64) KafkaStorageQuerySet {
	if len(retention) == 0 {
		qs.db.AddError(errors.New("must at least pass one retention in RetentionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("retention NOT IN (?)", retention))
}

// StorageClusterIDEq is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDEq(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id = ?", storageClusterID))
}

// StorageClusterIDGt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDGt(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id > ?", storageClusterID))
}

// StorageClusterIDGte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDGte(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id >= ?", storageClusterID))
}

// StorageClusterIDIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDIn(storageClusterID ...uint) KafkaStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id IN (?)", storageClusterID))
}

// StorageClusterIDLt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDLt(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id < ?", storageClusterID))
}

// StorageClusterIDLte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDLte(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id <= ?", storageClusterID))
}

// StorageClusterIDNe is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDNe(storageClusterID uint) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("storage_cluster_id != ?", storageClusterID))
}

// StorageClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) StorageClusterIDNotIn(storageClusterID ...uint) KafkaStorageQuerySet {
	if len(storageClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one storageClusterID in StorageClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("storage_cluster_id NOT IN (?)", storageClusterID))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDEq(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDGt(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDGte(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDIn(tableID ...string) KafkaStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDLike(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDLt(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDLte(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDNe(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDNotIn(tableID ...string) KafkaStorageQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TableIDNotlike(tableID string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TopicEq is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicEq(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic = ?", topic))
}

// TopicGt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicGt(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic > ?", topic))
}

// TopicGte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicGte(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic >= ?", topic))
}

// TopicIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicIn(topic ...string) KafkaStorageQuerySet {
	if len(topic) == 0 {
		qs.db.AddError(errors.New("must at least pass one topic in TopicIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("topic IN (?)", topic))
}

// TopicLike is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicLike(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic LIKE ?", topic))
}

// TopicLt is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicLt(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic < ?", topic))
}

// TopicLte is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicLte(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic <= ?", topic))
}

// TopicNe is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicNe(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic != ?", topic))
}

// TopicNotIn is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicNotIn(topic ...string) KafkaStorageQuerySet {
	if len(topic) == 0 {
		qs.db.AddError(errors.New("must at least pass one topic in TopicNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("topic NOT IN (?)", topic))
}

// TopicNotlike is an autogenerated method
// nolint: dupl
func (qs KafkaStorageQuerySet) TopicNotlike(topic string) KafkaStorageQuerySet {
	return qs.w(qs.db.Where("topic NOT LIKE ?", topic))
}

// SetPartition is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) SetPartition(partition uint) KafkaStorageUpdater {
	u.fields[string(KafkaStorageDBSchema.Partition)] = partition
	return u
}

// SetRetention is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) SetRetention(retention int64) KafkaStorageUpdater {
	u.fields[string(KafkaStorageDBSchema.Retention)] = retention
	return u
}

// SetStorageClusterID is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) SetStorageClusterID(storageClusterID uint) KafkaStorageUpdater {
	u.fields[string(KafkaStorageDBSchema.StorageClusterID)] = storageClusterID
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) SetTableID(tableID string) KafkaStorageUpdater {
	u.fields[string(KafkaStorageDBSchema.TableID)] = tableID
	return u
}

// SetTopic is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) SetTopic(topic string) KafkaStorageUpdater {
	u.fields[string(KafkaStorageDBSchema.Topic)] = topic
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u KafkaStorageUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set KafkaStorageQuerySet

// ===== BEGIN of KafkaStorage modifiers

// KafkaStorageDBSchemaField describes database schema field. It requires for method 'Update'
type KafkaStorageDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f KafkaStorageDBSchemaField) String() string {
	return string(f)
}

// KafkaStorageDBSchema stores db field names of KafkaStorage
var KafkaStorageDBSchema = struct {
	TableID          KafkaStorageDBSchemaField
	Topic            KafkaStorageDBSchemaField
	Partition        KafkaStorageDBSchemaField
	StorageClusterID KafkaStorageDBSchemaField
	Retention        KafkaStorageDBSchemaField
}{

	TableID:          KafkaStorageDBSchemaField("table_id"),
	Topic:            KafkaStorageDBSchemaField("topic"),
	Partition:        KafkaStorageDBSchemaField("partition"),
	StorageClusterID: KafkaStorageDBSchemaField("storage_cluster_id"),
	Retention:        KafkaStorageDBSchemaField("retention"),
}

// Update updates KafkaStorage fields by primary key
// nolint: dupl
func (o *KafkaStorage) Update(db *gorm.DB, fields ...KafkaStorageDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":           o.TableID,
		"topic":              o.Topic,
		"partition":          o.Partition,
		"storage_cluster_id": o.StorageClusterID,
		"retention":          o.Retention,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update KafkaStorage %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// KafkaStorageUpdater is an KafkaStorage updates manager
type KafkaStorageUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewKafkaStorageUpdater creates new KafkaStorage updater
// nolint: dupl
func NewKafkaStorageUpdater(db *gorm.DB) KafkaStorageUpdater {
	return KafkaStorageUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&KafkaStorage{}),
	}
}

// ===== END of KafkaStorage modifiers

// ===== END of all query sets
