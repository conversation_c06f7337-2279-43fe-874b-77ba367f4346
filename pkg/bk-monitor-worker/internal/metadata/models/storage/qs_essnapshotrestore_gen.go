// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set EsSnapshotRestoreQuerySet

// EsSnapshotRestoreQuerySet is an queryset type for EsSnapshotRestore
type EsSnapshotRestoreQuerySet struct {
	db *gorm.DB
}

// NewEsSnapshotRestoreQuerySet constructs new EsSnapshotRestoreQuerySet
func NewEsSnapshotRestoreQuerySet(db *gorm.DB) EsSnapshotRestoreQuerySet {
	return EsSnapshotRestoreQuerySet{
		db: db.Model(&EsSnapshotRestore{}),
	}
}

func (qs EsSnapshotRestoreQuerySet) w(db *gorm.DB) EsSnapshotRestoreQuerySet {
	return NewEsSnapshotRestoreQuerySet(db)
}

func (qs EsSnapshotRestoreQuerySet) Select(fields ...EsSnapshotRestoreDBSchemaField) EsSnapshotRestoreQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *EsSnapshotRestore) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *EsSnapshotRestore) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) All(ret *[]EsSnapshotRestore) error {
	return qs.db.Find(ret).Error
}

// CompleteDocCountEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountEq(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count = ?", completeDocCount))
}

// CompleteDocCountGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountGt(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count > ?", completeDocCount))
}

// CompleteDocCountGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountGte(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count >= ?", completeDocCount))
}

// CompleteDocCountIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountIn(completeDocCount ...int) EsSnapshotRestoreQuerySet {
	if len(completeDocCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one completeDocCount in CompleteDocCountIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("complete_doc_count IN (?)", completeDocCount))
}

// CompleteDocCountLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountLt(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count < ?", completeDocCount))
}

// CompleteDocCountLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountLte(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count <= ?", completeDocCount))
}

// CompleteDocCountNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountNe(completeDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("complete_doc_count != ?", completeDocCount))
}

// CompleteDocCountNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CompleteDocCountNotIn(completeDocCount ...int) EsSnapshotRestoreQuerySet {
	if len(completeDocCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one completeDocCount in CompleteDocCountNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("complete_doc_count NOT IN (?)", completeDocCount))
}

// Count is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeEq(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeGt(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeGte(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeLt(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeLte(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreateTimeNe(createTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorEq(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorGt(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorGte(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorIn(creator ...string) EsSnapshotRestoreQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorLike(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorLt(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorLte(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorNe(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorNotIn(creator ...string) EsSnapshotRestoreQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) CreatorNotlike(creator string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) Delete() error {
	return qs.db.Delete(EsSnapshotRestore{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(EsSnapshotRestore{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(EsSnapshotRestore{})
	return db.RowsAffected, db.Error
}

// DurationEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationEq(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration = ?", duration))
}

// DurationGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationGt(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration > ?", duration))
}

// DurationGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationGte(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration >= ?", duration))
}

// DurationIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationIn(duration ...int) EsSnapshotRestoreQuerySet {
	if len(duration) == 0 {
		qs.db.AddError(errors.New("must at least pass one duration in DurationIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("duration IN (?)", duration))
}

// DurationLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationLt(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration < ?", duration))
}

// DurationLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationLte(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration <= ?", duration))
}

// DurationNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationNe(duration int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("duration != ?", duration))
}

// DurationNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) DurationNotIn(duration ...int) EsSnapshotRestoreQuerySet {
	if len(duration) == 0 {
		qs.db.AddError(errors.New("must at least pass one duration in DurationNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("duration NOT IN (?)", duration))
}

// EndTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeEq(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time = ?", endTime))
}

// EndTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeGt(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time > ?", endTime))
}

// EndTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeGte(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time >= ?", endTime))
}

// EndTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeLt(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time < ?", endTime))
}

// EndTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeLte(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time <= ?", endTime))
}

// EndTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) EndTimeNe(endTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("end_time != ?", endTime))
}

// ExpiredDeleteEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredDeleteEq(expiredDelete bool) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_delete = ?", expiredDelete))
}

// ExpiredDeleteIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredDeleteIn(expiredDelete ...bool) EsSnapshotRestoreQuerySet {
	if len(expiredDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one expiredDelete in ExpiredDeleteIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("expired_delete IN (?)", expiredDelete))
}

// ExpiredDeleteNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredDeleteNe(expiredDelete bool) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_delete != ?", expiredDelete))
}

// ExpiredDeleteNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredDeleteNotIn(expiredDelete ...bool) EsSnapshotRestoreQuerySet {
	if len(expiredDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one expiredDelete in ExpiredDeleteNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("expired_delete NOT IN (?)", expiredDelete))
}

// ExpiredTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeEq(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time = ?", expiredTime))
}

// ExpiredTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeGt(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time > ?", expiredTime))
}

// ExpiredTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeGte(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time >= ?", expiredTime))
}

// ExpiredTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeLt(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time < ?", expiredTime))
}

// ExpiredTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeLte(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time <= ?", expiredTime))
}

// ExpiredTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) ExpiredTimeNe(expiredTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("expired_time != ?", expiredTime))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) GetUpdater() EsSnapshotRestoreUpdater {
	return NewEsSnapshotRestoreUpdater(qs.db)
}

// IndicesEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesEq(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices = ?", indices))
}

// IndicesGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesGt(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices > ?", indices))
}

// IndicesGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesGte(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices >= ?", indices))
}

// IndicesIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesIn(indices ...string) EsSnapshotRestoreQuerySet {
	if len(indices) == 0 {
		qs.db.AddError(errors.New("must at least pass one indices in IndicesIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("indices IN (?)", indices))
}

// IndicesLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesLike(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices LIKE ?", indices))
}

// IndicesLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesLt(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices < ?", indices))
}

// IndicesLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesLte(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices <= ?", indices))
}

// IndicesNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesNe(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices != ?", indices))
}

// IndicesNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesNotIn(indices ...string) EsSnapshotRestoreQuerySet {
	if len(indices) == 0 {
		qs.db.AddError(errors.New("must at least pass one indices in IndicesNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("indices NOT IN (?)", indices))
}

// IndicesNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IndicesNotlike(indices string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("indices NOT LIKE ?", indices))
}

// IsDeletedEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IsDeletedEq(isDeleted bool) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("is_deleted = ?", isDeleted))
}

// IsDeletedIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IsDeletedIn(isDeleted ...bool) EsSnapshotRestoreQuerySet {
	if len(isDeleted) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeleted in IsDeletedIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted IN (?)", isDeleted))
}

// IsDeletedNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IsDeletedNe(isDeleted bool) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("is_deleted != ?", isDeleted))
}

// IsDeletedNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) IsDeletedNotIn(isDeleted ...bool) EsSnapshotRestoreQuerySet {
	if len(isDeleted) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDeleted in IsDeletedNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_deleted NOT IN (?)", isDeleted))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeEq(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeGt(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeGte(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeLt(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeLte(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyTimeNe(lastModifyTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserEq(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserGt(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserGte(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserIn(lastModifyUser ...string) EsSnapshotRestoreQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserLike(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserLt(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserLte(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserNe(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserNotIn(lastModifyUser ...string) EsSnapshotRestoreQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) LastModifyUserNotlike(lastModifyUser string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) Limit(limit int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) Offset(offset int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs EsSnapshotRestoreQuerySet) One(ret *EsSnapshotRestore) error {
	return qs.db.First(ret).Error
}

// OrderAscByCompleteDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByCompleteDocCount() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("complete_doc_count ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByCreateTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByCreator() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByDuration is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByDuration() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("duration ASC"))
}

// OrderAscByEndTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByEndTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("end_time ASC"))
}

// OrderAscByExpiredDelete is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByExpiredDelete() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("expired_delete ASC"))
}

// OrderAscByExpiredTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByExpiredTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("expired_time ASC"))
}

// OrderAscByIndices is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByIndices() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("indices ASC"))
}

// OrderAscByIsDeleted is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByIsDeleted() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("is_deleted ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByLastModifyTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByLastModifyUser() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByRestoreID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByRestoreID() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("restore_id ASC"))
}

// OrderAscByStartTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByStartTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("start_time ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByTableID() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTotalDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByTotalDocCount() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("total_doc_count ASC"))
}

// OrderAscByTotalStoreSize is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderAscByTotalStoreSize() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("total_store_size ASC"))
}

// OrderDescByCompleteDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByCompleteDocCount() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("complete_doc_count DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByCreateTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByCreator() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByDuration is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByDuration() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("duration DESC"))
}

// OrderDescByEndTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByEndTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("end_time DESC"))
}

// OrderDescByExpiredDelete is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByExpiredDelete() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("expired_delete DESC"))
}

// OrderDescByExpiredTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByExpiredTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("expired_time DESC"))
}

// OrderDescByIndices is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByIndices() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("indices DESC"))
}

// OrderDescByIsDeleted is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByIsDeleted() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("is_deleted DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByLastModifyTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByLastModifyUser() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByRestoreID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByRestoreID() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("restore_id DESC"))
}

// OrderDescByStartTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByStartTime() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("start_time DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByTableID() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTotalDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByTotalDocCount() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("total_doc_count DESC"))
}

// OrderDescByTotalStoreSize is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) OrderDescByTotalStoreSize() EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Order("total_store_size DESC"))
}

// RestoreIDEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDEq(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id = ?", restoreID))
}

// RestoreIDGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDGt(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id > ?", restoreID))
}

// RestoreIDGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDGte(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id >= ?", restoreID))
}

// RestoreIDIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDIn(restoreID ...int) EsSnapshotRestoreQuerySet {
	if len(restoreID) == 0 {
		qs.db.AddError(errors.New("must at least pass one restoreID in RestoreIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("restore_id IN (?)", restoreID))
}

// RestoreIDLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDLt(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id < ?", restoreID))
}

// RestoreIDLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDLte(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id <= ?", restoreID))
}

// RestoreIDNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDNe(restoreID int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("restore_id != ?", restoreID))
}

// RestoreIDNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) RestoreIDNotIn(restoreID ...int) EsSnapshotRestoreQuerySet {
	if len(restoreID) == 0 {
		qs.db.AddError(errors.New("must at least pass one restoreID in RestoreIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("restore_id NOT IN (?)", restoreID))
}

// StartTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeEq(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time = ?", startTime))
}

// StartTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeGt(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time > ?", startTime))
}

// StartTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeGte(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time >= ?", startTime))
}

// StartTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeLt(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time < ?", startTime))
}

// StartTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeLte(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time <= ?", startTime))
}

// StartTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) StartTimeNe(startTime time.Time) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("start_time != ?", startTime))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDEq(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDGt(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDGte(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDIn(tableID ...string) EsSnapshotRestoreQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDLike(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDLt(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDLte(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDNe(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDNotIn(tableID ...string) EsSnapshotRestoreQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TableIDNotlike(tableID string) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TotalDocCountEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountEq(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count = ?", totalDocCount))
}

// TotalDocCountGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountGt(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count > ?", totalDocCount))
}

// TotalDocCountGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountGte(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count >= ?", totalDocCount))
}

// TotalDocCountIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountIn(totalDocCount ...int) EsSnapshotRestoreQuerySet {
	if len(totalDocCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one totalDocCount in TotalDocCountIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("total_doc_count IN (?)", totalDocCount))
}

// TotalDocCountLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountLt(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count < ?", totalDocCount))
}

// TotalDocCountLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountLte(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count <= ?", totalDocCount))
}

// TotalDocCountNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountNe(totalDocCount int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_doc_count != ?", totalDocCount))
}

// TotalDocCountNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalDocCountNotIn(totalDocCount ...int) EsSnapshotRestoreQuerySet {
	if len(totalDocCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one totalDocCount in TotalDocCountNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("total_doc_count NOT IN (?)", totalDocCount))
}

// TotalStoreSizeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeEq(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size = ?", totalStoreSize))
}

// TotalStoreSizeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeGt(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size > ?", totalStoreSize))
}

// TotalStoreSizeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeGte(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size >= ?", totalStoreSize))
}

// TotalStoreSizeIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeIn(totalStoreSize ...int) EsSnapshotRestoreQuerySet {
	if len(totalStoreSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one totalStoreSize in TotalStoreSizeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("total_store_size IN (?)", totalStoreSize))
}

// TotalStoreSizeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeLt(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size < ?", totalStoreSize))
}

// TotalStoreSizeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeLte(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size <= ?", totalStoreSize))
}

// TotalStoreSizeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeNe(totalStoreSize int) EsSnapshotRestoreQuerySet {
	return qs.w(qs.db.Where("total_store_size != ?", totalStoreSize))
}

// TotalStoreSizeNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotRestoreQuerySet) TotalStoreSizeNotIn(totalStoreSize ...int) EsSnapshotRestoreQuerySet {
	if len(totalStoreSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one totalStoreSize in TotalStoreSizeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("total_store_size NOT IN (?)", totalStoreSize))
}

// SetCompleteDocCount is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetCompleteDocCount(completeDocCount int) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.CompleteDocCount)] = completeDocCount
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetCreateTime(createTime time.Time) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetCreator(creator string) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.Creator)] = creator
	return u
}

// SetDuration is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetDuration(duration int) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.Duration)] = duration
	return u
}

// SetEndTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetEndTime(endTime time.Time) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.EndTime)] = endTime
	return u
}

// SetExpiredDelete is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetExpiredDelete(expiredDelete bool) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.ExpiredDelete)] = expiredDelete
	return u
}

// SetExpiredTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetExpiredTime(expiredTime time.Time) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.ExpiredTime)] = expiredTime
	return u
}

// SetIndices is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetIndices(indices string) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.Indices)] = indices
	return u
}

// SetIsDeleted is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetIsDeleted(isDeleted bool) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.IsDeleted)] = isDeleted
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetLastModifyTime(lastModifyTime time.Time) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetLastModifyUser(lastModifyUser string) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetRestoreID is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetRestoreID(restoreID int) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.RestoreID)] = restoreID
	return u
}

// SetStartTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetStartTime(startTime time.Time) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.StartTime)] = startTime
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetTableID(tableID string) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.TableID)] = tableID
	return u
}

// SetTotalDocCount is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetTotalDocCount(totalDocCount int) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.TotalDocCount)] = totalDocCount
	return u
}

// SetTotalStoreSize is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) SetTotalStoreSize(totalStoreSize int) EsSnapshotRestoreUpdater {
	u.fields[string(EsSnapshotRestoreDBSchema.TotalStoreSize)] = totalStoreSize
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u EsSnapshotRestoreUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set EsSnapshotRestoreQuerySet

// ===== BEGIN of EsSnapshotRestore modifiers

// EsSnapshotRestoreDBSchemaField describes database schema field. It requires for method 'Update'
type EsSnapshotRestoreDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f EsSnapshotRestoreDBSchemaField) String() string {
	return string(f)
}

// EsSnapshotRestoreDBSchema stores db field names of EsSnapshotRestore
var EsSnapshotRestoreDBSchema = struct {
	RestoreID        EsSnapshotRestoreDBSchemaField
	TableID          EsSnapshotRestoreDBSchemaField
	StartTime        EsSnapshotRestoreDBSchemaField
	EndTime          EsSnapshotRestoreDBSchemaField
	ExpiredTime      EsSnapshotRestoreDBSchemaField
	ExpiredDelete    EsSnapshotRestoreDBSchemaField
	Indices          EsSnapshotRestoreDBSchemaField
	CompleteDocCount EsSnapshotRestoreDBSchemaField
	TotalDocCount    EsSnapshotRestoreDBSchemaField
	TotalStoreSize   EsSnapshotRestoreDBSchemaField
	Duration         EsSnapshotRestoreDBSchemaField
	Creator          EsSnapshotRestoreDBSchemaField
	CreateTime       EsSnapshotRestoreDBSchemaField
	LastModifyUser   EsSnapshotRestoreDBSchemaField
	LastModifyTime   EsSnapshotRestoreDBSchemaField
	IsDeleted        EsSnapshotRestoreDBSchemaField
}{

	RestoreID:        EsSnapshotRestoreDBSchemaField("restore_id"),
	TableID:          EsSnapshotRestoreDBSchemaField("table_id"),
	StartTime:        EsSnapshotRestoreDBSchemaField("start_time"),
	EndTime:          EsSnapshotRestoreDBSchemaField("end_time"),
	ExpiredTime:      EsSnapshotRestoreDBSchemaField("expired_time"),
	ExpiredDelete:    EsSnapshotRestoreDBSchemaField("expired_delete"),
	Indices:          EsSnapshotRestoreDBSchemaField("indices"),
	CompleteDocCount: EsSnapshotRestoreDBSchemaField("complete_doc_count"),
	TotalDocCount:    EsSnapshotRestoreDBSchemaField("total_doc_count"),
	TotalStoreSize:   EsSnapshotRestoreDBSchemaField("total_store_size"),
	Duration:         EsSnapshotRestoreDBSchemaField("duration"),
	Creator:          EsSnapshotRestoreDBSchemaField("creator"),
	CreateTime:       EsSnapshotRestoreDBSchemaField("create_time"),
	LastModifyUser:   EsSnapshotRestoreDBSchemaField("last_modify_user"),
	LastModifyTime:   EsSnapshotRestoreDBSchemaField("last_modify_time"),
	IsDeleted:        EsSnapshotRestoreDBSchemaField("is_deleted"),
}

// Update updates EsSnapshotRestore fields by primary key
// nolint: dupl
func (o *EsSnapshotRestore) Update(db *gorm.DB, fields ...EsSnapshotRestoreDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"restore_id":         o.RestoreID,
		"table_id":           o.TableID,
		"start_time":         o.StartTime,
		"end_time":           o.EndTime,
		"expired_time":       o.ExpiredTime,
		"expired_delete":     o.ExpiredDelete,
		"indices":            o.Indices,
		"complete_doc_count": o.CompleteDocCount,
		"total_doc_count":    o.TotalDocCount,
		"total_store_size":   o.TotalStoreSize,
		"duration":           o.Duration,
		"creator":            o.Creator,
		"create_time":        o.CreateTime,
		"last_modify_user":   o.LastModifyUser,
		"last_modify_time":   o.LastModifyTime,
		"is_deleted":         o.IsDeleted,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update EsSnapshotRestore %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// EsSnapshotRestoreUpdater is an EsSnapshotRestore updates manager
type EsSnapshotRestoreUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewEsSnapshotRestoreUpdater creates new EsSnapshotRestore updater
// nolint: dupl
func NewEsSnapshotRestoreUpdater(db *gorm.DB) EsSnapshotRestoreUpdater {
	return EsSnapshotRestoreUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&EsSnapshotRestore{}),
	}
}

// ===== END of EsSnapshotRestore modifiers

// ===== END of all query sets
