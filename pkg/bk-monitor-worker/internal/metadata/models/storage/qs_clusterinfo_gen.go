// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ClusterInfoQuerySet

// ClusterInfoQuerySet is an queryset type for ClusterInfo
type ClusterInfoQuerySet struct {
	db *gorm.DB
}

// NewClusterInfoQuerySet constructs new ClusterInfoQuerySet
func NewClusterInfoQuerySet(db *gorm.DB) ClusterInfoQuerySet {
	return ClusterInfoQuerySet{
		db: db.Model(&ClusterInfo{}),
	}
}

func (qs ClusterInfoQuerySet) w(db *gorm.DB) ClusterInfoQuerySet {
	return NewClusterInfoQuerySet(db)
}

func (qs ClusterInfoQuerySet) Select(fields ...ClusterInfoDBSchemaField) ClusterInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ClusterInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ClusterInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) All(ret *[]ClusterInfo) error {
	return qs.db.Find(ret).Error
}

// ClusterIDEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDEq(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterID))
}

// ClusterIDGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDGt(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterID))
}

// ClusterIDGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDGte(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterID))
}

// ClusterIDIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDIn(clusterID ...uint) ClusterInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterID))
}

// ClusterIDLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDLt(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterID))
}

// ClusterIDLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDLte(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterID))
}

// ClusterIDNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDNe(clusterID uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterID))
}

// ClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterIDNotIn(clusterID ...uint) ClusterInfoQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterID))
}

// ClusterNameEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameEq(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name = ?", clusterName))
}

// ClusterNameGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameGt(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name > ?", clusterName))
}

// ClusterNameGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameGte(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name >= ?", clusterName))
}

// ClusterNameIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameIn(clusterName ...string) ClusterInfoQuerySet {
	if len(clusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterName in ClusterNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_name IN (?)", clusterName))
}

// ClusterNameLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameLike(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name LIKE ?", clusterName))
}

// ClusterNameLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameLt(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name < ?", clusterName))
}

// ClusterNameLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameLte(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name <= ?", clusterName))
}

// ClusterNameNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameNe(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name != ?", clusterName))
}

// ClusterNameNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameNotIn(clusterName ...string) ClusterInfoQuerySet {
	if len(clusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterName in ClusterNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_name NOT IN (?)", clusterName))
}

// ClusterNameNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterNameNotlike(clusterName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name NOT LIKE ?", clusterName))
}

// ClusterTypeEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeEq(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type = ?", clusterType))
}

// ClusterTypeGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeGt(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type > ?", clusterType))
}

// ClusterTypeGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeGte(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type >= ?", clusterType))
}

// ClusterTypeIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeIn(clusterType ...string) ClusterInfoQuerySet {
	if len(clusterType) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterType in ClusterTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_type IN (?)", clusterType))
}

// ClusterTypeLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeLike(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type LIKE ?", clusterType))
}

// ClusterTypeLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeLt(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type < ?", clusterType))
}

// ClusterTypeLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeLte(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type <= ?", clusterType))
}

// ClusterTypeNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeNe(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type != ?", clusterType))
}

// ClusterTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeNotIn(clusterType ...string) ClusterInfoQuerySet {
	if len(clusterType) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterType in ClusterTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_type NOT IN (?)", clusterType))
}

// ClusterTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ClusterTypeNotlike(clusterType string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_type NOT LIKE ?", clusterType))
}

// Count is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeEq(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeGt(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeGte(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeLt(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeLte(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreateTimeNe(createTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorEq(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorGt(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorGte(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorIn(creator ...string) ClusterInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorLike(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorLt(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorLte(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorNe(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorNotIn(creator ...string) ClusterInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CreatorNotlike(creator string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// CustomOptionEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionEq(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option = ?", customOption))
}

// CustomOptionGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionGt(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option > ?", customOption))
}

// CustomOptionGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionGte(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option >= ?", customOption))
}

// CustomOptionIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionIn(customOption ...string) ClusterInfoQuerySet {
	if len(customOption) == 0 {
		qs.db.AddError(errors.New("must at least pass one customOption in CustomOptionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_option IN (?)", customOption))
}

// CustomOptionLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionLike(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option LIKE ?", customOption))
}

// CustomOptionLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionLt(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option < ?", customOption))
}

// CustomOptionLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionLte(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option <= ?", customOption))
}

// CustomOptionNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionNe(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option != ?", customOption))
}

// CustomOptionNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionNotIn(customOption ...string) ClusterInfoQuerySet {
	if len(customOption) == 0 {
		qs.db.AddError(errors.New("must at least pass one customOption in CustomOptionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_option NOT IN (?)", customOption))
}

// CustomOptionNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) CustomOptionNotlike(customOption string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("custom_option NOT LIKE ?", customOption))
}

// DefaultSettingsEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsEq(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings = ?", defaultSettings))
}

// DefaultSettingsGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsGt(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings > ?", defaultSettings))
}

// DefaultSettingsGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsGte(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings >= ?", defaultSettings))
}

// DefaultSettingsIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsIn(defaultSettings ...string) ClusterInfoQuerySet {
	if len(defaultSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultSettings in DefaultSettingsIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_settings IN (?)", defaultSettings))
}

// DefaultSettingsLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsLike(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings LIKE ?", defaultSettings))
}

// DefaultSettingsLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsLt(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings < ?", defaultSettings))
}

// DefaultSettingsLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsLte(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings <= ?", defaultSettings))
}

// DefaultSettingsNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsNe(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings != ?", defaultSettings))
}

// DefaultSettingsNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsNotIn(defaultSettings ...string) ClusterInfoQuerySet {
	if len(defaultSettings) == 0 {
		qs.db.AddError(errors.New("must at least pass one defaultSettings in DefaultSettingsNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("default_settings NOT IN (?)", defaultSettings))
}

// DefaultSettingsNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DefaultSettingsNotlike(defaultSettings string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("default_settings NOT LIKE ?", defaultSettings))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) Delete() error {
	return qs.db.Delete(ClusterInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ClusterInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ClusterInfo{})
	return db.RowsAffected, db.Error
}

// DescriptionEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionEq(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description = ?", description))
}

// DescriptionGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionGt(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description > ?", description))
}

// DescriptionGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionGte(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description >= ?", description))
}

// DescriptionIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionIn(description ...string) ClusterInfoQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description IN (?)", description))
}

// DescriptionLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionLike(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description LIKE ?", description))
}

// DescriptionLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionLt(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description < ?", description))
}

// DescriptionLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionLte(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description <= ?", description))
}

// DescriptionNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionNe(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description != ?", description))
}

// DescriptionNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionNotIn(description ...string) ClusterInfoQuerySet {
	if len(description) == 0 {
		qs.db.AddError(errors.New("must at least pass one description in DescriptionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("description NOT IN (?)", description))
}

// DescriptionNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DescriptionNotlike(description string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("description NOT LIKE ?", description))
}

// DomainNameEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameEq(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name = ?", domainName))
}

// DomainNameGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameGt(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name > ?", domainName))
}

// DomainNameGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameGte(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name >= ?", domainName))
}

// DomainNameIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameIn(domainName ...string) ClusterInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name IN (?)", domainName))
}

// DomainNameLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameLike(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name LIKE ?", domainName))
}

// DomainNameLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameLt(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name < ?", domainName))
}

// DomainNameLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameLte(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name <= ?", domainName))
}

// DomainNameNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameNe(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name != ?", domainName))
}

// DomainNameNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameNotIn(domainName ...string) ClusterInfoQuerySet {
	if len(domainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one domainName in DomainNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("domain_name NOT IN (?)", domainName))
}

// DomainNameNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) DomainNameNotlike(domainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("domain_name NOT LIKE ?", domainName))
}

// ExtranetDomainNameEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameEq(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name = ?", extranetDomainName))
}

// ExtranetDomainNameGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameGt(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name > ?", extranetDomainName))
}

// ExtranetDomainNameGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameGte(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name >= ?", extranetDomainName))
}

// ExtranetDomainNameIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameIn(extranetDomainName ...string) ClusterInfoQuerySet {
	if len(extranetDomainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one extranetDomainName in ExtranetDomainNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("extranet_domain_name IN (?)", extranetDomainName))
}

// ExtranetDomainNameLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameLike(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name LIKE ?", extranetDomainName))
}

// ExtranetDomainNameLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameLt(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name < ?", extranetDomainName))
}

// ExtranetDomainNameLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameLte(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name <= ?", extranetDomainName))
}

// ExtranetDomainNameNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameNe(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name != ?", extranetDomainName))
}

// ExtranetDomainNameNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameNotIn(extranetDomainName ...string) ClusterInfoQuerySet {
	if len(extranetDomainName) == 0 {
		qs.db.AddError(errors.New("must at least pass one extranetDomainName in ExtranetDomainNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("extranet_domain_name NOT IN (?)", extranetDomainName))
}

// ExtranetDomainNameNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetDomainNameNotlike(extranetDomainName string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_domain_name NOT LIKE ?", extranetDomainName))
}

// ExtranetPortEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortEq(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port = ?", extranetPort))
}

// ExtranetPortGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortGt(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port > ?", extranetPort))
}

// ExtranetPortGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortGte(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port >= ?", extranetPort))
}

// ExtranetPortIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortIn(extranetPort ...uint) ClusterInfoQuerySet {
	if len(extranetPort) == 0 {
		qs.db.AddError(errors.New("must at least pass one extranetPort in ExtranetPortIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("extranet_port IN (?)", extranetPort))
}

// ExtranetPortLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortLt(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port < ?", extranetPort))
}

// ExtranetPortLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortLte(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port <= ?", extranetPort))
}

// ExtranetPortNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortNe(extranetPort uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("extranet_port != ?", extranetPort))
}

// ExtranetPortNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) ExtranetPortNotIn(extranetPort ...uint) ClusterInfoQuerySet {
	if len(extranetPort) == 0 {
		qs.db.AddError(errors.New("must at least pass one extranetPort in ExtranetPortNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("extranet_port NOT IN (?)", extranetPort))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GetUpdater() ClusterInfoUpdater {
	return NewClusterInfoUpdater(qs.db)
}

// GseStreamToIdEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdEq(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id = ?", gseStreamToId))
}

// GseStreamToIdGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdGt(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id > ?", gseStreamToId))
}

// GseStreamToIdGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdGte(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id >= ?", gseStreamToId))
}

// GseStreamToIdIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdIn(gseStreamToId ...int) ClusterInfoQuerySet {
	if len(gseStreamToId) == 0 {
		qs.db.AddError(errors.New("must at least pass one gseStreamToId in GseStreamToIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("gse_stream_to_id IN (?)", gseStreamToId))
}

// GseStreamToIdLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdLt(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id < ?", gseStreamToId))
}

// GseStreamToIdLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdLte(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id <= ?", gseStreamToId))
}

// GseStreamToIdNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdNe(gseStreamToId int) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("gse_stream_to_id != ?", gseStreamToId))
}

// GseStreamToIdNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) GseStreamToIdNotIn(gseStreamToId ...int) ClusterInfoQuerySet {
	if len(gseStreamToId) == 0 {
		qs.db.AddError(errors.New("must at least pass one gseStreamToId in GseStreamToIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("gse_stream_to_id NOT IN (?)", gseStreamToId))
}

// IsAuthEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsAuthEq(isAuth bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_auth = ?", isAuth))
}

// IsAuthIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsAuthIn(isAuth ...bool) ClusterInfoQuerySet {
	if len(isAuth) == 0 {
		qs.db.AddError(errors.New("must at least pass one isAuth in IsAuthIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_auth IN (?)", isAuth))
}

// IsAuthNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsAuthNe(isAuth bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_auth != ?", isAuth))
}

// IsAuthNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsAuthNotIn(isAuth ...bool) ClusterInfoQuerySet {
	if len(isAuth) == 0 {
		qs.db.AddError(errors.New("must at least pass one isAuth in IsAuthNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_auth NOT IN (?)", isAuth))
}

// IsDefaultClusterEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsDefaultClusterEq(isDefaultCluster bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_default_cluster = ?", isDefaultCluster))
}

// IsDefaultClusterIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsDefaultClusterIn(isDefaultCluster ...bool) ClusterInfoQuerySet {
	if len(isDefaultCluster) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDefaultCluster in IsDefaultClusterIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_default_cluster IN (?)", isDefaultCluster))
}

// IsDefaultClusterNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsDefaultClusterNe(isDefaultCluster bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_default_cluster != ?", isDefaultCluster))
}

// IsDefaultClusterNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsDefaultClusterNotIn(isDefaultCluster ...bool) ClusterInfoQuerySet {
	if len(isDefaultCluster) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDefaultCluster in IsDefaultClusterNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_default_cluster NOT IN (?)", isDefaultCluster))
}

// IsRegisterToGseEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsRegisterToGseEq(isRegisterToGse bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_register_to_gse = ?", isRegisterToGse))
}

// IsRegisterToGseIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsRegisterToGseIn(isRegisterToGse ...bool) ClusterInfoQuerySet {
	if len(isRegisterToGse) == 0 {
		qs.db.AddError(errors.New("must at least pass one isRegisterToGse in IsRegisterToGseIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_register_to_gse IN (?)", isRegisterToGse))
}

// IsRegisterToGseNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsRegisterToGseNe(isRegisterToGse bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_register_to_gse != ?", isRegisterToGse))
}

// IsRegisterToGseNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsRegisterToGseNotIn(isRegisterToGse ...bool) ClusterInfoQuerySet {
	if len(isRegisterToGse) == 0 {
		qs.db.AddError(errors.New("must at least pass one isRegisterToGse in IsRegisterToGseNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_register_to_gse NOT IN (?)", isRegisterToGse))
}

// IsSslVerifyEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsSslVerifyEq(isSslVerify bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_ssl_verify = ?", isSslVerify))
}

// IsSslVerifyIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsSslVerifyIn(isSslVerify ...bool) ClusterInfoQuerySet {
	if len(isSslVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSslVerify in IsSslVerifyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_ssl_verify IN (?)", isSslVerify))
}

// IsSslVerifyNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsSslVerifyNe(isSslVerify bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("is_ssl_verify != ?", isSslVerify))
}

// IsSslVerifyNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) IsSslVerifyNotIn(isSslVerify ...bool) ClusterInfoQuerySet {
	if len(isSslVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSslVerify in IsSslVerifyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_ssl_verify NOT IN (?)", isSslVerify))
}

// LabelEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelEq(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label = ?", label))
}

// LabelGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelGt(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label > ?", label))
}

// LabelGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelGte(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label >= ?", label))
}

// LabelIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelIn(label ...string) ClusterInfoQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label IN (?)", label))
}

// LabelLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelLike(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label LIKE ?", label))
}

// LabelLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelLt(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label < ?", label))
}

// LabelLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelLte(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label <= ?", label))
}

// LabelNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelNe(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label != ?", label))
}

// LabelNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelNotIn(label ...string) ClusterInfoQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label NOT IN (?)", label))
}

// LabelNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LabelNotlike(label string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("label NOT LIKE ?", label))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeEq(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeGt(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeGte(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeLt(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeLte(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyTimeNe(lastModifyTime time.Time) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserEq(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserGt(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserGte(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserIn(lastModifyUser ...string) ClusterInfoQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserLike(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserLt(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserLte(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserNe(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserNotIn(lastModifyUser ...string) ClusterInfoQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) LastModifyUserNotlike(lastModifyUser string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) Limit(limit int) ClusterInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) Offset(offset int) ClusterInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ClusterInfoQuerySet) One(ret *ClusterInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByClusterID is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByClusterID() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscByClusterName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByClusterName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_name ASC"))
}

// OrderAscByClusterType is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByClusterType() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_type ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByCreateTime() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByCreator() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByCustomOption is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByCustomOption() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("custom_option ASC"))
}

// OrderAscByDefaultSettings is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByDefaultSettings() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("default_settings ASC"))
}

// OrderAscByDescription is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByDescription() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("description ASC"))
}

// OrderAscByDomainName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByDomainName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("domain_name ASC"))
}

// OrderAscByExtranetDomainName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByExtranetDomainName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("extranet_domain_name ASC"))
}

// OrderAscByExtranetPort is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByExtranetPort() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("extranet_port ASC"))
}

// OrderAscByGseStreamToId is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByGseStreamToId() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("gse_stream_to_id ASC"))
}

// OrderAscByIsAuth is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByIsAuth() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_auth ASC"))
}

// OrderAscByIsDefaultCluster is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByIsDefaultCluster() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_default_cluster ASC"))
}

// OrderAscByIsRegisterToGse is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByIsRegisterToGse() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_register_to_gse ASC"))
}

// OrderAscByIsSslVerify is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByIsSslVerify() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_ssl_verify ASC"))
}

// OrderAscByLabel is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByLabel() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("label ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByLastModifyTime() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByLastModifyUser() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByPassword is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByPassword() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("password ASC"))
}

// OrderAscByPort is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByPort() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("port ASC"))
}

// OrderAscByRegisteredSystem is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByRegisteredSystem() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("registered_system ASC"))
}

// OrderAscBySchema is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySchema() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("schema ASC"))
}

// OrderAscBySslCertificate is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySslCertificate() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate ASC"))
}

// OrderAscBySslCertificateAuthorities is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySslCertificateAuthorities() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate_authorities ASC"))
}

// OrderAscBySslCertificateKey is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySslCertificateKey() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate_key ASC"))
}

// OrderAscBySslInsecureSkipVerify is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySslInsecureSkipVerify() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_insecure_skip_verify ASC"))
}

// OrderAscBySslVerificationMode is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscBySslVerificationMode() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_verification_mode ASC"))
}

// OrderAscByUsername is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByUsername() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("username ASC"))
}

// OrderAscByVersion is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderAscByVersion() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("version ASC"))
}

// OrderDescByClusterID is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByClusterID() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescByClusterName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByClusterName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_name DESC"))
}

// OrderDescByClusterType is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByClusterType() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_type DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByCreateTime() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByCreator() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByCustomOption is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByCustomOption() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("custom_option DESC"))
}

// OrderDescByDefaultSettings is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByDefaultSettings() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("default_settings DESC"))
}

// OrderDescByDescription is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByDescription() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("description DESC"))
}

// OrderDescByDomainName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByDomainName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("domain_name DESC"))
}

// OrderDescByExtranetDomainName is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByExtranetDomainName() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("extranet_domain_name DESC"))
}

// OrderDescByExtranetPort is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByExtranetPort() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("extranet_port DESC"))
}

// OrderDescByGseStreamToId is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByGseStreamToId() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("gse_stream_to_id DESC"))
}

// OrderDescByIsAuth is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByIsAuth() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_auth DESC"))
}

// OrderDescByIsDefaultCluster is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByIsDefaultCluster() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_default_cluster DESC"))
}

// OrderDescByIsRegisterToGse is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByIsRegisterToGse() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_register_to_gse DESC"))
}

// OrderDescByIsSslVerify is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByIsSslVerify() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("is_ssl_verify DESC"))
}

// OrderDescByLabel is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByLabel() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("label DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByLastModifyTime() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByLastModifyUser() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByPassword is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByPassword() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("password DESC"))
}

// OrderDescByPort is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByPort() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("port DESC"))
}

// OrderDescByRegisteredSystem is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByRegisteredSystem() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("registered_system DESC"))
}

// OrderDescBySchema is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySchema() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("schema DESC"))
}

// OrderDescBySslCertificate is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySslCertificate() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate DESC"))
}

// OrderDescBySslCertificateAuthorities is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySslCertificateAuthorities() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate_authorities DESC"))
}

// OrderDescBySslCertificateKey is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySslCertificateKey() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_certificate_key DESC"))
}

// OrderDescBySslInsecureSkipVerify is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySslInsecureSkipVerify() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_insecure_skip_verify DESC"))
}

// OrderDescBySslVerificationMode is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescBySslVerificationMode() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("ssl_verification_mode DESC"))
}

// OrderDescByUsername is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByUsername() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("username DESC"))
}

// OrderDescByVersion is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) OrderDescByVersion() ClusterInfoQuerySet {
	return qs.w(qs.db.Order("version DESC"))
}

// PasswordEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordEq(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password = ?", password))
}

// PasswordGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordGt(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password > ?", password))
}

// PasswordGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordGte(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password >= ?", password))
}

// PasswordIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordIn(password ...string) ClusterInfoQuerySet {
	if len(password) == 0 {
		qs.db.AddError(errors.New("must at least pass one password in PasswordIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("password IN (?)", password))
}

// PasswordLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordLike(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password LIKE ?", password))
}

// PasswordLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordLt(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password < ?", password))
}

// PasswordLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordLte(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password <= ?", password))
}

// PasswordNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordNe(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password != ?", password))
}

// PasswordNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordNotIn(password ...string) ClusterInfoQuerySet {
	if len(password) == 0 {
		qs.db.AddError(errors.New("must at least pass one password in PasswordNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("password NOT IN (?)", password))
}

// PasswordNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PasswordNotlike(password string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("password NOT LIKE ?", password))
}

// PortEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortEq(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port = ?", port))
}

// PortGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortGt(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port > ?", port))
}

// PortGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortGte(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port >= ?", port))
}

// PortIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortIn(port ...uint) ClusterInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port IN (?)", port))
}

// PortLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortLt(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port < ?", port))
}

// PortLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortLte(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port <= ?", port))
}

// PortNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortNe(port uint) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("port != ?", port))
}

// PortNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) PortNotIn(port ...uint) ClusterInfoQuerySet {
	if len(port) == 0 {
		qs.db.AddError(errors.New("must at least pass one port in PortNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("port NOT IN (?)", port))
}

// RegisteredSystemEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemEq(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system = ?", registeredSystem))
}

// RegisteredSystemGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemGt(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system > ?", registeredSystem))
}

// RegisteredSystemGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemGte(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system >= ?", registeredSystem))
}

// RegisteredSystemIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemIn(registeredSystem ...string) ClusterInfoQuerySet {
	if len(registeredSystem) == 0 {
		qs.db.AddError(errors.New("must at least pass one registeredSystem in RegisteredSystemIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("registered_system IN (?)", registeredSystem))
}

// RegisteredSystemLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemLike(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system LIKE ?", registeredSystem))
}

// RegisteredSystemLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemLt(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system < ?", registeredSystem))
}

// RegisteredSystemLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemLte(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system <= ?", registeredSystem))
}

// RegisteredSystemNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemNe(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system != ?", registeredSystem))
}

// RegisteredSystemNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemNotIn(registeredSystem ...string) ClusterInfoQuerySet {
	if len(registeredSystem) == 0 {
		qs.db.AddError(errors.New("must at least pass one registeredSystem in RegisteredSystemNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("registered_system NOT IN (?)", registeredSystem))
}

// RegisteredSystemNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) RegisteredSystemNotlike(registeredSystem string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("registered_system NOT LIKE ?", registeredSystem))
}

// SchemaEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaEq(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema = ?", schema))
}

// SchemaGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaGt(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema > ?", schema))
}

// SchemaGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaGte(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema >= ?", schema))
}

// SchemaIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaIn(schema ...string) ClusterInfoQuerySet {
	if len(schema) == 0 {
		qs.db.AddError(errors.New("must at least pass one schema in SchemaIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("schema IN (?)", schema))
}

// SchemaIsNotNull is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaIsNotNull() ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema IS NOT NULL"))
}

// SchemaIsNull is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaIsNull() ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema IS NULL"))
}

// SchemaLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaLike(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema LIKE ?", schema))
}

// SchemaLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaLt(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema < ?", schema))
}

// SchemaLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaLte(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema <= ?", schema))
}

// SchemaNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaNe(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema != ?", schema))
}

// SchemaNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaNotIn(schema ...string) ClusterInfoQuerySet {
	if len(schema) == 0 {
		qs.db.AddError(errors.New("must at least pass one schema in SchemaNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("schema NOT IN (?)", schema))
}

// SchemaNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SchemaNotlike(schema string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("schema NOT LIKE ?", schema))
}

// SslCertificateAuthoritiesEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesEq(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities = ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesGt(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities > ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesGte(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities >= ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesIn(sslCertificateAuthorities ...string) ClusterInfoQuerySet {
	if len(sslCertificateAuthorities) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificateAuthorities in SslCertificateAuthoritiesIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate_authorities IN (?)", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesLike(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities LIKE ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesLt(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities < ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesLte(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities <= ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesNe(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities != ?", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesNotIn(sslCertificateAuthorities ...string) ClusterInfoQuerySet {
	if len(sslCertificateAuthorities) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificateAuthorities in SslCertificateAuthoritiesNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate_authorities NOT IN (?)", sslCertificateAuthorities))
}

// SslCertificateAuthoritiesNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateAuthoritiesNotlike(sslCertificateAuthorities string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_authorities NOT LIKE ?", sslCertificateAuthorities))
}

// SslCertificateEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateEq(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate = ?", sslCertificate))
}

// SslCertificateGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateGt(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate > ?", sslCertificate))
}

// SslCertificateGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateGte(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate >= ?", sslCertificate))
}

// SslCertificateIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateIn(sslCertificate ...string) ClusterInfoQuerySet {
	if len(sslCertificate) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificate in SslCertificateIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate IN (?)", sslCertificate))
}

// SslCertificateKeyEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyEq(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key = ?", sslCertificateKey))
}

// SslCertificateKeyGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyGt(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key > ?", sslCertificateKey))
}

// SslCertificateKeyGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyGte(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key >= ?", sslCertificateKey))
}

// SslCertificateKeyIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyIn(sslCertificateKey ...string) ClusterInfoQuerySet {
	if len(sslCertificateKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificateKey in SslCertificateKeyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate_key IN (?)", sslCertificateKey))
}

// SslCertificateKeyLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyLike(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key LIKE ?", sslCertificateKey))
}

// SslCertificateKeyLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyLt(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key < ?", sslCertificateKey))
}

// SslCertificateKeyLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyLte(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key <= ?", sslCertificateKey))
}

// SslCertificateKeyNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyNe(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key != ?", sslCertificateKey))
}

// SslCertificateKeyNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyNotIn(sslCertificateKey ...string) ClusterInfoQuerySet {
	if len(sslCertificateKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificateKey in SslCertificateKeyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate_key NOT IN (?)", sslCertificateKey))
}

// SslCertificateKeyNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateKeyNotlike(sslCertificateKey string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate_key NOT LIKE ?", sslCertificateKey))
}

// SslCertificateLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateLike(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate LIKE ?", sslCertificate))
}

// SslCertificateLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateLt(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate < ?", sslCertificate))
}

// SslCertificateLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateLte(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate <= ?", sslCertificate))
}

// SslCertificateNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateNe(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate != ?", sslCertificate))
}

// SslCertificateNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateNotIn(sslCertificate ...string) ClusterInfoQuerySet {
	if len(sslCertificate) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslCertificate in SslCertificateNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_certificate NOT IN (?)", sslCertificate))
}

// SslCertificateNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslCertificateNotlike(sslCertificate string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_certificate NOT LIKE ?", sslCertificate))
}

// SslInsecureSkipVerifyEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslInsecureSkipVerifyEq(sslInsecureSkipVerify bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_insecure_skip_verify = ?", sslInsecureSkipVerify))
}

// SslInsecureSkipVerifyIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslInsecureSkipVerifyIn(sslInsecureSkipVerify ...bool) ClusterInfoQuerySet {
	if len(sslInsecureSkipVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslInsecureSkipVerify in SslInsecureSkipVerifyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_insecure_skip_verify IN (?)", sslInsecureSkipVerify))
}

// SslInsecureSkipVerifyNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslInsecureSkipVerifyNe(sslInsecureSkipVerify bool) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_insecure_skip_verify != ?", sslInsecureSkipVerify))
}

// SslInsecureSkipVerifyNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslInsecureSkipVerifyNotIn(sslInsecureSkipVerify ...bool) ClusterInfoQuerySet {
	if len(sslInsecureSkipVerify) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslInsecureSkipVerify in SslInsecureSkipVerifyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_insecure_skip_verify NOT IN (?)", sslInsecureSkipVerify))
}

// SslVerificationModeEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeEq(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode = ?", sslVerificationMode))
}

// SslVerificationModeGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeGt(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode > ?", sslVerificationMode))
}

// SslVerificationModeGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeGte(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode >= ?", sslVerificationMode))
}

// SslVerificationModeIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeIn(sslVerificationMode ...string) ClusterInfoQuerySet {
	if len(sslVerificationMode) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslVerificationMode in SslVerificationModeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_verification_mode IN (?)", sslVerificationMode))
}

// SslVerificationModeLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeLike(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode LIKE ?", sslVerificationMode))
}

// SslVerificationModeLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeLt(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode < ?", sslVerificationMode))
}

// SslVerificationModeLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeLte(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode <= ?", sslVerificationMode))
}

// SslVerificationModeNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeNe(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode != ?", sslVerificationMode))
}

// SslVerificationModeNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeNotIn(sslVerificationMode ...string) ClusterInfoQuerySet {
	if len(sslVerificationMode) == 0 {
		qs.db.AddError(errors.New("must at least pass one sslVerificationMode in SslVerificationModeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ssl_verification_mode NOT IN (?)", sslVerificationMode))
}

// SslVerificationModeNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) SslVerificationModeNotlike(sslVerificationMode string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("ssl_verification_mode NOT LIKE ?", sslVerificationMode))
}

// UsernameEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameEq(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username = ?", username))
}

// UsernameGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameGt(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username > ?", username))
}

// UsernameGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameGte(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username >= ?", username))
}

// UsernameIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameIn(username ...string) ClusterInfoQuerySet {
	if len(username) == 0 {
		qs.db.AddError(errors.New("must at least pass one username in UsernameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("username IN (?)", username))
}

// UsernameLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameLike(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username LIKE ?", username))
}

// UsernameLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameLt(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username < ?", username))
}

// UsernameLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameLte(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username <= ?", username))
}

// UsernameNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameNe(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username != ?", username))
}

// UsernameNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameNotIn(username ...string) ClusterInfoQuerySet {
	if len(username) == 0 {
		qs.db.AddError(errors.New("must at least pass one username in UsernameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("username NOT IN (?)", username))
}

// UsernameNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) UsernameNotlike(username string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("username NOT LIKE ?", username))
}

// VersionEq is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionEq(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version = ?", version))
}

// VersionGt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionGt(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version > ?", version))
}

// VersionGte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionGte(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version >= ?", version))
}

// VersionIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionIn(version ...string) ClusterInfoQuerySet {
	if len(version) == 0 {
		qs.db.AddError(errors.New("must at least pass one version in VersionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("version IN (?)", version))
}

// VersionIsNotNull is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionIsNotNull() ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version IS NOT NULL"))
}

// VersionIsNull is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionIsNull() ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version IS NULL"))
}

// VersionLike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionLike(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version LIKE ?", version))
}

// VersionLt is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionLt(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version < ?", version))
}

// VersionLte is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionLte(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version <= ?", version))
}

// VersionNe is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionNe(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version != ?", version))
}

// VersionNotIn is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionNotIn(version ...string) ClusterInfoQuerySet {
	if len(version) == 0 {
		qs.db.AddError(errors.New("must at least pass one version in VersionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("version NOT IN (?)", version))
}

// VersionNotlike is an autogenerated method
// nolint: dupl
func (qs ClusterInfoQuerySet) VersionNotlike(version string) ClusterInfoQuerySet {
	return qs.w(qs.db.Where("version NOT LIKE ?", version))
}

// SetClusterID is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetClusterID(clusterID uint) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.ClusterID)] = clusterID
	return u
}

// SetClusterName is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetClusterName(clusterName string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.ClusterName)] = clusterName
	return u
}

// SetClusterType is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetClusterType(clusterType string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.ClusterType)] = clusterType
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetCreateTime(createTime time.Time) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetCreator(creator string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Creator)] = creator
	return u
}

// SetCustomOption is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetCustomOption(customOption string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.CustomOption)] = customOption
	return u
}

// SetDefaultSettings is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetDefaultSettings(defaultSettings string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.DefaultSettings)] = defaultSettings
	return u
}

// SetDescription is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetDescription(description string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Description)] = description
	return u
}

// SetDomainName is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetDomainName(domainName string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.DomainName)] = domainName
	return u
}

// SetExtranetDomainName is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetExtranetDomainName(extranetDomainName string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.ExtranetDomainName)] = extranetDomainName
	return u
}

// SetExtranetPort is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetExtranetPort(extranetPort uint) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.ExtranetPort)] = extranetPort
	return u
}

// SetGseStreamToId is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetGseStreamToId(gseStreamToId int) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.GseStreamToId)] = gseStreamToId
	return u
}

// SetIsAuth is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetIsAuth(isAuth bool) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.IsAuth)] = isAuth
	return u
}

// SetIsDefaultCluster is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetIsDefaultCluster(isDefaultCluster bool) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.IsDefaultCluster)] = isDefaultCluster
	return u
}

// SetIsRegisterToGse is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetIsRegisterToGse(isRegisterToGse bool) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.IsRegisterToGse)] = isRegisterToGse
	return u
}

// SetIsSslVerify is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetIsSslVerify(isSslVerify bool) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.IsSslVerify)] = isSslVerify
	return u
}

// SetLabel is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetLabel(label string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Label)] = label
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetLastModifyTime(lastModifyTime time.Time) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetLastModifyUser(lastModifyUser string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetPassword is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetPassword(password string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Password)] = password
	return u
}

// SetPort is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetPort(port uint) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Port)] = port
	return u
}

// SetRegisteredSystem is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetRegisteredSystem(registeredSystem string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.RegisteredSystem)] = registeredSystem
	return u
}

// SetSchema is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSchema(schema *string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Schema)] = schema
	return u
}

// SetSslCertificate is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSslCertificate(sslCertificate string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.SslCertificate)] = sslCertificate
	return u
}

// SetSslCertificateAuthorities is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSslCertificateAuthorities(sslCertificateAuthorities string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.SslCertificateAuthorities)] = sslCertificateAuthorities
	return u
}

// SetSslCertificateKey is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSslCertificateKey(sslCertificateKey string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.SslCertificateKey)] = sslCertificateKey
	return u
}

// SetSslInsecureSkipVerify is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSslInsecureSkipVerify(sslInsecureSkipVerify bool) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.SslInsecureSkipVerify)] = sslInsecureSkipVerify
	return u
}

// SetSslVerificationMode is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetSslVerificationMode(sslVerificationMode string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.SslVerificationMode)] = sslVerificationMode
	return u
}

// SetUsername is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetUsername(username string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Username)] = username
	return u
}

// SetVersion is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) SetVersion(version *string) ClusterInfoUpdater {
	u.fields[string(ClusterInfoDBSchema.Version)] = version
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ClusterInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ClusterInfoQuerySet

// ===== BEGIN of ClusterInfo modifiers

// ClusterInfoDBSchemaField describes database schema field. It requires for method 'Update'
type ClusterInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ClusterInfoDBSchemaField) String() string {
	return string(f)
}

// ClusterInfoDBSchema stores db field names of ClusterInfo
var ClusterInfoDBSchema = struct {
	ClusterID                 ClusterInfoDBSchemaField
	ClusterName               ClusterInfoDBSchemaField
	ClusterType               ClusterInfoDBSchemaField
	DomainName                ClusterInfoDBSchemaField
	Port                      ClusterInfoDBSchemaField
	Description               ClusterInfoDBSchemaField
	IsDefaultCluster          ClusterInfoDBSchemaField
	Password                  ClusterInfoDBSchemaField
	Username                  ClusterInfoDBSchemaField
	IsSslVerify               ClusterInfoDBSchemaField
	Schema                    ClusterInfoDBSchemaField
	Version                   ClusterInfoDBSchemaField
	RegisteredSystem          ClusterInfoDBSchemaField
	CustomOption              ClusterInfoDBSchemaField
	CreateTime                ClusterInfoDBSchemaField
	Creator                   ClusterInfoDBSchemaField
	LastModifyTime            ClusterInfoDBSchemaField
	LastModifyUser            ClusterInfoDBSchemaField
	GseStreamToId             ClusterInfoDBSchemaField
	IsRegisterToGse           ClusterInfoDBSchemaField
	DefaultSettings           ClusterInfoDBSchemaField
	Label                     ClusterInfoDBSchemaField
	SslCertificate            ClusterInfoDBSchemaField
	SslCertificateAuthorities ClusterInfoDBSchemaField
	SslCertificateKey         ClusterInfoDBSchemaField
	SslInsecureSkipVerify     ClusterInfoDBSchemaField
	SslVerificationMode       ClusterInfoDBSchemaField
	ExtranetDomainName        ClusterInfoDBSchemaField
	ExtranetPort              ClusterInfoDBSchemaField
	IsAuth                    ClusterInfoDBSchemaField
}{

	ClusterID:                 ClusterInfoDBSchemaField("cluster_id"),
	ClusterName:               ClusterInfoDBSchemaField("cluster_name"),
	ClusterType:               ClusterInfoDBSchemaField("cluster_type"),
	DomainName:                ClusterInfoDBSchemaField("domain_name"),
	Port:                      ClusterInfoDBSchemaField("port"),
	Description:               ClusterInfoDBSchemaField("description"),
	IsDefaultCluster:          ClusterInfoDBSchemaField("is_default_cluster"),
	Password:                  ClusterInfoDBSchemaField("password"),
	Username:                  ClusterInfoDBSchemaField("username"),
	IsSslVerify:               ClusterInfoDBSchemaField("is_ssl_verify"),
	Schema:                    ClusterInfoDBSchemaField("schema"),
	Version:                   ClusterInfoDBSchemaField("version"),
	RegisteredSystem:          ClusterInfoDBSchemaField("registered_system"),
	CustomOption:              ClusterInfoDBSchemaField("custom_option"),
	CreateTime:                ClusterInfoDBSchemaField("create_time"),
	Creator:                   ClusterInfoDBSchemaField("creator"),
	LastModifyTime:            ClusterInfoDBSchemaField("last_modify_time"),
	LastModifyUser:            ClusterInfoDBSchemaField("last_modify_user"),
	GseStreamToId:             ClusterInfoDBSchemaField("gse_stream_to_id"),
	IsRegisterToGse:           ClusterInfoDBSchemaField("is_register_to_gse"),
	DefaultSettings:           ClusterInfoDBSchemaField("default_settings"),
	Label:                     ClusterInfoDBSchemaField("label"),
	SslCertificate:            ClusterInfoDBSchemaField("ssl_certificate"),
	SslCertificateAuthorities: ClusterInfoDBSchemaField("ssl_certificate_authorities"),
	SslCertificateKey:         ClusterInfoDBSchemaField("ssl_certificate_key"),
	SslInsecureSkipVerify:     ClusterInfoDBSchemaField("ssl_insecure_skip_verify"),
	SslVerificationMode:       ClusterInfoDBSchemaField("ssl_verification_mode"),
	ExtranetDomainName:        ClusterInfoDBSchemaField("extranet_domain_name"),
	ExtranetPort:              ClusterInfoDBSchemaField("extranet_port"),
	IsAuth:                    ClusterInfoDBSchemaField("is_auth"),
}

// Update updates ClusterInfo fields by primary key
// nolint: dupl
func (o *ClusterInfo) Update(db *gorm.DB, fields ...ClusterInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"cluster_id":                  o.ClusterID,
		"cluster_name":                o.ClusterName,
		"cluster_type":                o.ClusterType,
		"domain_name":                 o.DomainName,
		"port":                        o.Port,
		"description":                 o.Description,
		"is_default_cluster":          o.IsDefaultCluster,
		"password":                    o.Password,
		"username":                    o.Username,
		"is_ssl_verify":               o.IsSslVerify,
		"schema":                      o.Schema,
		"version":                     o.Version,
		"registered_system":           o.RegisteredSystem,
		"custom_option":               o.CustomOption,
		"create_time":                 o.CreateTime,
		"creator":                     o.Creator,
		"last_modify_time":            o.LastModifyTime,
		"last_modify_user":            o.LastModifyUser,
		"gse_stream_to_id":            o.GseStreamToId,
		"is_register_to_gse":          o.IsRegisterToGse,
		"default_settings":            o.DefaultSettings,
		"label":                       o.Label,
		"ssl_certificate":             o.SslCertificate,
		"ssl_certificate_authorities": o.SslCertificateAuthorities,
		"ssl_certificate_key":         o.SslCertificateKey,
		"ssl_insecure_skip_verify":    o.SslInsecureSkipVerify,
		"ssl_verification_mode":       o.SslVerificationMode,
		"extranet_domain_name":        o.ExtranetDomainName,
		"extranet_port":               o.ExtranetPort,
		"is_auth":                     o.IsAuth,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ClusterInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ClusterInfoUpdater is an ClusterInfo updates manager
type ClusterInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewClusterInfoUpdater creates new ClusterInfo updater
// nolint: dupl
func NewClusterInfoUpdater(db *gorm.DB) ClusterInfoUpdater {
	return ClusterInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ClusterInfo{}),
	}
}

// ===== END of ClusterInfo modifiers

// ===== END of all query sets
