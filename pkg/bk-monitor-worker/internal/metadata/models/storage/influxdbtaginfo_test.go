// <PERSON><PERSON> is pleased to support the open source community by making
// 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
// Copyright (C) 2022 THL A29 Limited, a Tencent company. All rights reserved.
// Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
// You may obtain a copy of the License at http://opensource.org/licenses/MIT
// Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
// an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

package storage

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInfluxdbTagInfo_GenerateNewInfo(t *testing.T) {
	i := InfluxdbTagInfo{
		HostList: `host1,host2,hostNew`,
	}
	tagItemInfo := TagItemInfo{
		HostList: []string{"host1", "host2", "hostDelete"},
	}
	newItemInfo, err := i.GenerateNewInfo(tagItemInfo)
	assert.Nil(t, err)
	// 原列表不变
	assert.Equal(t, []string{"host1", "host2", "hostDelete"}, newItemInfo.HostList)
	// 要增加的
	assert.Equal(t, []string{"hostNew"}, newItemInfo.UnreadableHost)
	// 要删除的的
	assert.Equal(t, []string{"hostDelete"}, newItemInfo.DeleteHostList)

}
