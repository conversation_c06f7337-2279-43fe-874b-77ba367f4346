// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set EsSnapshotQuerySet

// EsSnapshotQuerySet is an queryset type for EsSnapshot
type EsSnapshotQuerySet struct {
	db *gorm.DB
}

// NewEsSnapshotQuerySet constructs new EsSnapshotQuerySet
func NewEsSnapshotQuerySet(db *gorm.DB) EsSnapshotQuerySet {
	return EsSnapshotQuerySet{
		db: db.Model(&EsSnapshot{}),
	}
}

func (qs EsSnapshotQuerySet) w(db *gorm.DB) EsSnapshotQuerySet {
	return NewEsSnapshotQuerySet(db)
}

func (qs EsSnapshotQuerySet) Select(fields ...EsSnapshotDBSchemaField) EsSnapshotQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *EsSnapshot) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *EsSnapshot) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) All(ret *[]EsSnapshot) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeEq(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeGt(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeGte(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeLt(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeLte(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreateTimeNe(createTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorEq(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorGt(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorGte(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorIn(creator ...string) EsSnapshotQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorLike(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorLt(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorLte(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorNe(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorNotIn(creator ...string) EsSnapshotQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) CreatorNotlike(creator string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) Delete() error {
	return qs.db.Delete(EsSnapshot{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(EsSnapshot{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(EsSnapshot{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) GetUpdater() EsSnapshotUpdater {
	return NewEsSnapshotUpdater(qs.db)
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeEq(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeGt(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeGte(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeLt(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeLte(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyTimeNe(lastModifyTime time.Time) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserEq(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserGt(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserGte(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserIn(lastModifyUser ...string) EsSnapshotQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserLike(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserLt(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserLte(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserNe(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserNotIn(lastModifyUser ...string) EsSnapshotQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) LastModifyUserNotlike(lastModifyUser string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) Limit(limit int) EsSnapshotQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) Offset(offset int) EsSnapshotQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs EsSnapshotQuerySet) One(ret *EsSnapshot) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByCreateTime() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByCreator() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByLastModifyTime() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByLastModifyUser() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscBySnapshotDays is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscBySnapshotDays() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("snapshot_days ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByTableID() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTargetSnapshotRepositoryName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderAscByTargetSnapshotRepositoryName() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("target_snapshot_repository_name ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByCreateTime() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByCreator() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByLastModifyTime() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByLastModifyUser() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescBySnapshotDays is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescBySnapshotDays() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("snapshot_days DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByTableID() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTargetSnapshotRepositoryName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) OrderDescByTargetSnapshotRepositoryName() EsSnapshotQuerySet {
	return qs.w(qs.db.Order("target_snapshot_repository_name DESC"))
}

// SnapshotDaysEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysEq(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days = ?", snapshotDays))
}

// SnapshotDaysGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysGt(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days > ?", snapshotDays))
}

// SnapshotDaysGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysGte(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days >= ?", snapshotDays))
}

// SnapshotDaysIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysIn(snapshotDays ...int) EsSnapshotQuerySet {
	if len(snapshotDays) == 0 {
		qs.db.AddError(errors.New("must at least pass one snapshotDays in SnapshotDaysIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snapshot_days IN (?)", snapshotDays))
}

// SnapshotDaysLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysLt(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days < ?", snapshotDays))
}

// SnapshotDaysLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysLte(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days <= ?", snapshotDays))
}

// SnapshotDaysNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysNe(snapshotDays int) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("snapshot_days != ?", snapshotDays))
}

// SnapshotDaysNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) SnapshotDaysNotIn(snapshotDays ...int) EsSnapshotQuerySet {
	if len(snapshotDays) == 0 {
		qs.db.AddError(errors.New("must at least pass one snapshotDays in SnapshotDaysNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snapshot_days NOT IN (?)", snapshotDays))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDEq(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDGt(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDGte(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDIn(tableID ...string) EsSnapshotQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDLike(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDLt(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDLte(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDNe(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDNotIn(tableID ...string) EsSnapshotQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TableIDNotlike(tableID string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TargetSnapshotRepositoryNameEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameEq(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name = ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameGt(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name > ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameGte(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name >= ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameIn(targetSnapshotRepositoryName ...string) EsSnapshotQuerySet {
	if len(targetSnapshotRepositoryName) == 0 {
		qs.db.AddError(errors.New("must at least pass one targetSnapshotRepositoryName in TargetSnapshotRepositoryNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("target_snapshot_repository_name IN (?)", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameLike(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name LIKE ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameLt(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name < ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameLte(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name <= ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameNe(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name != ?", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameNotIn(targetSnapshotRepositoryName ...string) EsSnapshotQuerySet {
	if len(targetSnapshotRepositoryName) == 0 {
		qs.db.AddError(errors.New("must at least pass one targetSnapshotRepositoryName in TargetSnapshotRepositoryNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("target_snapshot_repository_name NOT IN (?)", targetSnapshotRepositoryName))
}

// TargetSnapshotRepositoryNameNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotQuerySet) TargetSnapshotRepositoryNameNotlike(targetSnapshotRepositoryName string) EsSnapshotQuerySet {
	return qs.w(qs.db.Where("target_snapshot_repository_name NOT LIKE ?", targetSnapshotRepositoryName))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetCreateTime(createTime time.Time) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetCreator(creator string) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.Creator)] = creator
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetLastModifyTime(lastModifyTime time.Time) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetLastModifyUser(lastModifyUser string) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetSnapshotDays is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetSnapshotDays(snapshotDays int) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.SnapshotDays)] = snapshotDays
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetTableID(tableID string) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.TableID)] = tableID
	return u
}

// SetTargetSnapshotRepositoryName is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) SetTargetSnapshotRepositoryName(targetSnapshotRepositoryName string) EsSnapshotUpdater {
	u.fields[string(EsSnapshotDBSchema.TargetSnapshotRepositoryName)] = targetSnapshotRepositoryName
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u EsSnapshotUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set EsSnapshotQuerySet

// ===== BEGIN of EsSnapshot modifiers

// EsSnapshotDBSchemaField describes database schema field. It requires for method 'Update'
type EsSnapshotDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f EsSnapshotDBSchemaField) String() string {
	return string(f)
}

// EsSnapshotDBSchema stores db field names of EsSnapshot
var EsSnapshotDBSchema = struct {
	TableID                      EsSnapshotDBSchemaField
	TargetSnapshotRepositoryName EsSnapshotDBSchemaField
	SnapshotDays                 EsSnapshotDBSchemaField
	CreateTime                   EsSnapshotDBSchemaField
	Creator                      EsSnapshotDBSchemaField
	LastModifyTime               EsSnapshotDBSchemaField
	LastModifyUser               EsSnapshotDBSchemaField
}{

	TableID:                      EsSnapshotDBSchemaField("table_id"),
	TargetSnapshotRepositoryName: EsSnapshotDBSchemaField("target_snapshot_repository_name"),
	SnapshotDays:                 EsSnapshotDBSchemaField("snapshot_days"),
	CreateTime:                   EsSnapshotDBSchemaField("create_time"),
	Creator:                      EsSnapshotDBSchemaField("creator"),
	LastModifyTime:               EsSnapshotDBSchemaField("last_modify_time"),
	LastModifyUser:               EsSnapshotDBSchemaField("last_modify_user"),
}

// Update updates EsSnapshot fields by primary key
// nolint: dupl
func (o *EsSnapshot) Update(db *gorm.DB, fields ...EsSnapshotDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":                        o.TableID,
		"target_snapshot_repository_name": o.TargetSnapshotRepositoryName,
		"snapshot_days":                   o.SnapshotDays,
		"create_time":                     o.CreateTime,
		"creator":                         o.Creator,
		"last_modify_time":                o.LastModifyTime,
		"last_modify_user":                o.LastModifyUser,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update EsSnapshot %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// EsSnapshotUpdater is an EsSnapshot updates manager
type EsSnapshotUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewEsSnapshotUpdater creates new EsSnapshot updater
// nolint: dupl
func NewEsSnapshotUpdater(db *gorm.DB) EsSnapshotUpdater {
	return EsSnapshotUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&EsSnapshot{}),
	}
}

// ===== END of EsSnapshot modifiers

// ===== END of all query sets
