// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set InfluxdbClusterInfoQuerySet

// InfluxdbClusterInfoQuerySet is an queryset type for InfluxdbClusterInfo
type InfluxdbClusterInfoQuerySet struct {
	db *gorm.DB
}

// NewInfluxdbClusterInfoQuerySet constructs new InfluxdbClusterInfoQuerySet
func NewInfluxdbClusterInfoQuerySet(db *gorm.DB) InfluxdbClusterInfoQuerySet {
	return InfluxdbClusterInfoQuerySet{
		db: db.Model(&InfluxdbClusterInfo{}),
	}
}

func (qs InfluxdbClusterInfoQuerySet) w(db *gorm.DB) InfluxdbClusterInfoQuerySet {
	return NewInfluxdbClusterInfoQuerySet(db)
}

func (qs InfluxdbClusterInfoQuerySet) Select(fields ...InfluxdbClusterInfoDBSchemaField) InfluxdbClusterInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *InfluxdbClusterInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *InfluxdbClusterInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) All(ret *[]InfluxdbClusterInfo) error {
	return qs.db.Find(ret).Error
}

// ClusterNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameEq(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name = ?", clusterName))
}

// ClusterNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameGt(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name > ?", clusterName))
}

// ClusterNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameGte(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name >= ?", clusterName))
}

// ClusterNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameIn(clusterName ...string) InfluxdbClusterInfoQuerySet {
	if len(clusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterName in ClusterNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_name IN (?)", clusterName))
}

// ClusterNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameLike(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name LIKE ?", clusterName))
}

// ClusterNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameLt(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name < ?", clusterName))
}

// ClusterNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameLte(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name <= ?", clusterName))
}

// ClusterNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameNe(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name != ?", clusterName))
}

// ClusterNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameNotIn(clusterName ...string) InfluxdbClusterInfoQuerySet {
	if len(clusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterName in ClusterNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_name NOT IN (?)", clusterName))
}

// ClusterNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) ClusterNameNotlike(clusterName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("cluster_name NOT LIKE ?", clusterName))
}

// Count is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) Delete() error {
	return qs.db.Delete(InfluxdbClusterInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(InfluxdbClusterInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(InfluxdbClusterInfo{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) GetUpdater() InfluxdbClusterInfoUpdater {
	return NewInfluxdbClusterInfoUpdater(qs.db)
}

// HostNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameEq(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name = ?", hostName))
}

// HostNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameGt(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name > ?", hostName))
}

// HostNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameGte(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name >= ?", hostName))
}

// HostNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameIn(hostName ...string) InfluxdbClusterInfoQuerySet {
	if len(hostName) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostName in HostNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_name IN (?)", hostName))
}

// HostNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameLike(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name LIKE ?", hostName))
}

// HostNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameLt(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name < ?", hostName))
}

// HostNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameLte(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name <= ?", hostName))
}

// HostNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameNe(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name != ?", hostName))
}

// HostNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameNotIn(hostName ...string) InfluxdbClusterInfoQuerySet {
	if len(hostName) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostName in HostNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_name NOT IN (?)", hostName))
}

// HostNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostNameNotlike(hostName string) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_name NOT LIKE ?", hostName))
}

// HostReadableEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostReadableEq(hostReadable bool) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_readable = ?", hostReadable))
}

// HostReadableIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostReadableIn(hostReadable ...bool) InfluxdbClusterInfoQuerySet {
	if len(hostReadable) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostReadable in HostReadableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_readable IN (?)", hostReadable))
}

// HostReadableNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostReadableNe(hostReadable bool) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Where("host_readable != ?", hostReadable))
}

// HostReadableNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) HostReadableNotIn(hostReadable ...bool) InfluxdbClusterInfoQuerySet {
	if len(hostReadable) == 0 {
		qs.db.AddError(errors.New("must at least pass one hostReadable in HostReadableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("host_readable NOT IN (?)", hostReadable))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) Limit(limit int) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) Offset(offset int) InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs InfluxdbClusterInfoQuerySet) One(ret *InfluxdbClusterInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderAscByClusterName() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_name ASC"))
}

// OrderAscByHostName is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderAscByHostName() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("host_name ASC"))
}

// OrderAscByHostReadable is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderAscByHostReadable() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("host_readable ASC"))
}

// OrderDescByClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderDescByClusterName() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("cluster_name DESC"))
}

// OrderDescByHostName is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderDescByHostName() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("host_name DESC"))
}

// OrderDescByHostReadable is an autogenerated method
// nolint: dupl
func (qs InfluxdbClusterInfoQuerySet) OrderDescByHostReadable() InfluxdbClusterInfoQuerySet {
	return qs.w(qs.db.Order("host_readable DESC"))
}

// SetClusterName is an autogenerated method
// nolint: dupl
func (u InfluxdbClusterInfoUpdater) SetClusterName(clusterName string) InfluxdbClusterInfoUpdater {
	u.fields[string(InfluxdbClusterInfoDBSchema.ClusterName)] = clusterName
	return u
}

// SetHostName is an autogenerated method
// nolint: dupl
func (u InfluxdbClusterInfoUpdater) SetHostName(hostName string) InfluxdbClusterInfoUpdater {
	u.fields[string(InfluxdbClusterInfoDBSchema.HostName)] = hostName
	return u
}

// SetHostReadable is an autogenerated method
// nolint: dupl
func (u InfluxdbClusterInfoUpdater) SetHostReadable(hostReadable bool) InfluxdbClusterInfoUpdater {
	u.fields[string(InfluxdbClusterInfoDBSchema.HostReadable)] = hostReadable
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u InfluxdbClusterInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u InfluxdbClusterInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set InfluxdbClusterInfoQuerySet

// ===== BEGIN of InfluxdbClusterInfo modifiers

// InfluxdbClusterInfoDBSchemaField describes database schema field. It requires for method 'Update'
type InfluxdbClusterInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f InfluxdbClusterInfoDBSchemaField) String() string {
	return string(f)
}

// InfluxdbClusterInfoDBSchema stores db field names of InfluxdbClusterInfo
var InfluxdbClusterInfoDBSchema = struct {
	HostName     InfluxdbClusterInfoDBSchemaField
	ClusterName  InfluxdbClusterInfoDBSchemaField
	HostReadable InfluxdbClusterInfoDBSchemaField
}{

	HostName:     InfluxdbClusterInfoDBSchemaField("host_name"),
	ClusterName:  InfluxdbClusterInfoDBSchemaField("cluster_name"),
	HostReadable: InfluxdbClusterInfoDBSchemaField("host_readable"),
}

// Update updates InfluxdbClusterInfo fields by primary key
// nolint: dupl
func (o *InfluxdbClusterInfo) Update(db *gorm.DB, fields ...InfluxdbClusterInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"host_name":     o.HostName,
		"cluster_name":  o.ClusterName,
		"host_readable": o.HostReadable,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update InfluxdbClusterInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// InfluxdbClusterInfoUpdater is an InfluxdbClusterInfo updates manager
type InfluxdbClusterInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewInfluxdbClusterInfoUpdater creates new InfluxdbClusterInfo updater
// nolint: dupl
func NewInfluxdbClusterInfoUpdater(db *gorm.DB) InfluxdbClusterInfoUpdater {
	return InfluxdbClusterInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&InfluxdbClusterInfo{}),
	}
}

// ===== END of InfluxdbClusterInfo modifiers

// ===== END of all query sets
