// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set InfluxdbProxyStorageQuerySet

// InfluxdbProxyStorageQuerySet is an queryset type for InfluxdbProxyStorage
type InfluxdbProxyStorageQuerySet struct {
	db *gorm.DB
}

// NewInfluxdbProxyStorageQuerySet constructs new InfluxdbProxyStorageQuerySet
func NewInfluxdbProxyStorageQuerySet(db *gorm.DB) InfluxdbProxyStorageQuerySet {
	return InfluxdbProxyStorageQuerySet{
		db: db.Model(&InfluxdbProxyStorage{}),
	}
}

func (qs InfluxdbProxyStorageQuerySet) w(db *gorm.DB) InfluxdbProxyStorageQuerySet {
	return NewInfluxdbProxyStorageQuerySet(db)
}

func (qs InfluxdbProxyStorageQuerySet) Select(fields ...InfluxdbProxyStorageDBSchemaField) InfluxdbProxyStorageQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *InfluxdbProxyStorage) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *InfluxdbProxyStorage) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) All(ret *[]InfluxdbProxyStorage) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeEq(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeGt(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeGte(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeLt(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeLte(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreateTimeNe(createTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorEq(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorGt(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorGte(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorIn(creator ...string) InfluxdbProxyStorageQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorLike(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorLt(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorLte(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorNe(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorNotIn(creator ...string) InfluxdbProxyStorageQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) CreatorNotlike(creator string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) Delete() error {
	return qs.db.Delete(InfluxdbProxyStorage{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(InfluxdbProxyStorage{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(InfluxdbProxyStorage{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) GetUpdater() InfluxdbProxyStorageUpdater {
	return NewInfluxdbProxyStorageUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDEq(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDGt(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDGte(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDIn(ID ...uint) InfluxdbProxyStorageQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDLt(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDLte(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDNe(ID uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IDNotIn(ID ...uint) InfluxdbProxyStorageQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// InstanceClusterNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameEq(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name = ?", instanceClusterName))
}

// InstanceClusterNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameGt(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name > ?", instanceClusterName))
}

// InstanceClusterNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameGte(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name >= ?", instanceClusterName))
}

// InstanceClusterNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameIn(instanceClusterName ...string) InfluxdbProxyStorageQuerySet {
	if len(instanceClusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one instanceClusterName in InstanceClusterNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("instance_cluster_name IN (?)", instanceClusterName))
}

// InstanceClusterNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameLike(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name LIKE ?", instanceClusterName))
}

// InstanceClusterNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameLt(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name < ?", instanceClusterName))
}

// InstanceClusterNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameLte(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name <= ?", instanceClusterName))
}

// InstanceClusterNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameNe(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name != ?", instanceClusterName))
}

// InstanceClusterNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameNotIn(instanceClusterName ...string) InfluxdbProxyStorageQuerySet {
	if len(instanceClusterName) == 0 {
		qs.db.AddError(errors.New("must at least pass one instanceClusterName in InstanceClusterNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("instance_cluster_name NOT IN (?)", instanceClusterName))
}

// InstanceClusterNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) InstanceClusterNameNotlike(instanceClusterName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("instance_cluster_name NOT LIKE ?", instanceClusterName))
}

// IsDefaultEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IsDefaultEq(isDefault bool) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("is_default = ?", isDefault))
}

// IsDefaultIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IsDefaultIn(isDefault ...bool) InfluxdbProxyStorageQuerySet {
	if len(isDefault) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDefault in IsDefaultIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_default IN (?)", isDefault))
}

// IsDefaultNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IsDefaultNe(isDefault bool) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("is_default != ?", isDefault))
}

// IsDefaultNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) IsDefaultNotIn(isDefault ...bool) InfluxdbProxyStorageQuerySet {
	if len(isDefault) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDefault in IsDefaultNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_default NOT IN (?)", isDefault))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) Limit(limit int) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) Offset(offset int) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs InfluxdbProxyStorageQuerySet) One(ret *InfluxdbProxyStorage) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByCreateTime() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByCreator() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByID() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByInstanceClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByInstanceClusterName() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("instance_cluster_name ASC"))
}

// OrderAscByIsDefault is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByIsDefault() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("is_default ASC"))
}

// OrderAscByProxyClusterId is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByProxyClusterId() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("proxy_cluster_id ASC"))
}

// OrderAscByServiceName is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByServiceName() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("service_name ASC"))
}

// OrderAscByUpdateTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByUpdateTime() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("update_time ASC"))
}

// OrderAscByUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderAscByUpdater() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("updater ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByCreateTime() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByCreator() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByID() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByInstanceClusterName is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByInstanceClusterName() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("instance_cluster_name DESC"))
}

// OrderDescByIsDefault is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByIsDefault() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("is_default DESC"))
}

// OrderDescByProxyClusterId is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByProxyClusterId() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("proxy_cluster_id DESC"))
}

// OrderDescByServiceName is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByServiceName() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("service_name DESC"))
}

// OrderDescByUpdateTime is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByUpdateTime() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("update_time DESC"))
}

// OrderDescByUpdater is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) OrderDescByUpdater() InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Order("updater DESC"))
}

// ProxyClusterIdEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdEq(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id = ?", proxyClusterId))
}

// ProxyClusterIdGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdGt(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id > ?", proxyClusterId))
}

// ProxyClusterIdGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdGte(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id >= ?", proxyClusterId))
}

// ProxyClusterIdIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdIn(proxyClusterId ...uint) InfluxdbProxyStorageQuerySet {
	if len(proxyClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one proxyClusterId in ProxyClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("proxy_cluster_id IN (?)", proxyClusterId))
}

// ProxyClusterIdLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdLt(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id < ?", proxyClusterId))
}

// ProxyClusterIdLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdLte(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id <= ?", proxyClusterId))
}

// ProxyClusterIdNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdNe(proxyClusterId uint) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("proxy_cluster_id != ?", proxyClusterId))
}

// ProxyClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ProxyClusterIdNotIn(proxyClusterId ...uint) InfluxdbProxyStorageQuerySet {
	if len(proxyClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one proxyClusterId in ProxyClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("proxy_cluster_id NOT IN (?)", proxyClusterId))
}

// ServiceNameEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameEq(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name = ?", serviceName))
}

// ServiceNameGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameGt(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name > ?", serviceName))
}

// ServiceNameGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameGte(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name >= ?", serviceName))
}

// ServiceNameIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameIn(serviceName ...string) InfluxdbProxyStorageQuerySet {
	if len(serviceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one serviceName in ServiceNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("service_name IN (?)", serviceName))
}

// ServiceNameLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameLike(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name LIKE ?", serviceName))
}

// ServiceNameLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameLt(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name < ?", serviceName))
}

// ServiceNameLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameLte(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name <= ?", serviceName))
}

// ServiceNameNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameNe(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name != ?", serviceName))
}

// ServiceNameNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameNotIn(serviceName ...string) InfluxdbProxyStorageQuerySet {
	if len(serviceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one serviceName in ServiceNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("service_name NOT IN (?)", serviceName))
}

// ServiceNameNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) ServiceNameNotlike(serviceName string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("service_name NOT LIKE ?", serviceName))
}

// UpdateTimeEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeEq(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time = ?", updateTime))
}

// UpdateTimeGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeGt(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time > ?", updateTime))
}

// UpdateTimeGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeGte(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time >= ?", updateTime))
}

// UpdateTimeLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeLt(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time < ?", updateTime))
}

// UpdateTimeLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeLte(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time <= ?", updateTime))
}

// UpdateTimeNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdateTimeNe(updateTime time.Time) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("update_time != ?", updateTime))
}

// UpdaterEq is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterEq(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater = ?", updater))
}

// UpdaterGt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterGt(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater > ?", updater))
}

// UpdaterGte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterGte(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater >= ?", updater))
}

// UpdaterIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterIn(updater ...string) InfluxdbProxyStorageQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater IN (?)", updater))
}

// UpdaterLike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterLike(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater LIKE ?", updater))
}

// UpdaterLt is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterLt(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater < ?", updater))
}

// UpdaterLte is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterLte(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater <= ?", updater))
}

// UpdaterNe is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterNe(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater != ?", updater))
}

// UpdaterNotIn is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterNotIn(updater ...string) InfluxdbProxyStorageQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater NOT IN (?)", updater))
}

// UpdaterNotlike is an autogenerated method
// nolint: dupl
func (qs InfluxdbProxyStorageQuerySet) UpdaterNotlike(updater string) InfluxdbProxyStorageQuerySet {
	return qs.w(qs.db.Where("updater NOT LIKE ?", updater))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetCreateTime(createTime time.Time) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetCreator(creator string) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.Creator)] = creator
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetID(ID uint) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.ID)] = ID
	return u
}

// SetInstanceClusterName is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetInstanceClusterName(instanceClusterName string) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.InstanceClusterName)] = instanceClusterName
	return u
}

// SetIsDefault is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetIsDefault(isDefault bool) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.IsDefault)] = isDefault
	return u
}

// SetProxyClusterId is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetProxyClusterId(proxyClusterId uint) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.ProxyClusterId)] = proxyClusterId
	return u
}

// SetServiceName is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetServiceName(serviceName string) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.ServiceName)] = serviceName
	return u
}

// SetUpdateTime is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetUpdateTime(updateTime time.Time) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.UpdateTime)] = updateTime
	return u
}

// SetUpdater is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) SetUpdater(updater string) InfluxdbProxyStorageUpdater {
	u.fields[string(InfluxdbProxyStorageDBSchema.Updater)] = updater
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u InfluxdbProxyStorageUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set InfluxdbProxyStorageQuerySet

// ===== BEGIN of InfluxdbProxyStorage modifiers

// InfluxdbProxyStorageDBSchemaField describes database schema field. It requires for method 'Update'
type InfluxdbProxyStorageDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f InfluxdbProxyStorageDBSchemaField) String() string {
	return string(f)
}

// InfluxdbProxyStorageDBSchema stores db field names of InfluxdbProxyStorage
var InfluxdbProxyStorageDBSchema = struct {
	ID                  InfluxdbProxyStorageDBSchemaField
	ProxyClusterId      InfluxdbProxyStorageDBSchemaField
	InstanceClusterName InfluxdbProxyStorageDBSchemaField
	ServiceName         InfluxdbProxyStorageDBSchemaField
	IsDefault           InfluxdbProxyStorageDBSchemaField
	Creator             InfluxdbProxyStorageDBSchemaField
	CreateTime          InfluxdbProxyStorageDBSchemaField
	Updater             InfluxdbProxyStorageDBSchemaField
	UpdateTime          InfluxdbProxyStorageDBSchemaField
}{

	ID:                  InfluxdbProxyStorageDBSchemaField("id"),
	ProxyClusterId:      InfluxdbProxyStorageDBSchemaField("proxy_cluster_id"),
	InstanceClusterName: InfluxdbProxyStorageDBSchemaField("instance_cluster_name"),
	ServiceName:         InfluxdbProxyStorageDBSchemaField("service_name"),
	IsDefault:           InfluxdbProxyStorageDBSchemaField("is_default"),
	Creator:             InfluxdbProxyStorageDBSchemaField("creator"),
	CreateTime:          InfluxdbProxyStorageDBSchemaField("create_time"),
	Updater:             InfluxdbProxyStorageDBSchemaField("updater"),
	UpdateTime:          InfluxdbProxyStorageDBSchemaField("update_time"),
}

// Update updates InfluxdbProxyStorage fields by primary key
// nolint: dupl
func (o *InfluxdbProxyStorage) Update(db *gorm.DB, fields ...InfluxdbProxyStorageDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                    o.ID,
		"proxy_cluster_id":      o.ProxyClusterId,
		"instance_cluster_name": o.InstanceClusterName,
		"service_name":          o.ServiceName,
		"is_default":            o.IsDefault,
		"creator":               o.Creator,
		"create_time":           o.CreateTime,
		"updater":               o.Updater,
		"update_time":           o.UpdateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update InfluxdbProxyStorage %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// InfluxdbProxyStorageUpdater is an InfluxdbProxyStorage updates manager
type InfluxdbProxyStorageUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewInfluxdbProxyStorageUpdater creates new InfluxdbProxyStorage updater
// nolint: dupl
func NewInfluxdbProxyStorageUpdater(db *gorm.DB) InfluxdbProxyStorageUpdater {
	return InfluxdbProxyStorageUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&InfluxdbProxyStorage{}),
	}
}

// ===== END of InfluxdbProxyStorage modifiers

// ===== END of all query sets
