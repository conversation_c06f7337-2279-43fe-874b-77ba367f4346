// Code generated by go-queryset. DO NOT EDIT.
package storage

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set EsSnapshotIndiceQuerySet

// EsSnapshotIndiceQuerySet is an queryset type for EsSnapshotIndice
type EsSnapshotIndiceQuerySet struct {
	db *gorm.DB
}

// NewEsSnapshotIndiceQuerySet constructs new EsSnapshotIndiceQuerySet
func NewEsSnapshotIndiceQuerySet(db *gorm.DB) EsSnapshotIndiceQuerySet {
	return EsSnapshotIndiceQuerySet{
		db: db.Model(&EsSnapshotIndice{}),
	}
}

func (qs EsSnapshotIndiceQuerySet) w(db *gorm.DB) EsSnapshotIndiceQuerySet {
	return NewEsSnapshotIndiceQuerySet(db)
}

func (qs EsSnapshotIndiceQuerySet) Select(fields ...EsSnapshotIndiceDBSchemaField) EsSnapshotIndiceQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *EsSnapshotIndice) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *EsSnapshotIndice) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) All(ret *[]EsSnapshotIndice) error {
	return qs.db.Find(ret).Error
}

// ClusterIDEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDEq(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id = ?", clusterID))
}

// ClusterIDGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDGt(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id > ?", clusterID))
}

// ClusterIDGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDGte(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id >= ?", clusterID))
}

// ClusterIDIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDIn(clusterID ...uint) EsSnapshotIndiceQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id IN (?)", clusterID))
}

// ClusterIDLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDLt(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id < ?", clusterID))
}

// ClusterIDLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDLte(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id <= ?", clusterID))
}

// ClusterIDNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDNe(clusterID uint) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("cluster_id != ?", clusterID))
}

// ClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) ClusterIDNotIn(clusterID ...uint) EsSnapshotIndiceQuerySet {
	if len(clusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one clusterID in ClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("cluster_id NOT IN (?)", clusterID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) Delete() error {
	return qs.db.Delete(EsSnapshotIndice{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(EsSnapshotIndice{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(EsSnapshotIndice{})
	return db.RowsAffected, db.Error
}

// DocCountEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountEq(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count = ?", docCount))
}

// DocCountGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountGt(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count > ?", docCount))
}

// DocCountGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountGte(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count >= ?", docCount))
}

// DocCountIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountIn(docCount ...int64) EsSnapshotIndiceQuerySet {
	if len(docCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one docCount in DocCountIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("doc_count IN (?)", docCount))
}

// DocCountLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountLt(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count < ?", docCount))
}

// DocCountLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountLte(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count <= ?", docCount))
}

// DocCountNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountNe(docCount int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("doc_count != ?", docCount))
}

// DocCountNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) DocCountNotIn(docCount ...int64) EsSnapshotIndiceQuerySet {
	if len(docCount) == 0 {
		qs.db.AddError(errors.New("must at least pass one docCount in DocCountNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("doc_count NOT IN (?)", docCount))
}

// EndTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeEq(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time = ?", endTime))
}

// EndTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeGt(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time > ?", endTime))
}

// EndTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeGte(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time >= ?", endTime))
}

// EndTimeIsNotNull is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeIsNotNull() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time IS NOT NULL"))
}

// EndTimeIsNull is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeIsNull() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time IS NULL"))
}

// EndTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeLt(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time < ?", endTime))
}

// EndTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeLte(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time <= ?", endTime))
}

// EndTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) EndTimeNe(endTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("end_time != ?", endTime))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) GetUpdater() EsSnapshotIndiceUpdater {
	return NewEsSnapshotIndiceUpdater(qs.db)
}

// IndexNameEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameEq(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name = ?", indexName))
}

// IndexNameGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameGt(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name > ?", indexName))
}

// IndexNameGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameGte(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name >= ?", indexName))
}

// IndexNameIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameIn(indexName ...string) EsSnapshotIndiceQuerySet {
	if len(indexName) == 0 {
		qs.db.AddError(errors.New("must at least pass one indexName in IndexNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("index_name IN (?)", indexName))
}

// IndexNameLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameLike(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name LIKE ?", indexName))
}

// IndexNameLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameLt(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name < ?", indexName))
}

// IndexNameLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameLte(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name <= ?", indexName))
}

// IndexNameNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameNe(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name != ?", indexName))
}

// IndexNameNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameNotIn(indexName ...string) EsSnapshotIndiceQuerySet {
	if len(indexName) == 0 {
		qs.db.AddError(errors.New("must at least pass one indexName in IndexNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("index_name NOT IN (?)", indexName))
}

// IndexNameNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) IndexNameNotlike(indexName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("index_name NOT LIKE ?", indexName))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) Limit(limit int) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) Offset(offset int) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs EsSnapshotIndiceQuerySet) One(ret *EsSnapshotIndice) error {
	return qs.db.First(ret).Error
}

// OrderAscByClusterID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByClusterID() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("cluster_id ASC"))
}

// OrderAscByDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByDocCount() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("doc_count ASC"))
}

// OrderAscByEndTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByEndTime() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("end_time ASC"))
}

// OrderAscByIndexName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByIndexName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("index_name ASC"))
}

// OrderAscByRepositoryName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByRepositoryName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("repository_name ASC"))
}

// OrderAscBySnapshotName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscBySnapshotName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("snapshot_name ASC"))
}

// OrderAscByStartTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByStartTime() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("start_time ASC"))
}

// OrderAscByStoreSize is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByStoreSize() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("store_size ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderAscByTableID() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByClusterID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByClusterID() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("cluster_id DESC"))
}

// OrderDescByDocCount is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByDocCount() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("doc_count DESC"))
}

// OrderDescByEndTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByEndTime() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("end_time DESC"))
}

// OrderDescByIndexName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByIndexName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("index_name DESC"))
}

// OrderDescByRepositoryName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByRepositoryName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("repository_name DESC"))
}

// OrderDescBySnapshotName is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescBySnapshotName() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("snapshot_name DESC"))
}

// OrderDescByStartTime is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByStartTime() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("start_time DESC"))
}

// OrderDescByStoreSize is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByStoreSize() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("store_size DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) OrderDescByTableID() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// RepositoryNameEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameEq(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name = ?", repositoryName))
}

// RepositoryNameGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameGt(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name > ?", repositoryName))
}

// RepositoryNameGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameGte(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name >= ?", repositoryName))
}

// RepositoryNameIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameIn(repositoryName ...string) EsSnapshotIndiceQuerySet {
	if len(repositoryName) == 0 {
		qs.db.AddError(errors.New("must at least pass one repositoryName in RepositoryNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("repository_name IN (?)", repositoryName))
}

// RepositoryNameLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameLike(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name LIKE ?", repositoryName))
}

// RepositoryNameLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameLt(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name < ?", repositoryName))
}

// RepositoryNameLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameLte(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name <= ?", repositoryName))
}

// RepositoryNameNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameNe(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name != ?", repositoryName))
}

// RepositoryNameNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameNotIn(repositoryName ...string) EsSnapshotIndiceQuerySet {
	if len(repositoryName) == 0 {
		qs.db.AddError(errors.New("must at least pass one repositoryName in RepositoryNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("repository_name NOT IN (?)", repositoryName))
}

// RepositoryNameNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) RepositoryNameNotlike(repositoryName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("repository_name NOT LIKE ?", repositoryName))
}

// SnapshotNameEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameEq(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name = ?", snapshotName))
}

// SnapshotNameGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameGt(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name > ?", snapshotName))
}

// SnapshotNameGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameGte(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name >= ?", snapshotName))
}

// SnapshotNameIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameIn(snapshotName ...string) EsSnapshotIndiceQuerySet {
	if len(snapshotName) == 0 {
		qs.db.AddError(errors.New("must at least pass one snapshotName in SnapshotNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snapshot_name IN (?)", snapshotName))
}

// SnapshotNameLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameLike(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name LIKE ?", snapshotName))
}

// SnapshotNameLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameLt(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name < ?", snapshotName))
}

// SnapshotNameLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameLte(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name <= ?", snapshotName))
}

// SnapshotNameNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameNe(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name != ?", snapshotName))
}

// SnapshotNameNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameNotIn(snapshotName ...string) EsSnapshotIndiceQuerySet {
	if len(snapshotName) == 0 {
		qs.db.AddError(errors.New("must at least pass one snapshotName in SnapshotNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snapshot_name NOT IN (?)", snapshotName))
}

// SnapshotNameNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) SnapshotNameNotlike(snapshotName string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("snapshot_name NOT LIKE ?", snapshotName))
}

// StartTimeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeEq(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time = ?", startTime))
}

// StartTimeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeGt(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time > ?", startTime))
}

// StartTimeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeGte(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time >= ?", startTime))
}

// StartTimeIsNotNull is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeIsNotNull() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time IS NOT NULL"))
}

// StartTimeIsNull is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeIsNull() EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time IS NULL"))
}

// StartTimeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeLt(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time < ?", startTime))
}

// StartTimeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeLte(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time <= ?", startTime))
}

// StartTimeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StartTimeNe(startTime time.Time) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("start_time != ?", startTime))
}

// StoreSizeEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeEq(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size = ?", storeSize))
}

// StoreSizeGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeGt(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size > ?", storeSize))
}

// StoreSizeGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeGte(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size >= ?", storeSize))
}

// StoreSizeIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeIn(storeSize ...int64) EsSnapshotIndiceQuerySet {
	if len(storeSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one storeSize in StoreSizeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("store_size IN (?)", storeSize))
}

// StoreSizeLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeLt(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size < ?", storeSize))
}

// StoreSizeLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeLte(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size <= ?", storeSize))
}

// StoreSizeNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeNe(storeSize int64) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("store_size != ?", storeSize))
}

// StoreSizeNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) StoreSizeNotIn(storeSize ...int64) EsSnapshotIndiceQuerySet {
	if len(storeSize) == 0 {
		qs.db.AddError(errors.New("must at least pass one storeSize in StoreSizeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("store_size NOT IN (?)", storeSize))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDEq(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDGt(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDGte(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDIn(tableID ...string) EsSnapshotIndiceQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDLike(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDLt(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDLte(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDNe(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDNotIn(tableID ...string) EsSnapshotIndiceQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs EsSnapshotIndiceQuerySet) TableIDNotlike(tableID string) EsSnapshotIndiceQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// SetClusterID is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetClusterID(clusterID uint) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.ClusterID)] = clusterID
	return u
}

// SetDocCount is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetDocCount(docCount int64) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.DocCount)] = docCount
	return u
}

// SetEndTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetEndTime(endTime *time.Time) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.EndTime)] = endTime
	return u
}

// SetIndexName is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetIndexName(indexName string) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.IndexName)] = indexName
	return u
}

// SetRepositoryName is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetRepositoryName(repositoryName string) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.RepositoryName)] = repositoryName
	return u
}

// SetSnapshotName is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetSnapshotName(snapshotName string) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.SnapshotName)] = snapshotName
	return u
}

// SetStartTime is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetStartTime(startTime *time.Time) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.StartTime)] = startTime
	return u
}

// SetStoreSize is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetStoreSize(storeSize int64) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.StoreSize)] = storeSize
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) SetTableID(tableID string) EsSnapshotIndiceUpdater {
	u.fields[string(EsSnapshotIndiceDBSchema.TableID)] = tableID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u EsSnapshotIndiceUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set EsSnapshotIndiceQuerySet

// ===== BEGIN of EsSnapshotIndice modifiers

// EsSnapshotIndiceDBSchemaField describes database schema field. It requires for method 'Update'
type EsSnapshotIndiceDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f EsSnapshotIndiceDBSchemaField) String() string {
	return string(f)
}

// EsSnapshotIndiceDBSchema stores db field names of EsSnapshotIndice
var EsSnapshotIndiceDBSchema = struct {
	TableID        EsSnapshotIndiceDBSchemaField
	SnapshotName   EsSnapshotIndiceDBSchemaField
	ClusterID      EsSnapshotIndiceDBSchemaField
	RepositoryName EsSnapshotIndiceDBSchemaField
	IndexName      EsSnapshotIndiceDBSchemaField
	DocCount       EsSnapshotIndiceDBSchemaField
	StoreSize      EsSnapshotIndiceDBSchemaField
	StartTime      EsSnapshotIndiceDBSchemaField
	EndTime        EsSnapshotIndiceDBSchemaField
}{

	TableID:        EsSnapshotIndiceDBSchemaField("table_id"),
	SnapshotName:   EsSnapshotIndiceDBSchemaField("snapshot_name"),
	ClusterID:      EsSnapshotIndiceDBSchemaField("cluster_id"),
	RepositoryName: EsSnapshotIndiceDBSchemaField("repository_name"),
	IndexName:      EsSnapshotIndiceDBSchemaField("index_name"),
	DocCount:       EsSnapshotIndiceDBSchemaField("doc_count"),
	StoreSize:      EsSnapshotIndiceDBSchemaField("store_size"),
	StartTime:      EsSnapshotIndiceDBSchemaField("start_time"),
	EndTime:        EsSnapshotIndiceDBSchemaField("end_time"),
}

// Update updates EsSnapshotIndice fields by primary key
// nolint: dupl
func (o *EsSnapshotIndice) Update(db *gorm.DB, fields ...EsSnapshotIndiceDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"table_id":        o.TableID,
		"snapshot_name":   o.SnapshotName,
		"cluster_id":      o.ClusterID,
		"repository_name": o.RepositoryName,
		"index_name":      o.IndexName,
		"doc_count":       o.DocCount,
		"store_size":      o.StoreSize,
		"start_time":      o.StartTime,
		"end_time":        o.EndTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update EsSnapshotIndice %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// EsSnapshotIndiceUpdater is an EsSnapshotIndice updates manager
type EsSnapshotIndiceUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewEsSnapshotIndiceUpdater creates new EsSnapshotIndice updater
// nolint: dupl
func NewEsSnapshotIndiceUpdater(db *gorm.DB) EsSnapshotIndiceUpdater {
	return EsSnapshotIndiceUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&EsSnapshotIndice{}),
	}
}

// ===== END of EsSnapshotIndice modifiers

// ===== END of all query sets
