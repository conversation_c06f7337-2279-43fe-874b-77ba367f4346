// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set ResultTableOptionQuerySet

// ResultTableOptionQuerySet is an queryset type for ResultTableOption
type ResultTableOptionQuerySet struct {
	db *gorm.DB
}

// NewResultTableOptionQuerySet constructs new ResultTableOptionQuerySet
func NewResultTableOptionQuerySet(db *gorm.DB) ResultTableOptionQuerySet {
	return ResultTableOptionQuerySet{
		db: db.Model(&ResultTableOption{}),
	}
}

func (qs ResultTableOptionQuerySet) w(db *gorm.DB) ResultTableOptionQuerySet {
	return NewResultTableOptionQuerySet(db)
}

func (qs ResultTableOptionQuerySet) Select(fields ...ResultTableOptionDBSchemaField) ResultTableOptionQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *ResultTableOption) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *ResultTableOption) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) All(ret *[]ResultTableOption) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeEq(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeGt(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeGte(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeLt(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeLte(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreateTimeNe(createTime time.Time) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorEq(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorGt(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorGte(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorIn(creator ...string) ResultTableOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorLike(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorLt(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorLte(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorNe(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorNotIn(creator ...string) ResultTableOptionQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) CreatorNotlike(creator string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) Delete() error {
	return qs.db.Delete(ResultTableOption{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(ResultTableOption{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(ResultTableOption{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) GetUpdater() ResultTableOptionUpdater {
	return NewResultTableOptionUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) Limit(limit int) ResultTableOptionQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameEq(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameGt(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameGte(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameIn(name ...string) ResultTableOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameLike(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameLt(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameLte(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameNe(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameNotIn(name ...string) ResultTableOptionQuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) NameNotlike(name string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) Offset(offset int) ResultTableOptionQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs ResultTableOptionQuerySet) One(ret *ResultTableOption) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByCreateTime() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByCreator() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByName() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByTableID() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByValue is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByValue() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("value ASC"))
}

// OrderAscByValueType is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderAscByValueType() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("value_type ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByCreateTime() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByCreator() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByName() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByTableID() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByValue is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByValue() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("value DESC"))
}

// OrderDescByValueType is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) OrderDescByValueType() ResultTableOptionQuerySet {
	return qs.w(qs.db.Order("value_type DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDEq(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDGt(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDGte(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDIn(tableID ...string) ResultTableOptionQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDLike(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDLt(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDLte(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDNe(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDNotIn(tableID ...string) ResultTableOptionQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) TableIDNotlike(tableID string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// ValueEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueEq(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value = ?", value))
}

// ValueGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueGt(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value > ?", value))
}

// ValueGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueGte(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value >= ?", value))
}

// ValueIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueIn(value ...string) ResultTableOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value IN (?)", value))
}

// ValueLike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueLike(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value LIKE ?", value))
}

// ValueLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueLt(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value < ?", value))
}

// ValueLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueLte(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value <= ?", value))
}

// ValueNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueNe(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value != ?", value))
}

// ValueNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueNotIn(value ...string) ResultTableOptionQuerySet {
	if len(value) == 0 {
		qs.db.AddError(errors.New("must at least pass one value in ValueNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value NOT IN (?)", value))
}

// ValueNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueNotlike(value string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value NOT LIKE ?", value))
}

// ValueTypeEq is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeEq(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type = ?", valueType))
}

// ValueTypeGt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeGt(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type > ?", valueType))
}

// ValueTypeGte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeGte(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type >= ?", valueType))
}

// ValueTypeIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeIn(valueType ...string) ResultTableOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type IN (?)", valueType))
}

// ValueTypeLike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeLike(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type LIKE ?", valueType))
}

// ValueTypeLt is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeLt(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type < ?", valueType))
}

// ValueTypeLte is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeLte(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type <= ?", valueType))
}

// ValueTypeNe is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeNe(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type != ?", valueType))
}

// ValueTypeNotIn is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeNotIn(valueType ...string) ResultTableOptionQuerySet {
	if len(valueType) == 0 {
		qs.db.AddError(errors.New("must at least pass one valueType in ValueTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("value_type NOT IN (?)", valueType))
}

// ValueTypeNotlike is an autogenerated method
// nolint: dupl
func (qs ResultTableOptionQuerySet) ValueTypeNotlike(valueType string) ResultTableOptionQuerySet {
	return qs.w(qs.db.Where("value_type NOT LIKE ?", valueType))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetCreateTime(createTime time.Time) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetCreator(creator string) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.Creator)] = creator
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetName(name string) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.Name)] = name
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetTableID(tableID string) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.TableID)] = tableID
	return u
}

// SetValue is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetValue(value string) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.Value)] = value
	return u
}

// SetValueType is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) SetValueType(valueType string) ResultTableOptionUpdater {
	u.fields[string(ResultTableOptionDBSchema.ValueType)] = valueType
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u ResultTableOptionUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set ResultTableOptionQuerySet

// ===== BEGIN of ResultTableOption modifiers

// ResultTableOptionDBSchemaField describes database schema field. It requires for method 'Update'
type ResultTableOptionDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f ResultTableOptionDBSchemaField) String() string {
	return string(f)
}

// ResultTableOptionDBSchema stores db field names of ResultTableOption
var ResultTableOptionDBSchema = struct {
	ValueType  ResultTableOptionDBSchemaField
	Value      ResultTableOptionDBSchemaField
	Creator    ResultTableOptionDBSchemaField
	CreateTime ResultTableOptionDBSchemaField
	TableID    ResultTableOptionDBSchemaField
	Name       ResultTableOptionDBSchemaField
}{

	ValueType:  ResultTableOptionDBSchemaField("value_type"),
	Value:      ResultTableOptionDBSchemaField("value"),
	Creator:    ResultTableOptionDBSchemaField("creator"),
	CreateTime: ResultTableOptionDBSchemaField("create_time"),
	TableID:    ResultTableOptionDBSchemaField("table_id"),
	Name:       ResultTableOptionDBSchemaField("name"),
}

// Update updates ResultTableOption fields by primary key
// nolint: dupl
func (o *ResultTableOption) Update(db *gorm.DB, fields ...ResultTableOptionDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"value_type":  o.ValueType,
		"value":       o.Value,
		"creator":     o.Creator,
		"create_time": o.CreateTime,
		"table_id":    o.TableID,
		"name":        o.Name,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update ResultTableOption %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// ResultTableOptionUpdater is an ResultTableOption updates manager
type ResultTableOptionUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewResultTableOptionUpdater creates new ResultTableOption updater
// nolint: dupl
func NewResultTableOptionUpdater(db *gorm.DB) ResultTableOptionUpdater {
	return ResultTableOptionUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&ResultTableOption{}),
	}
}

// ===== END of ResultTableOption modifiers

// ===== END of all query sets
