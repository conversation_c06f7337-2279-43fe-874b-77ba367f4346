// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set DataSourceQuerySet

// DataSourceQuerySet is an queryset type for DataSource
type DataSourceQuerySet struct {
	db *gorm.DB
}

// NewDataSourceQuerySet constructs new DataSourceQuerySet
func NewDataSourceQuerySet(db *gorm.DB) DataSourceQuerySet {
	return DataSourceQuerySet{
		db: db.Model(&DataSource{}),
	}
}

func (qs DataSourceQuerySet) w(db *gorm.DB) DataSourceQuerySet {
	return NewDataSourceQuerySet(db)
}

func (qs DataSourceQuerySet) Select(fields ...DataSourceDBSchemaField) DataSourceQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *DataSource) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *DataSource) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) All(ret *[]DataSource) error {
	return qs.db.Find(ret).Error
}

// BkDataIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdEq(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataId))
}

// BkDataIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdGt(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataId))
}

// BkDataIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdGte(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataId))
}

// BkDataIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdIn(bkDataId ...uint) DataSourceQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataId))
}

// BkDataIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdLt(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataId))
}

// BkDataIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdLte(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataId))
}

// BkDataIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdNe(bkDataId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataId))
}

// BkDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) BkDataIdNotIn(bkDataId ...uint) DataSourceQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataId))
}

// Count is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeEq(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeGt(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeGte(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeLt(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeLte(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreateTimeNe(createTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatedFromEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromEq(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from = ?", createdFrom))
}

// CreatedFromGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromGt(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from > ?", createdFrom))
}

// CreatedFromGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromGte(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from >= ?", createdFrom))
}

// CreatedFromIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromIn(createdFrom ...string) DataSourceQuerySet {
	if len(createdFrom) == 0 {
		qs.db.AddError(errors.New("must at least pass one createdFrom in CreatedFromIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("created_from IN (?)", createdFrom))
}

// CreatedFromLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromLike(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from LIKE ?", createdFrom))
}

// CreatedFromLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromLt(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from < ?", createdFrom))
}

// CreatedFromLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromLte(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from <= ?", createdFrom))
}

// CreatedFromNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromNe(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from != ?", createdFrom))
}

// CreatedFromNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromNotIn(createdFrom ...string) DataSourceQuerySet {
	if len(createdFrom) == 0 {
		qs.db.AddError(errors.New("must at least pass one createdFrom in CreatedFromNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("created_from NOT IN (?)", createdFrom))
}

// CreatedFromNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatedFromNotlike(createdFrom string) DataSourceQuerySet {
	return qs.w(qs.db.Where("created_from NOT LIKE ?", createdFrom))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorEq(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorGt(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorGte(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorIn(creator ...string) DataSourceQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorLike(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorLt(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorLte(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorNe(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorNotIn(creator ...string) DataSourceQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CreatorNotlike(creator string) DataSourceQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// CustomLabelEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelEq(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label = ?", customLabel))
}

// CustomLabelGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelGt(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label > ?", customLabel))
}

// CustomLabelGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelGte(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label >= ?", customLabel))
}

// CustomLabelIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelIn(customLabel ...string) DataSourceQuerySet {
	if len(customLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLabel in CustomLabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_label IN (?)", customLabel))
}

// CustomLabelIsNotNull is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelIsNotNull() DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label IS NOT NULL"))
}

// CustomLabelIsNull is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelIsNull() DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label IS NULL"))
}

// CustomLabelLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelLike(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label LIKE ?", customLabel))
}

// CustomLabelLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelLt(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label < ?", customLabel))
}

// CustomLabelLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelLte(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label <= ?", customLabel))
}

// CustomLabelNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelNe(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label != ?", customLabel))
}

// CustomLabelNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelNotIn(customLabel ...string) DataSourceQuerySet {
	if len(customLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one customLabel in CustomLabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("custom_label NOT IN (?)", customLabel))
}

// CustomLabelNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) CustomLabelNotlike(customLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("custom_label NOT LIKE ?", customLabel))
}

// DataDescriptionEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionEq(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description = ?", dataDescription))
}

// DataDescriptionGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionGt(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description > ?", dataDescription))
}

// DataDescriptionGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionGte(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description >= ?", dataDescription))
}

// DataDescriptionIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionIn(dataDescription ...string) DataSourceQuerySet {
	if len(dataDescription) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataDescription in DataDescriptionIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_description IN (?)", dataDescription))
}

// DataDescriptionLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionLike(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description LIKE ?", dataDescription))
}

// DataDescriptionLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionLt(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description < ?", dataDescription))
}

// DataDescriptionLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionLte(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description <= ?", dataDescription))
}

// DataDescriptionNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionNe(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description != ?", dataDescription))
}

// DataDescriptionNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionNotIn(dataDescription ...string) DataSourceQuerySet {
	if len(dataDescription) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataDescription in DataDescriptionNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_description NOT IN (?)", dataDescription))
}

// DataDescriptionNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataDescriptionNotlike(dataDescription string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_description NOT LIKE ?", dataDescription))
}

// DataNameEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameEq(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name = ?", dataName))
}

// DataNameGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameGt(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name > ?", dataName))
}

// DataNameGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameGte(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name >= ?", dataName))
}

// DataNameIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameIn(dataName ...string) DataSourceQuerySet {
	if len(dataName) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataName in DataNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_name IN (?)", dataName))
}

// DataNameLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameLike(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name LIKE ?", dataName))
}

// DataNameLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameLt(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name < ?", dataName))
}

// DataNameLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameLte(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name <= ?", dataName))
}

// DataNameNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameNe(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name != ?", dataName))
}

// DataNameNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameNotIn(dataName ...string) DataSourceQuerySet {
	if len(dataName) == 0 {
		qs.db.AddError(errors.New("must at least pass one dataName in DataNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("data_name NOT IN (?)", dataName))
}

// DataNameNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DataNameNotlike(dataName string) DataSourceQuerySet {
	return qs.w(qs.db.Where("data_name NOT LIKE ?", dataName))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) Delete() error {
	return qs.db.Delete(DataSource{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(DataSource{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(DataSource{})
	return db.RowsAffected, db.Error
}

// EtlConfigEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigEq(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config = ?", etlConfig))
}

// EtlConfigGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigGt(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config > ?", etlConfig))
}

// EtlConfigGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigGte(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config >= ?", etlConfig))
}

// EtlConfigIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigIn(etlConfig ...string) DataSourceQuerySet {
	if len(etlConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one etlConfig in EtlConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("etl_config IN (?)", etlConfig))
}

// EtlConfigLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigLike(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config LIKE ?", etlConfig))
}

// EtlConfigLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigLt(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config < ?", etlConfig))
}

// EtlConfigLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigLte(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config <= ?", etlConfig))
}

// EtlConfigNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigNe(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config != ?", etlConfig))
}

// EtlConfigNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigNotIn(etlConfig ...string) DataSourceQuerySet {
	if len(etlConfig) == 0 {
		qs.db.AddError(errors.New("must at least pass one etlConfig in EtlConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("etl_config NOT IN (?)", etlConfig))
}

// EtlConfigNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) EtlConfigNotlike(etlConfig string) DataSourceQuerySet {
	return qs.w(qs.db.Where("etl_config NOT LIKE ?", etlConfig))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) GetUpdater() DataSourceUpdater {
	return NewDataSourceUpdater(qs.db)
}

// IsCustomSourceEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsCustomSourceEq(isCustomSource bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_custom_source = ?", isCustomSource))
}

// IsCustomSourceIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsCustomSourceIn(isCustomSource ...bool) DataSourceQuerySet {
	if len(isCustomSource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomSource in IsCustomSourceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_source IN (?)", isCustomSource))
}

// IsCustomSourceNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsCustomSourceNe(isCustomSource bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_custom_source != ?", isCustomSource))
}

// IsCustomSourceNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsCustomSourceNotIn(isCustomSource ...bool) DataSourceQuerySet {
	if len(isCustomSource) == 0 {
		qs.db.AddError(errors.New("must at least pass one isCustomSource in IsCustomSourceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_custom_source NOT IN (?)", isCustomSource))
}

// IsEnableEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsEnableEq(isEnable bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_enable = ?", isEnable))
}

// IsEnableIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsEnableIn(isEnable ...bool) DataSourceQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable IN (?)", isEnable))
}

// IsEnableNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsEnableNe(isEnable bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_enable != ?", isEnable))
}

// IsEnableNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsEnableNotIn(isEnable ...bool) DataSourceQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable NOT IN (?)", isEnable))
}

// IsPlatformDataIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsPlatformDataIdEq(isPlatformDataId bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_platform_data_id = ?", isPlatformDataId))
}

// IsPlatformDataIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsPlatformDataIdIn(isPlatformDataId ...bool) DataSourceQuerySet {
	if len(isPlatformDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isPlatformDataId in IsPlatformDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_platform_data_id IN (?)", isPlatformDataId))
}

// IsPlatformDataIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsPlatformDataIdNe(isPlatformDataId bool) DataSourceQuerySet {
	return qs.w(qs.db.Where("is_platform_data_id != ?", isPlatformDataId))
}

// IsPlatformDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) IsPlatformDataIdNotIn(isPlatformDataId ...bool) DataSourceQuerySet {
	if len(isPlatformDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one isPlatformDataId in IsPlatformDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_platform_data_id NOT IN (?)", isPlatformDataId))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeEq(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeGt(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeGte(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeLt(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeLte(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyTimeNe(lastModifyTime time.Time) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserEq(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserGt(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserGte(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserIn(lastModifyUser ...string) DataSourceQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserLike(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserLt(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserLte(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserNe(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserNotIn(lastModifyUser ...string) DataSourceQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) LastModifyUserNotlike(lastModifyUser string) DataSourceQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) Limit(limit int) DataSourceQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// MqClusterIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdEq(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id = ?", mqClusterId))
}

// MqClusterIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdGt(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id > ?", mqClusterId))
}

// MqClusterIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdGte(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id >= ?", mqClusterId))
}

// MqClusterIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdIn(mqClusterId ...uint) DataSourceQuerySet {
	if len(mqClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one mqClusterId in MqClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mq_cluster_id IN (?)", mqClusterId))
}

// MqClusterIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdLt(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id < ?", mqClusterId))
}

// MqClusterIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdLte(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id <= ?", mqClusterId))
}

// MqClusterIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdNe(mqClusterId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_cluster_id != ?", mqClusterId))
}

// MqClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqClusterIdNotIn(mqClusterId ...uint) DataSourceQuerySet {
	if len(mqClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one mqClusterId in MqClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mq_cluster_id NOT IN (?)", mqClusterId))
}

// MqConfigIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdEq(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id = ?", mqConfigId))
}

// MqConfigIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdGt(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id > ?", mqConfigId))
}

// MqConfigIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdGte(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id >= ?", mqConfigId))
}

// MqConfigIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdIn(mqConfigId ...uint) DataSourceQuerySet {
	if len(mqConfigId) == 0 {
		qs.db.AddError(errors.New("must at least pass one mqConfigId in MqConfigIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mq_config_id IN (?)", mqConfigId))
}

// MqConfigIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdLt(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id < ?", mqConfigId))
}

// MqConfigIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdLte(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id <= ?", mqConfigId))
}

// MqConfigIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdNe(mqConfigId uint) DataSourceQuerySet {
	return qs.w(qs.db.Where("mq_config_id != ?", mqConfigId))
}

// MqConfigIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) MqConfigIdNotIn(mqConfigId ...uint) DataSourceQuerySet {
	if len(mqConfigId) == 0 {
		qs.db.AddError(errors.New("must at least pass one mqConfigId in MqConfigIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("mq_config_id NOT IN (?)", mqConfigId))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) Offset(offset int) DataSourceQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs DataSourceQuerySet) One(ret *DataSource) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByBkDataId() DataSourceQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByCreateTime() DataSourceQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreatedFrom is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByCreatedFrom() DataSourceQuerySet {
	return qs.w(qs.db.Order("created_from ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByCreator() DataSourceQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByCustomLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByCustomLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("custom_label ASC"))
}

// OrderAscByDataDescription is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByDataDescription() DataSourceQuerySet {
	return qs.w(qs.db.Order("data_description ASC"))
}

// OrderAscByDataName is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByDataName() DataSourceQuerySet {
	return qs.w(qs.db.Order("data_name ASC"))
}

// OrderAscByEtlConfig is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByEtlConfig() DataSourceQuerySet {
	return qs.w(qs.db.Order("etl_config ASC"))
}

// OrderAscByIsCustomSource is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByIsCustomSource() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_custom_source ASC"))
}

// OrderAscByIsEnable is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByIsEnable() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_enable ASC"))
}

// OrderAscByIsPlatformDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByIsPlatformDataId() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_platform_data_id ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByLastModifyTime() DataSourceQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByLastModifyUser() DataSourceQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByMqClusterId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByMqClusterId() DataSourceQuerySet {
	return qs.w(qs.db.Order("mq_cluster_id ASC"))
}

// OrderAscByMqConfigId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByMqConfigId() DataSourceQuerySet {
	return qs.w(qs.db.Order("mq_config_id ASC"))
}

// OrderAscBySourceLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscBySourceLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("source_label ASC"))
}

// OrderAscBySourceSystem is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscBySourceSystem() DataSourceQuerySet {
	return qs.w(qs.db.Order("source_system ASC"))
}

// OrderAscBySpaceTypeId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscBySpaceTypeId() DataSourceQuerySet {
	return qs.w(qs.db.Order("space_type_id ASC"))
}

// OrderAscBySpaceUid is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscBySpaceUid() DataSourceQuerySet {
	return qs.w(qs.db.Order("space_uid ASC"))
}

// OrderAscByToken is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByToken() DataSourceQuerySet {
	return qs.w(qs.db.Order("token ASC"))
}

// OrderAscByTransferClusterId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByTransferClusterId() DataSourceQuerySet {
	return qs.w(qs.db.Order("transfer_cluster_id ASC"))
}

// OrderAscByTypeLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderAscByTypeLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("type_label ASC"))
}

// OrderDescByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByBkDataId() DataSourceQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByCreateTime() DataSourceQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreatedFrom is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByCreatedFrom() DataSourceQuerySet {
	return qs.w(qs.db.Order("created_from DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByCreator() DataSourceQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByCustomLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByCustomLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("custom_label DESC"))
}

// OrderDescByDataDescription is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByDataDescription() DataSourceQuerySet {
	return qs.w(qs.db.Order("data_description DESC"))
}

// OrderDescByDataName is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByDataName() DataSourceQuerySet {
	return qs.w(qs.db.Order("data_name DESC"))
}

// OrderDescByEtlConfig is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByEtlConfig() DataSourceQuerySet {
	return qs.w(qs.db.Order("etl_config DESC"))
}

// OrderDescByIsCustomSource is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByIsCustomSource() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_custom_source DESC"))
}

// OrderDescByIsEnable is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByIsEnable() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_enable DESC"))
}

// OrderDescByIsPlatformDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByIsPlatformDataId() DataSourceQuerySet {
	return qs.w(qs.db.Order("is_platform_data_id DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByLastModifyTime() DataSourceQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByLastModifyUser() DataSourceQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByMqClusterId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByMqClusterId() DataSourceQuerySet {
	return qs.w(qs.db.Order("mq_cluster_id DESC"))
}

// OrderDescByMqConfigId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByMqConfigId() DataSourceQuerySet {
	return qs.w(qs.db.Order("mq_config_id DESC"))
}

// OrderDescBySourceLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescBySourceLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("source_label DESC"))
}

// OrderDescBySourceSystem is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescBySourceSystem() DataSourceQuerySet {
	return qs.w(qs.db.Order("source_system DESC"))
}

// OrderDescBySpaceTypeId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescBySpaceTypeId() DataSourceQuerySet {
	return qs.w(qs.db.Order("space_type_id DESC"))
}

// OrderDescBySpaceUid is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescBySpaceUid() DataSourceQuerySet {
	return qs.w(qs.db.Order("space_uid DESC"))
}

// OrderDescByToken is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByToken() DataSourceQuerySet {
	return qs.w(qs.db.Order("token DESC"))
}

// OrderDescByTransferClusterId is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByTransferClusterId() DataSourceQuerySet {
	return qs.w(qs.db.Order("transfer_cluster_id DESC"))
}

// OrderDescByTypeLabel is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) OrderDescByTypeLabel() DataSourceQuerySet {
	return qs.w(qs.db.Order("type_label DESC"))
}

// SourceLabelEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelEq(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label = ?", sourceLabel))
}

// SourceLabelGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelGt(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label > ?", sourceLabel))
}

// SourceLabelGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelGte(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label >= ?", sourceLabel))
}

// SourceLabelIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelIn(sourceLabel ...string) DataSourceQuerySet {
	if len(sourceLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceLabel in SourceLabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_label IN (?)", sourceLabel))
}

// SourceLabelLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelLike(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label LIKE ?", sourceLabel))
}

// SourceLabelLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelLt(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label < ?", sourceLabel))
}

// SourceLabelLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelLte(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label <= ?", sourceLabel))
}

// SourceLabelNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelNe(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label != ?", sourceLabel))
}

// SourceLabelNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelNotIn(sourceLabel ...string) DataSourceQuerySet {
	if len(sourceLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceLabel in SourceLabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_label NOT IN (?)", sourceLabel))
}

// SourceLabelNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceLabelNotlike(sourceLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_label NOT LIKE ?", sourceLabel))
}

// SourceSystemEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemEq(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system = ?", sourceSystem))
}

// SourceSystemGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemGt(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system > ?", sourceSystem))
}

// SourceSystemGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemGte(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system >= ?", sourceSystem))
}

// SourceSystemIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemIn(sourceSystem ...string) DataSourceQuerySet {
	if len(sourceSystem) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceSystem in SourceSystemIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_system IN (?)", sourceSystem))
}

// SourceSystemLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemLike(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system LIKE ?", sourceSystem))
}

// SourceSystemLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemLt(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system < ?", sourceSystem))
}

// SourceSystemLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemLte(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system <= ?", sourceSystem))
}

// SourceSystemNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemNe(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system != ?", sourceSystem))
}

// SourceSystemNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemNotIn(sourceSystem ...string) DataSourceQuerySet {
	if len(sourceSystem) == 0 {
		qs.db.AddError(errors.New("must at least pass one sourceSystem in SourceSystemNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source_system NOT IN (?)", sourceSystem))
}

// SourceSystemNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SourceSystemNotlike(sourceSystem string) DataSourceQuerySet {
	return qs.w(qs.db.Where("source_system NOT LIKE ?", sourceSystem))
}

// SpaceTypeIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdEq(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id = ?", spaceTypeId))
}

// SpaceTypeIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdGt(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id > ?", spaceTypeId))
}

// SpaceTypeIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdGte(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id >= ?", spaceTypeId))
}

// SpaceTypeIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdIn(spaceTypeId ...string) DataSourceQuerySet {
	if len(spaceTypeId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceTypeId in SpaceTypeIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type_id IN (?)", spaceTypeId))
}

// SpaceTypeIdLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdLike(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id LIKE ?", spaceTypeId))
}

// SpaceTypeIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdLt(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id < ?", spaceTypeId))
}

// SpaceTypeIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdLte(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id <= ?", spaceTypeId))
}

// SpaceTypeIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdNe(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id != ?", spaceTypeId))
}

// SpaceTypeIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdNotIn(spaceTypeId ...string) DataSourceQuerySet {
	if len(spaceTypeId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceTypeId in SpaceTypeIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type_id NOT IN (?)", spaceTypeId))
}

// SpaceTypeIdNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceTypeIdNotlike(spaceTypeId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_type_id NOT LIKE ?", spaceTypeId))
}

// SpaceUidEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidEq(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid = ?", spaceUid))
}

// SpaceUidGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidGt(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid > ?", spaceUid))
}

// SpaceUidGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidGte(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid >= ?", spaceUid))
}

// SpaceUidIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidIn(spaceUid ...string) DataSourceQuerySet {
	if len(spaceUid) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceUid in SpaceUidIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_uid IN (?)", spaceUid))
}

// SpaceUidLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidLike(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid LIKE ?", spaceUid))
}

// SpaceUidLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidLt(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid < ?", spaceUid))
}

// SpaceUidLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidLte(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid <= ?", spaceUid))
}

// SpaceUidNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidNe(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid != ?", spaceUid))
}

// SpaceUidNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidNotIn(spaceUid ...string) DataSourceQuerySet {
	if len(spaceUid) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceUid in SpaceUidNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_uid NOT IN (?)", spaceUid))
}

// SpaceUidNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) SpaceUidNotlike(spaceUid string) DataSourceQuerySet {
	return qs.w(qs.db.Where("space_uid NOT LIKE ?", spaceUid))
}

// TokenEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenEq(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token = ?", token))
}

// TokenGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenGt(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token > ?", token))
}

// TokenGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenGte(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token >= ?", token))
}

// TokenIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenIn(token ...string) DataSourceQuerySet {
	if len(token) == 0 {
		qs.db.AddError(errors.New("must at least pass one token in TokenIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("token IN (?)", token))
}

// TokenLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenLike(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token LIKE ?", token))
}

// TokenLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenLt(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token < ?", token))
}

// TokenLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenLte(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token <= ?", token))
}

// TokenNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenNe(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token != ?", token))
}

// TokenNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenNotIn(token ...string) DataSourceQuerySet {
	if len(token) == 0 {
		qs.db.AddError(errors.New("must at least pass one token in TokenNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("token NOT IN (?)", token))
}

// TokenNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TokenNotlike(token string) DataSourceQuerySet {
	return qs.w(qs.db.Where("token NOT LIKE ?", token))
}

// TransferClusterIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdEq(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id = ?", transferClusterId))
}

// TransferClusterIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdGt(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id > ?", transferClusterId))
}

// TransferClusterIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdGte(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id >= ?", transferClusterId))
}

// TransferClusterIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdIn(transferClusterId ...string) DataSourceQuerySet {
	if len(transferClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one transferClusterId in TransferClusterIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("transfer_cluster_id IN (?)", transferClusterId))
}

// TransferClusterIdLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdLike(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id LIKE ?", transferClusterId))
}

// TransferClusterIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdLt(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id < ?", transferClusterId))
}

// TransferClusterIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdLte(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id <= ?", transferClusterId))
}

// TransferClusterIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdNe(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id != ?", transferClusterId))
}

// TransferClusterIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdNotIn(transferClusterId ...string) DataSourceQuerySet {
	if len(transferClusterId) == 0 {
		qs.db.AddError(errors.New("must at least pass one transferClusterId in TransferClusterIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("transfer_cluster_id NOT IN (?)", transferClusterId))
}

// TransferClusterIdNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TransferClusterIdNotlike(transferClusterId string) DataSourceQuerySet {
	return qs.w(qs.db.Where("transfer_cluster_id NOT LIKE ?", transferClusterId))
}

// TypeLabelEq is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelEq(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label = ?", typeLabel))
}

// TypeLabelGt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelGt(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label > ?", typeLabel))
}

// TypeLabelGte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelGte(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label >= ?", typeLabel))
}

// TypeLabelIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelIn(typeLabel ...string) DataSourceQuerySet {
	if len(typeLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeLabel in TypeLabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type_label IN (?)", typeLabel))
}

// TypeLabelLike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelLike(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label LIKE ?", typeLabel))
}

// TypeLabelLt is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelLt(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label < ?", typeLabel))
}

// TypeLabelLte is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelLte(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label <= ?", typeLabel))
}

// TypeLabelNe is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelNe(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label != ?", typeLabel))
}

// TypeLabelNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelNotIn(typeLabel ...string) DataSourceQuerySet {
	if len(typeLabel) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeLabel in TypeLabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type_label NOT IN (?)", typeLabel))
}

// TypeLabelNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceQuerySet) TypeLabelNotlike(typeLabel string) DataSourceQuerySet {
	return qs.w(qs.db.Where("type_label NOT LIKE ?", typeLabel))
}

// SetBkDataId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetBkDataId(bkDataId uint) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.BkDataId)] = bkDataId
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetCreateTime(createTime time.Time) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.CreateTime)] = createTime
	return u
}

// SetCreatedFrom is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetCreatedFrom(createdFrom string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.CreatedFrom)] = createdFrom
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetCreator(creator string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.Creator)] = creator
	return u
}

// SetCustomLabel is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetCustomLabel(customLabel *string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.CustomLabel)] = customLabel
	return u
}

// SetDataDescription is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetDataDescription(dataDescription string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.DataDescription)] = dataDescription
	return u
}

// SetDataName is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetDataName(dataName string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.DataName)] = dataName
	return u
}

// SetEtlConfig is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetEtlConfig(etlConfig string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.EtlConfig)] = etlConfig
	return u
}

// SetIsCustomSource is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetIsCustomSource(isCustomSource bool) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.IsCustomSource)] = isCustomSource
	return u
}

// SetIsEnable is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetIsEnable(isEnable bool) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.IsEnable)] = isEnable
	return u
}

// SetIsPlatformDataId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetIsPlatformDataId(isPlatformDataId bool) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.IsPlatformDataId)] = isPlatformDataId
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetLastModifyTime(lastModifyTime time.Time) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetLastModifyUser(lastModifyUser string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetMqClusterId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetMqClusterId(mqClusterId uint) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.MqClusterId)] = mqClusterId
	return u
}

// SetMqConfigId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetMqConfigId(mqConfigId uint) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.MqConfigId)] = mqConfigId
	return u
}

// SetSourceLabel is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetSourceLabel(sourceLabel string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.SourceLabel)] = sourceLabel
	return u
}

// SetSourceSystem is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetSourceSystem(sourceSystem string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.SourceSystem)] = sourceSystem
	return u
}

// SetSpaceTypeId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetSpaceTypeId(spaceTypeId string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.SpaceTypeId)] = spaceTypeId
	return u
}

// SetSpaceUid is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetSpaceUid(spaceUid string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.SpaceUid)] = spaceUid
	return u
}

// SetToken is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetToken(token string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.Token)] = token
	return u
}

// SetTransferClusterId is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetTransferClusterId(transferClusterId string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.TransferClusterId)] = transferClusterId
	return u
}

// SetTypeLabel is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) SetTypeLabel(typeLabel string) DataSourceUpdater {
	u.fields[string(DataSourceDBSchema.TypeLabel)] = typeLabel
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u DataSourceUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set DataSourceQuerySet

// ===== BEGIN of DataSource modifiers

// DataSourceDBSchemaField describes database schema field. It requires for method 'Update'
type DataSourceDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f DataSourceDBSchemaField) String() string {
	return string(f)
}

// DataSourceDBSchema stores db field names of DataSource
var DataSourceDBSchema = struct {
	BkDataId          DataSourceDBSchemaField
	Token             DataSourceDBSchemaField
	DataName          DataSourceDBSchemaField
	DataDescription   DataSourceDBSchemaField
	MqClusterId       DataSourceDBSchemaField
	MqConfigId        DataSourceDBSchemaField
	EtlConfig         DataSourceDBSchemaField
	IsCustomSource    DataSourceDBSchemaField
	Creator           DataSourceDBSchemaField
	CreateTime        DataSourceDBSchemaField
	LastModifyUser    DataSourceDBSchemaField
	LastModifyTime    DataSourceDBSchemaField
	TypeLabel         DataSourceDBSchemaField
	SourceLabel       DataSourceDBSchemaField
	CustomLabel       DataSourceDBSchemaField
	SourceSystem      DataSourceDBSchemaField
	IsEnable          DataSourceDBSchemaField
	TransferClusterId DataSourceDBSchemaField
	IsPlatformDataId  DataSourceDBSchemaField
	SpaceTypeId       DataSourceDBSchemaField
	SpaceUid          DataSourceDBSchemaField
	CreatedFrom       DataSourceDBSchemaField
}{

	BkDataId:          DataSourceDBSchemaField("bk_data_id"),
	Token:             DataSourceDBSchemaField("token"),
	DataName:          DataSourceDBSchemaField("data_name"),
	DataDescription:   DataSourceDBSchemaField("data_description"),
	MqClusterId:       DataSourceDBSchemaField("mq_cluster_id"),
	MqConfigId:        DataSourceDBSchemaField("mq_config_id"),
	EtlConfig:         DataSourceDBSchemaField("etl_config"),
	IsCustomSource:    DataSourceDBSchemaField("is_custom_source"),
	Creator:           DataSourceDBSchemaField("creator"),
	CreateTime:        DataSourceDBSchemaField("create_time"),
	LastModifyUser:    DataSourceDBSchemaField("last_modify_user"),
	LastModifyTime:    DataSourceDBSchemaField("last_modify_time"),
	TypeLabel:         DataSourceDBSchemaField("type_label"),
	SourceLabel:       DataSourceDBSchemaField("source_label"),
	CustomLabel:       DataSourceDBSchemaField("custom_label"),
	SourceSystem:      DataSourceDBSchemaField("source_system"),
	IsEnable:          DataSourceDBSchemaField("is_enable"),
	TransferClusterId: DataSourceDBSchemaField("transfer_cluster_id"),
	IsPlatformDataId:  DataSourceDBSchemaField("is_platform_data_id"),
	SpaceTypeId:       DataSourceDBSchemaField("space_type_id"),
	SpaceUid:          DataSourceDBSchemaField("space_uid"),
	CreatedFrom:       DataSourceDBSchemaField("created_from"),
}

// Update updates DataSource fields by primary key
// nolint: dupl
func (o *DataSource) Update(db *gorm.DB, fields ...DataSourceDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"bk_data_id":          o.BkDataId,
		"token":               o.Token,
		"data_name":           o.DataName,
		"data_description":    o.DataDescription,
		"mq_cluster_id":       o.MqClusterId,
		"mq_config_id":        o.MqConfigId,
		"etl_config":          o.EtlConfig,
		"is_custom_source":    o.IsCustomSource,
		"creator":             o.Creator,
		"create_time":         o.CreateTime,
		"last_modify_user":    o.LastModifyUser,
		"last_modify_time":    o.LastModifyTime,
		"type_label":          o.TypeLabel,
		"source_label":        o.SourceLabel,
		"custom_label":        o.CustomLabel,
		"source_system":       o.SourceSystem,
		"is_enable":           o.IsEnable,
		"transfer_cluster_id": o.TransferClusterId,
		"is_platform_data_id": o.IsPlatformDataId,
		"space_type_id":       o.SpaceTypeId,
		"space_uid":           o.SpaceUid,
		"created_from":        o.CreatedFrom,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update DataSource %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// DataSourceUpdater is an DataSource updates manager
type DataSourceUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewDataSourceUpdater creates new DataSource updater
// nolint: dupl
func NewDataSourceUpdater(db *gorm.DB) DataSourceUpdater {
	return DataSourceUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&DataSource{}),
	}
}

// ===== END of DataSource modifiers

// ===== END of all query sets
