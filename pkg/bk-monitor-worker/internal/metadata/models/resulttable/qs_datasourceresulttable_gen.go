// Code generated by go-queryset. DO NOT EDIT.
package resulttable

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set DataSourceResultTableQuerySet

// DataSourceResultTableQuerySet is an queryset type for DataSourceResultTable
type DataSourceResultTableQuerySet struct {
	db *gorm.DB
}

// NewDataSourceResultTableQuerySet constructs new DataSourceResultTableQuerySet
func NewDataSourceResultTableQuerySet(db *gorm.DB) DataSourceResultTableQuerySet {
	return DataSourceResultTableQuerySet{
		db: db.Model(&DataSourceResultTable{}),
	}
}

func (qs DataSourceResultTableQuerySet) w(db *gorm.DB) DataSourceResultTableQuerySet {
	return NewDataSourceResultTableQuerySet(db)
}

func (qs DataSourceResultTableQuerySet) Select(fields ...DataSourceResultTableDBSchemaField) DataSourceResultTableQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *DataSourceResultTable) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *DataSourceResultTable) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) All(ret *[]DataSourceResultTable) error {
	return qs.db.Find(ret).Error
}

// BkDataIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdEq(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataId))
}

// BkDataIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdGt(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataId))
}

// BkDataIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdGte(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataId))
}

// BkDataIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdIn(bkDataId ...uint) DataSourceResultTableQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataId))
}

// BkDataIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdLt(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataId))
}

// BkDataIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdLte(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataId))
}

// BkDataIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdNe(bkDataId uint) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataId))
}

// BkDataIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) BkDataIdNotIn(bkDataId ...uint) DataSourceResultTableQuerySet {
	if len(bkDataId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataId in BkDataIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataId))
}

// Count is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeEq(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeGt(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeGte(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeLt(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeLte(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreateTimeNe(createTime time.Time) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorEq(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorGt(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorGte(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorIn(creator ...string) DataSourceResultTableQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorLike(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorLt(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorLte(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorNe(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorNotIn(creator ...string) DataSourceResultTableQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) CreatorNotlike(creator string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) Delete() error {
	return qs.db.Delete(DataSourceResultTable{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(DataSourceResultTable{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(DataSourceResultTable{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) GetUpdater() DataSourceResultTableUpdater {
	return NewDataSourceResultTableUpdater(qs.db)
}

// Limit is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) Limit(limit int) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) Offset(offset int) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs DataSourceResultTableQuerySet) One(ret *DataSourceResultTable) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderAscByBkDataId() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderAscByCreateTime() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderAscByCreator() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByTableId is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderAscByTableId() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByBkDataId is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderDescByBkDataId() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderDescByCreateTime() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderDescByCreator() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByTableId is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) OrderDescByTableId() DataSourceResultTableQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// TableIdEq is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdEq(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableId))
}

// TableIdGt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdGt(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableId))
}

// TableIdGte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdGte(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableId))
}

// TableIdIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdIn(tableId ...string) DataSourceResultTableQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableId))
}

// TableIdLike is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdLike(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableId))
}

// TableIdLt is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdLt(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableId))
}

// TableIdLte is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdLte(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableId))
}

// TableIdNe is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdNe(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableId))
}

// TableIdNotIn is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdNotIn(tableId ...string) DataSourceResultTableQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableId))
}

// TableIdNotlike is an autogenerated method
// nolint: dupl
func (qs DataSourceResultTableQuerySet) TableIdNotlike(tableId string) DataSourceResultTableQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableId))
}

// SetBkDataId is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) SetBkDataId(bkDataId uint) DataSourceResultTableUpdater {
	u.fields[string(DataSourceResultTableDBSchema.BkDataId)] = bkDataId
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) SetCreateTime(createTime time.Time) DataSourceResultTableUpdater {
	u.fields[string(DataSourceResultTableDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) SetCreator(creator string) DataSourceResultTableUpdater {
	u.fields[string(DataSourceResultTableDBSchema.Creator)] = creator
	return u
}

// SetTableId is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) SetTableId(tableId string) DataSourceResultTableUpdater {
	u.fields[string(DataSourceResultTableDBSchema.TableId)] = tableId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u DataSourceResultTableUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set DataSourceResultTableQuerySet

// ===== BEGIN of DataSourceResultTable modifiers

// DataSourceResultTableDBSchemaField describes database schema field. It requires for method 'Update'
type DataSourceResultTableDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f DataSourceResultTableDBSchemaField) String() string {
	return string(f)
}

// DataSourceResultTableDBSchema stores db field names of DataSourceResultTable
var DataSourceResultTableDBSchema = struct {
	BkDataId   DataSourceResultTableDBSchemaField
	TableId    DataSourceResultTableDBSchemaField
	Creator    DataSourceResultTableDBSchemaField
	CreateTime DataSourceResultTableDBSchemaField
}{

	BkDataId:   DataSourceResultTableDBSchemaField("bk_data_id"),
	TableId:    DataSourceResultTableDBSchemaField("table_id"),
	Creator:    DataSourceResultTableDBSchemaField("creator"),
	CreateTime: DataSourceResultTableDBSchemaField("create_time"),
}

// Update updates DataSourceResultTable fields by primary key
// nolint: dupl
func (o *DataSourceResultTable) Update(db *gorm.DB, fields ...DataSourceResultTableDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"bk_data_id":  o.BkDataId,
		"table_id":    o.TableId,
		"creator":     o.Creator,
		"create_time": o.CreateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update DataSourceResultTable %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// DataSourceResultTableUpdater is an DataSourceResultTable updates manager
type DataSourceResultTableUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewDataSourceResultTableUpdater creates new DataSourceResultTable updater
// nolint: dupl
func NewDataSourceResultTableUpdater(db *gorm.DB) DataSourceResultTableUpdater {
	return DataSourceResultTableUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&DataSourceResultTable{}),
	}
}

// ===== END of DataSourceResultTable modifiers

// ===== END of all query sets
