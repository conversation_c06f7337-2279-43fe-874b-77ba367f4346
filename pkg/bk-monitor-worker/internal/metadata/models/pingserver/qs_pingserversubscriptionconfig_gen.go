// Code generated by go-queryset. DO NOT EDIT.
package pingserver

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set PingServerSubscriptionConfigQuerySet

// PingServerSubscriptionConfigQuerySet is an queryset type for PingServerSubscriptionConfig
type PingServerSubscriptionConfigQuerySet struct {
	db *gorm.DB
}

// NewPingServerSubscriptionConfigQuerySet constructs new PingServerSubscriptionConfigQuerySet
func NewPingServerSubscriptionConfigQuerySet(db *gorm.DB) PingServerSubscriptionConfigQuerySet {
	return PingServerSubscriptionConfigQuerySet{
		db: db.Model(&PingServerSubscriptionConfig{}),
	}
}

func (qs PingServerSubscriptionConfigQuerySet) w(db *gorm.DB) PingServerSubscriptionConfigQuerySet {
	return NewPingServerSubscriptionConfigQuerySet(db)
}

func (qs PingServerSubscriptionConfigQuerySet) Select(fields ...PingServerSubscriptionConfigDBSchemaField) PingServerSubscriptionConfigQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *PingServerSubscriptionConfig) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *PingServerSubscriptionConfig) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) All(ret *[]PingServerSubscriptionConfig) error {
	return qs.db.Find(ret).Error
}

// BkCloudIdEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdEq(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id = ?", bkCloudId))
}

// BkCloudIdGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdGt(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id > ?", bkCloudId))
}

// BkCloudIdGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdGte(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id >= ?", bkCloudId))
}

// BkCloudIdIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdIn(bkCloudId ...int) PingServerSubscriptionConfigQuerySet {
	if len(bkCloudId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkCloudId in BkCloudIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_cloud_id IN (?)", bkCloudId))
}

// BkCloudIdLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdLt(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id < ?", bkCloudId))
}

// BkCloudIdLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdLte(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id <= ?", bkCloudId))
}

// BkCloudIdNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdNe(bkCloudId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_cloud_id != ?", bkCloudId))
}

// BkCloudIdNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkCloudIdNotIn(bkCloudId ...int) PingServerSubscriptionConfigQuerySet {
	if len(bkCloudId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkCloudId in BkCloudIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_cloud_id NOT IN (?)", bkCloudId))
}

// BkHostIdEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdEq(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id = ?", bkHostId))
}

// BkHostIdGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdGt(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id > ?", bkHostId))
}

// BkHostIdGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdGte(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id >= ?", bkHostId))
}

// BkHostIdIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdIn(bkHostId ...int) PingServerSubscriptionConfigQuerySet {
	if len(bkHostId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkHostId in BkHostIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_host_id IN (?)", bkHostId))
}

// BkHostIdIsNotNull is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdIsNotNull() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id IS NOT NULL"))
}

// BkHostIdIsNull is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdIsNull() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id IS NULL"))
}

// BkHostIdLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdLt(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id < ?", bkHostId))
}

// BkHostIdLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdLte(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id <= ?", bkHostId))
}

// BkHostIdNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdNe(bkHostId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_host_id != ?", bkHostId))
}

// BkHostIdNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) BkHostIdNotIn(bkHostId ...int) PingServerSubscriptionConfigQuerySet {
	if len(bkHostId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkHostId in BkHostIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_host_id NOT IN (?)", bkHostId))
}

// ConfigEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigEq(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config = ?", config))
}

// ConfigGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigGt(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config > ?", config))
}

// ConfigGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigGte(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config >= ?", config))
}

// ConfigIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigIn(config ...string) PingServerSubscriptionConfigQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config IN (?)", config))
}

// ConfigLike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigLike(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config LIKE ?", config))
}

// ConfigLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigLt(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config < ?", config))
}

// ConfigLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigLte(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config <= ?", config))
}

// ConfigNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigNe(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config != ?", config))
}

// ConfigNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigNotIn(config ...string) PingServerSubscriptionConfigQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config NOT IN (?)", config))
}

// ConfigNotlike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) ConfigNotlike(config string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config NOT LIKE ?", config))
}

// Count is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) Delete() error {
	return qs.db.Delete(PingServerSubscriptionConfig{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(PingServerSubscriptionConfig{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(PingServerSubscriptionConfig{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) GetUpdater() PingServerSubscriptionConfigUpdater {
	return NewPingServerSubscriptionConfigUpdater(qs.db)
}

// IPEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPEq(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip = ?", IP))
}

// IPGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPGt(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip > ?", IP))
}

// IPGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPGte(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip >= ?", IP))
}

// IPIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPIn(IP ...string) PingServerSubscriptionConfigQuerySet {
	if len(IP) == 0 {
		qs.db.AddError(errors.New("must at least pass one IP in IPIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ip IN (?)", IP))
}

// IPLike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPLike(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip LIKE ?", IP))
}

// IPLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPLt(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip < ?", IP))
}

// IPLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPLte(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip <= ?", IP))
}

// IPNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPNe(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip != ?", IP))
}

// IPNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPNotIn(IP ...string) PingServerSubscriptionConfigQuerySet {
	if len(IP) == 0 {
		qs.db.AddError(errors.New("must at least pass one IP in IPNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("ip NOT IN (?)", IP))
}

// IPNotlike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) IPNotlike(IP string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("ip NOT LIKE ?", IP))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) Limit(limit int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) Offset(offset int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs PingServerSubscriptionConfigQuerySet) One(ret *PingServerSubscriptionConfig) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkCloudId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscByBkCloudId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_cloud_id ASC"))
}

// OrderAscByBkHostId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscByBkHostId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_host_id ASC"))
}

// OrderAscByConfig is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscByConfig() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("config ASC"))
}

// OrderAscByIP is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscByIP() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("ip ASC"))
}

// OrderAscByPluginName is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscByPluginName() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("plugin_name ASC"))
}

// OrderAscBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderAscBySubscriptionId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("subscription_id ASC"))
}

// OrderDescByBkCloudId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescByBkCloudId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_cloud_id DESC"))
}

// OrderDescByBkHostId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescByBkHostId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_host_id DESC"))
}

// OrderDescByConfig is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescByConfig() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("config DESC"))
}

// OrderDescByIP is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescByIP() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("ip DESC"))
}

// OrderDescByPluginName is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescByPluginName() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("plugin_name DESC"))
}

// OrderDescBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) OrderDescBySubscriptionId() PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("subscription_id DESC"))
}

// PluginNameEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameEq(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name = ?", pluginName))
}

// PluginNameGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameGt(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name > ?", pluginName))
}

// PluginNameGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameGte(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name >= ?", pluginName))
}

// PluginNameIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameIn(pluginName ...string) PingServerSubscriptionConfigQuerySet {
	if len(pluginName) == 0 {
		qs.db.AddError(errors.New("must at least pass one pluginName in PluginNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("plugin_name IN (?)", pluginName))
}

// PluginNameLike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameLike(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name LIKE ?", pluginName))
}

// PluginNameLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameLt(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name < ?", pluginName))
}

// PluginNameLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameLte(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name <= ?", pluginName))
}

// PluginNameNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameNe(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name != ?", pluginName))
}

// PluginNameNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameNotIn(pluginName ...string) PingServerSubscriptionConfigQuerySet {
	if len(pluginName) == 0 {
		qs.db.AddError(errors.New("must at least pass one pluginName in PluginNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("plugin_name NOT IN (?)", pluginName))
}

// PluginNameNotlike is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) PluginNameNotlike(pluginName string) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("plugin_name NOT LIKE ?", pluginName))
}

// SubscriptionIdEq is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdEq(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id = ?", subscriptionId))
}

// SubscriptionIdGt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdGt(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id > ?", subscriptionId))
}

// SubscriptionIdGte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdGte(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id >= ?", subscriptionId))
}

// SubscriptionIdIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdIn(subscriptionId ...int) PingServerSubscriptionConfigQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id IN (?)", subscriptionId))
}

// SubscriptionIdLt is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdLt(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id < ?", subscriptionId))
}

// SubscriptionIdLte is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdLte(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id <= ?", subscriptionId))
}

// SubscriptionIdNe is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdNe(subscriptionId int) PingServerSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id != ?", subscriptionId))
}

// SubscriptionIdNotIn is an autogenerated method
// nolint: dupl
func (qs PingServerSubscriptionConfigQuerySet) SubscriptionIdNotIn(subscriptionId ...int) PingServerSubscriptionConfigQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id NOT IN (?)", subscriptionId))
}

// SetBkCloudId is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetBkCloudId(bkCloudId int) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.BkCloudId)] = bkCloudId
	return u
}

// SetBkHostId is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetBkHostId(bkHostId *int) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.BkHostId)] = bkHostId
	return u
}

// SetConfig is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetConfig(config string) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.Config)] = config
	return u
}

// SetIP is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetIP(IP string) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.IP)] = IP
	return u
}

// SetPluginName is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetPluginName(pluginName string) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.PluginName)] = pluginName
	return u
}

// SetSubscriptionId is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) SetSubscriptionId(subscriptionId int) PingServerSubscriptionConfigUpdater {
	u.fields[string(PingServerSubscriptionConfigDBSchema.SubscriptionId)] = subscriptionId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u PingServerSubscriptionConfigUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set PingServerSubscriptionConfigQuerySet

// ===== BEGIN of PingServerSubscriptionConfig modifiers

// PingServerSubscriptionConfigDBSchemaField describes database schema field. It requires for method 'Update'
type PingServerSubscriptionConfigDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f PingServerSubscriptionConfigDBSchemaField) String() string {
	return string(f)
}

// PingServerSubscriptionConfigDBSchema stores db field names of PingServerSubscriptionConfig
var PingServerSubscriptionConfigDBSchema = struct {
	SubscriptionId PingServerSubscriptionConfigDBSchemaField
	BkCloudId      PingServerSubscriptionConfigDBSchemaField
	IP             PingServerSubscriptionConfigDBSchemaField
	BkHostId       PingServerSubscriptionConfigDBSchemaField
	Config         PingServerSubscriptionConfigDBSchemaField
	PluginName     PingServerSubscriptionConfigDBSchemaField
}{

	SubscriptionId: PingServerSubscriptionConfigDBSchemaField("subscription_id"),
	BkCloudId:      PingServerSubscriptionConfigDBSchemaField("bk_cloud_id"),
	IP:             PingServerSubscriptionConfigDBSchemaField("ip"),
	BkHostId:       PingServerSubscriptionConfigDBSchemaField("bk_host_id"),
	Config:         PingServerSubscriptionConfigDBSchemaField("config"),
	PluginName:     PingServerSubscriptionConfigDBSchemaField("plugin_name"),
}

// Update updates PingServerSubscriptionConfig fields by primary key
// nolint: dupl
func (o *PingServerSubscriptionConfig) Update(db *gorm.DB, fields ...PingServerSubscriptionConfigDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"subscription_id": o.SubscriptionId,
		"bk_cloud_id":     o.BkCloudId,
		"ip":              o.IP,
		"bk_host_id":      o.BkHostId,
		"config":          o.Config,
		"plugin_name":     o.PluginName,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update PingServerSubscriptionConfig %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// PingServerSubscriptionConfigUpdater is an PingServerSubscriptionConfig updates manager
type PingServerSubscriptionConfigUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewPingServerSubscriptionConfigUpdater creates new PingServerSubscriptionConfig updater
// nolint: dupl
func NewPingServerSubscriptionConfigUpdater(db *gorm.DB) PingServerSubscriptionConfigUpdater {
	return PingServerSubscriptionConfigUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&PingServerSubscriptionConfig{}),
	}
}

// ===== END of PingServerSubscriptionConfig modifiers

// ===== END of all query sets
