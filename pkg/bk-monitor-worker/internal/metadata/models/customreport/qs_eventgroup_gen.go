// Code generated by go-queryset. DO NOT EDIT.
package customreport

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set EventGroupQuerySet

// EventGroupQuerySet is an queryset type for EventGroup
type EventGroupQuerySet struct {
	db *gorm.DB
}

// NewEventGroupQuerySet constructs new EventGroupQuerySet
func NewEventGroupQuerySet(db *gorm.DB) EventGroupQuerySet {
	return EventGroupQuerySet{
		db: db.Model(&EventGroup{}),
	}
}

func (qs EventGroupQuerySet) w(db *gorm.DB) EventGroupQuerySet {
	return NewEventGroupQuerySet(db)
}

func (qs EventGroupQuerySet) Select(fields ...EventGroupDBSchemaField) EventGroupQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *EventGroup) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *EventGroup) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) All(ret *[]EventGroup) error {
	return qs.db.Find(ret).Error
}

// BkBizIDEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDEq(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizID))
}

// BkBizIDGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDGt(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizID))
}

// BkBizIDGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDGte(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizID))
}

// BkBizIDIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDIn(bkBizID ...int) EventGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizID))
}

// BkBizIDLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDLt(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizID))
}

// BkBizIDLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDLte(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizID))
}

// BkBizIDNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDNe(bkBizID int) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizID))
}

// BkBizIDNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkBizIDNotIn(bkBizID ...int) EventGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizID))
}

// BkDataIDEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDEq(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataID))
}

// BkDataIDGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDGt(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataID))
}

// BkDataIDGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDGte(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataID))
}

// BkDataIDIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDIn(bkDataID ...uint) EventGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataID))
}

// BkDataIDLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDLt(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataID))
}

// BkDataIDLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDLte(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataID))
}

// BkDataIDNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDNe(bkDataID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataID))
}

// BkDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) BkDataIDNotIn(bkDataID ...uint) EventGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeEq(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeGt(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeGte(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeLt(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeLte(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreateTimeNe(createTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorEq(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorGt(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorGte(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorIn(creator ...string) EventGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorLike(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorLt(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorLte(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorNe(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorNotIn(creator ...string) EventGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) CreatorNotlike(creator string) EventGroupQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) Delete() error {
	return qs.db.Delete(EventGroup{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(EventGroup{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(EventGroup{})
	return db.RowsAffected, db.Error
}

// EventGroupIDEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDEq(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id = ?", eventGroupID))
}

// EventGroupIDGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDGt(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id > ?", eventGroupID))
}

// EventGroupIDGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDGte(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id >= ?", eventGroupID))
}

// EventGroupIDIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDIn(eventGroupID ...uint) EventGroupQuerySet {
	if len(eventGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one eventGroupID in EventGroupIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("event_group_id IN (?)", eventGroupID))
}

// EventGroupIDLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDLt(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id < ?", eventGroupID))
}

// EventGroupIDLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDLte(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id <= ?", eventGroupID))
}

// EventGroupIDNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDNe(eventGroupID uint) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_id != ?", eventGroupID))
}

// EventGroupIDNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupIDNotIn(eventGroupID ...uint) EventGroupQuerySet {
	if len(eventGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one eventGroupID in EventGroupIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("event_group_id NOT IN (?)", eventGroupID))
}

// EventGroupNameEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameEq(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name = ?", eventGroupName))
}

// EventGroupNameGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameGt(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name > ?", eventGroupName))
}

// EventGroupNameGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameGte(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name >= ?", eventGroupName))
}

// EventGroupNameIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameIn(eventGroupName ...string) EventGroupQuerySet {
	if len(eventGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one eventGroupName in EventGroupNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("event_group_name IN (?)", eventGroupName))
}

// EventGroupNameLike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameLike(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name LIKE ?", eventGroupName))
}

// EventGroupNameLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameLt(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name < ?", eventGroupName))
}

// EventGroupNameLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameLte(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name <= ?", eventGroupName))
}

// EventGroupNameNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameNe(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name != ?", eventGroupName))
}

// EventGroupNameNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameNotIn(eventGroupName ...string) EventGroupQuerySet {
	if len(eventGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one eventGroupName in EventGroupNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("event_group_name NOT IN (?)", eventGroupName))
}

// EventGroupNameNotlike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) EventGroupNameNotlike(eventGroupName string) EventGroupQuerySet {
	return qs.w(qs.db.Where("event_group_name NOT LIKE ?", eventGroupName))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) GetUpdater() EventGroupUpdater {
	return NewEventGroupUpdater(qs.db)
}

// IsDeleteEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsDeleteEq(isDelete bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_delete = ?", isDelete))
}

// IsDeleteIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsDeleteIn(isDelete ...bool) EventGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete IN (?)", isDelete))
}

// IsDeleteNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsDeleteNe(isDelete bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_delete != ?", isDelete))
}

// IsDeleteNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsDeleteNotIn(isDelete ...bool) EventGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete NOT IN (?)", isDelete))
}

// IsEnableEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsEnableEq(isEnable bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_enable = ?", isEnable))
}

// IsEnableIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsEnableIn(isEnable ...bool) EventGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable IN (?)", isEnable))
}

// IsEnableNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsEnableNe(isEnable bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_enable != ?", isEnable))
}

// IsEnableNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsEnableNotIn(isEnable ...bool) EventGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable NOT IN (?)", isEnable))
}

// IsSplitMeasurementEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsSplitMeasurementEq(isSplitMeasurement bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement = ?", isSplitMeasurement))
}

// IsSplitMeasurementIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsSplitMeasurementIn(isSplitMeasurement ...bool) EventGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement IN (?)", isSplitMeasurement))
}

// IsSplitMeasurementNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsSplitMeasurementNe(isSplitMeasurement bool) EventGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement != ?", isSplitMeasurement))
}

// IsSplitMeasurementNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) IsSplitMeasurementNotIn(isSplitMeasurement ...bool) EventGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement NOT IN (?)", isSplitMeasurement))
}

// LabelEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelEq(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label = ?", label))
}

// LabelGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelGt(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label > ?", label))
}

// LabelGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelGte(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label >= ?", label))
}

// LabelIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelIn(label ...string) EventGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label IN (?)", label))
}

// LabelLike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelLike(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label LIKE ?", label))
}

// LabelLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelLt(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label < ?", label))
}

// LabelLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelLte(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label <= ?", label))
}

// LabelNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelNe(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label != ?", label))
}

// LabelNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelNotIn(label ...string) EventGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label NOT IN (?)", label))
}

// LabelNotlike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LabelNotlike(label string) EventGroupQuerySet {
	return qs.w(qs.db.Where("label NOT LIKE ?", label))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeEq(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeGt(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeGte(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeLt(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeLte(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyTimeNe(lastModifyTime time.Time) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserEq(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserGt(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserGte(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserIn(lastModifyUser ...string) EventGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserLike(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserLt(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserLte(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserNe(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserNotIn(lastModifyUser ...string) EventGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) LastModifyUserNotlike(lastModifyUser string) EventGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) Limit(limit int) EventGroupQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// MaxRateEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateEq(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate = ?", maxRate))
}

// MaxRateGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateGt(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate > ?", maxRate))
}

// MaxRateGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateGte(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate >= ?", maxRate))
}

// MaxRateIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateIn(maxRate ...int) EventGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate IN (?)", maxRate))
}

// MaxRateLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateLt(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate < ?", maxRate))
}

// MaxRateLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateLte(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate <= ?", maxRate))
}

// MaxRateNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateNe(maxRate int) EventGroupQuerySet {
	return qs.w(qs.db.Where("max_rate != ?", maxRate))
}

// MaxRateNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) MaxRateNotIn(maxRate ...int) EventGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate NOT IN (?)", maxRate))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) Offset(offset int) EventGroupQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs EventGroupQuerySet) One(ret *EventGroup) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByBkBizID() EventGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByBkDataID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByBkDataID() EventGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByCreateTime() EventGroupQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByCreator() EventGroupQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByEventGroupID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByEventGroupID() EventGroupQuerySet {
	return qs.w(qs.db.Order("event_group_id ASC"))
}

// OrderAscByEventGroupName is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByEventGroupName() EventGroupQuerySet {
	return qs.w(qs.db.Order("event_group_name ASC"))
}

// OrderAscByIsDelete is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByIsDelete() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_delete ASC"))
}

// OrderAscByIsEnable is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByIsEnable() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_enable ASC"))
}

// OrderAscByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByIsSplitMeasurement() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement ASC"))
}

// OrderAscByLabel is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByLabel() EventGroupQuerySet {
	return qs.w(qs.db.Order("label ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByLastModifyTime() EventGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByLastModifyUser() EventGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByMaxRate is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByMaxRate() EventGroupQuerySet {
	return qs.w(qs.db.Order("max_rate ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderAscByTableID() EventGroupQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByBkBizID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByBkBizID() EventGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByBkDataID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByBkDataID() EventGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByCreateTime() EventGroupQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByCreator() EventGroupQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByEventGroupID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByEventGroupID() EventGroupQuerySet {
	return qs.w(qs.db.Order("event_group_id DESC"))
}

// OrderDescByEventGroupName is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByEventGroupName() EventGroupQuerySet {
	return qs.w(qs.db.Order("event_group_name DESC"))
}

// OrderDescByIsDelete is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByIsDelete() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_delete DESC"))
}

// OrderDescByIsEnable is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByIsEnable() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_enable DESC"))
}

// OrderDescByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByIsSplitMeasurement() EventGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement DESC"))
}

// OrderDescByLabel is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByLabel() EventGroupQuerySet {
	return qs.w(qs.db.Order("label DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByLastModifyTime() EventGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByLastModifyUser() EventGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByMaxRate is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByMaxRate() EventGroupQuerySet {
	return qs.w(qs.db.Order("max_rate DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) OrderDescByTableID() EventGroupQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDEq(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDGt(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDGte(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDIn(tableID ...string) EventGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDLike(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDLt(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDLte(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDNe(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDNotIn(tableID ...string) EventGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs EventGroupQuerySet) TableIDNotlike(tableID string) EventGroupQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// SetBkBizID is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetBkBizID(bkBizID int) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.BkBizID)] = bkBizID
	return u
}

// SetBkDataID is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetBkDataID(bkDataID uint) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.BkDataID)] = bkDataID
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetCreateTime(createTime time.Time) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetCreator(creator string) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.Creator)] = creator
	return u
}

// SetEventGroupID is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetEventGroupID(eventGroupID uint) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.EventGroupID)] = eventGroupID
	return u
}

// SetEventGroupName is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetEventGroupName(eventGroupName string) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.EventGroupName)] = eventGroupName
	return u
}

// SetIsDelete is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetIsDelete(isDelete bool) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.IsDelete)] = isDelete
	return u
}

// SetIsEnable is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetIsEnable(isEnable bool) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.IsEnable)] = isEnable
	return u
}

// SetIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetIsSplitMeasurement(isSplitMeasurement bool) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.IsSplitMeasurement)] = isSplitMeasurement
	return u
}

// SetLabel is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetLabel(label string) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.Label)] = label
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetLastModifyTime(lastModifyTime time.Time) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetLastModifyUser(lastModifyUser string) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetMaxRate is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetMaxRate(maxRate int) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.MaxRate)] = maxRate
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) SetTableID(tableID string) EventGroupUpdater {
	u.fields[string(EventGroupDBSchema.TableID)] = tableID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u EventGroupUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set EventGroupQuerySet

// ===== BEGIN of EventGroup modifiers

// EventGroupDBSchemaField describes database schema field. It requires for method 'Update'
type EventGroupDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f EventGroupDBSchemaField) String() string {
	return string(f)
}

// EventGroupDBSchema stores db field names of EventGroup
var EventGroupDBSchema = struct {
	BkDataID           EventGroupDBSchemaField
	BkBizID            EventGroupDBSchemaField
	TableID            EventGroupDBSchemaField
	MaxRate            EventGroupDBSchemaField
	Label              EventGroupDBSchemaField
	IsEnable           EventGroupDBSchemaField
	IsDelete           EventGroupDBSchemaField
	Creator            EventGroupDBSchemaField
	CreateTime         EventGroupDBSchemaField
	LastModifyUser     EventGroupDBSchemaField
	LastModifyTime     EventGroupDBSchemaField
	IsSplitMeasurement EventGroupDBSchemaField
	EventGroupID       EventGroupDBSchemaField
	EventGroupName     EventGroupDBSchemaField
}{

	BkDataID:           EventGroupDBSchemaField("bk_data_id"),
	BkBizID:            EventGroupDBSchemaField("bk_biz_id"),
	TableID:            EventGroupDBSchemaField("table_id"),
	MaxRate:            EventGroupDBSchemaField("max_rate"),
	Label:              EventGroupDBSchemaField("label"),
	IsEnable:           EventGroupDBSchemaField("is_enable"),
	IsDelete:           EventGroupDBSchemaField("is_delete"),
	Creator:            EventGroupDBSchemaField("creator"),
	CreateTime:         EventGroupDBSchemaField("create_time"),
	LastModifyUser:     EventGroupDBSchemaField("last_modify_user"),
	LastModifyTime:     EventGroupDBSchemaField("last_modify_time"),
	IsSplitMeasurement: EventGroupDBSchemaField("is_split_measurement"),
	EventGroupID:       EventGroupDBSchemaField("event_group_id"),
	EventGroupName:     EventGroupDBSchemaField("event_group_name"),
}

// Update updates EventGroup fields by primary key
// nolint: dupl
func (o *EventGroup) Update(db *gorm.DB, fields ...EventGroupDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"bk_data_id":           o.BkDataID,
		"bk_biz_id":            o.BkBizID,
		"table_id":             o.TableID,
		"max_rate":             o.MaxRate,
		"label":                o.Label,
		"is_enable":            o.IsEnable,
		"is_delete":            o.IsDelete,
		"creator":              o.Creator,
		"create_time":          o.CreateTime,
		"last_modify_user":     o.LastModifyUser,
		"last_modify_time":     o.LastModifyTime,
		"is_split_measurement": o.IsSplitMeasurement,
		"event_group_id":       o.EventGroupID,
		"event_group_name":     o.EventGroupName,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update EventGroup %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// EventGroupUpdater is an EventGroup updates manager
type EventGroupUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewEventGroupUpdater creates new EventGroup updater
// nolint: dupl
func NewEventGroupUpdater(db *gorm.DB) EventGroupUpdater {
	return EventGroupUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&EventGroup{}),
	}
}

// ===== END of EventGroup modifiers

// ===== END of all query sets
