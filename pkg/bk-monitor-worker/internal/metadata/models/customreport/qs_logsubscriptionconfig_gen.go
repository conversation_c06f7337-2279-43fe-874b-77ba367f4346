// Code generated by go-queryset. DO NOT EDIT.
package customreport

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set LogSubscriptionConfigQuerySet

// LogSubscriptionConfigQuerySet is an queryset type for LogSubscriptionConfig
type LogSubscriptionConfigQuerySet struct {
	db *gorm.DB
}

// NewLogSubscriptionConfigQuerySet constructs new LogSubscriptionConfigQuerySet
func NewLogSubscriptionConfigQuerySet(db *gorm.DB) LogSubscriptionConfigQuerySet {
	return LogSubscriptionConfigQuerySet{
		db: db.Model(&LogSubscriptionConfig{}),
	}
}

func (qs LogSubscriptionConfigQuerySet) w(db *gorm.DB) LogSubscriptionConfigQuerySet {
	return NewLogSubscriptionConfigQuerySet(db)
}

func (qs LogSubscriptionConfigQuerySet) Select(fields ...LogSubscriptionConfigDBSchemaField) LogSubscriptionConfigQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *LogSubscriptionConfig) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *LogSubscriptionConfig) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) All(ret *[]LogSubscriptionConfig) error {
	return qs.db.Find(ret).Error
}

// BkBizIdEq is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdEq(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizId))
}

// BkBizIdGt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdGt(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizId))
}

// BkBizIdGte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdGte(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizId))
}

// BkBizIdIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdIn(bkBizId ...int) LogSubscriptionConfigQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizId))
}

// BkBizIdLt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdLt(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizId))
}

// BkBizIdLte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdLte(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizId))
}

// BkBizIdNe is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdNe(bkBizId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizId))
}

// BkBizIdNotIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) BkBizIdNotIn(bkBizId ...int) LogSubscriptionConfigQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizId))
}

// ConfigEq is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigEq(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config = ?", config))
}

// ConfigGt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigGt(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config > ?", config))
}

// ConfigGte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigGte(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config >= ?", config))
}

// ConfigIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigIn(config ...string) LogSubscriptionConfigQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config IN (?)", config))
}

// ConfigLike is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigLike(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config LIKE ?", config))
}

// ConfigLt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigLt(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config < ?", config))
}

// ConfigLte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigLte(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config <= ?", config))
}

// ConfigNe is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigNe(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config != ?", config))
}

// ConfigNotIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigNotIn(config ...string) LogSubscriptionConfigQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config NOT IN (?)", config))
}

// ConfigNotlike is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) ConfigNotlike(config string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("config NOT LIKE ?", config))
}

// Count is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) Delete() error {
	return qs.db.Delete(LogSubscriptionConfig{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(LogSubscriptionConfig{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(LogSubscriptionConfig{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) GetUpdater() LogSubscriptionConfigUpdater {
	return NewLogSubscriptionConfigUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDEq(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDGt(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDGte(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDIn(ID ...uint) LogSubscriptionConfigQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDLt(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDLte(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDNe(ID uint) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) IDNotIn(ID ...uint) LogSubscriptionConfigQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) Limit(limit int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// LogNameEq is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameEq(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name = ?", logName))
}

// LogNameGt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameGt(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name > ?", logName))
}

// LogNameGte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameGte(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name >= ?", logName))
}

// LogNameIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameIn(logName ...string) LogSubscriptionConfigQuerySet {
	if len(logName) == 0 {
		qs.db.AddError(errors.New("must at least pass one logName in LogNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_name IN (?)", logName))
}

// LogNameLike is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameLike(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name LIKE ?", logName))
}

// LogNameLt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameLt(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name < ?", logName))
}

// LogNameLte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameLte(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name <= ?", logName))
}

// LogNameNe is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameNe(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name != ?", logName))
}

// LogNameNotIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameNotIn(logName ...string) LogSubscriptionConfigQuerySet {
	if len(logName) == 0 {
		qs.db.AddError(errors.New("must at least pass one logName in LogNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_name NOT IN (?)", logName))
}

// LogNameNotlike is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) LogNameNotlike(logName string) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("log_name NOT LIKE ?", logName))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) Offset(offset int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs LogSubscriptionConfigQuerySet) One(ret *LogSubscriptionConfig) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizId is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderAscByBkBizId() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByConfig is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderAscByConfig() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("config ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderAscByID() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByLogName is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderAscByLogName() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("log_name ASC"))
}

// OrderAscBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderAscBySubscriptionId() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("subscription_id ASC"))
}

// OrderDescByBkBizId is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderDescByBkBizId() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByConfig is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderDescByConfig() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("config DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderDescByID() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByLogName is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderDescByLogName() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("log_name DESC"))
}

// OrderDescBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) OrderDescBySubscriptionId() LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Order("subscription_id DESC"))
}

// SubscriptionIdEq is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdEq(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id = ?", subscriptionId))
}

// SubscriptionIdGt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdGt(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id > ?", subscriptionId))
}

// SubscriptionIdGte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdGte(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id >= ?", subscriptionId))
}

// SubscriptionIdIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdIn(subscriptionId ...int) LogSubscriptionConfigQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id IN (?)", subscriptionId))
}

// SubscriptionIdLt is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdLt(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id < ?", subscriptionId))
}

// SubscriptionIdLte is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdLte(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id <= ?", subscriptionId))
}

// SubscriptionIdNe is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdNe(subscriptionId int) LogSubscriptionConfigQuerySet {
	return qs.w(qs.db.Where("subscription_id != ?", subscriptionId))
}

// SubscriptionIdNotIn is an autogenerated method
// nolint: dupl
func (qs LogSubscriptionConfigQuerySet) SubscriptionIdNotIn(subscriptionId ...int) LogSubscriptionConfigQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id NOT IN (?)", subscriptionId))
}

// SetBkBizId is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) SetBkBizId(bkBizId int) LogSubscriptionConfigUpdater {
	u.fields[string(LogSubscriptionConfigDBSchema.BkBizId)] = bkBizId
	return u
}

// SetConfig is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) SetConfig(config string) LogSubscriptionConfigUpdater {
	u.fields[string(LogSubscriptionConfigDBSchema.Config)] = config
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) SetID(ID uint) LogSubscriptionConfigUpdater {
	u.fields[string(LogSubscriptionConfigDBSchema.ID)] = ID
	return u
}

// SetLogName is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) SetLogName(logName string) LogSubscriptionConfigUpdater {
	u.fields[string(LogSubscriptionConfigDBSchema.LogName)] = logName
	return u
}

// SetSubscriptionId is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) SetSubscriptionId(subscriptionId int) LogSubscriptionConfigUpdater {
	u.fields[string(LogSubscriptionConfigDBSchema.SubscriptionId)] = subscriptionId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u LogSubscriptionConfigUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set LogSubscriptionConfigQuerySet

// ===== BEGIN of LogSubscriptionConfig modifiers

// LogSubscriptionConfigDBSchemaField describes database schema field. It requires for method 'Update'
type LogSubscriptionConfigDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f LogSubscriptionConfigDBSchemaField) String() string {
	return string(f)
}

// LogSubscriptionConfigDBSchema stores db field names of LogSubscriptionConfig
var LogSubscriptionConfigDBSchema = struct {
	ID             LogSubscriptionConfigDBSchemaField
	BkBizId        LogSubscriptionConfigDBSchemaField
	SubscriptionId LogSubscriptionConfigDBSchemaField
	Config         LogSubscriptionConfigDBSchemaField
	LogName        LogSubscriptionConfigDBSchemaField
}{

	ID:             LogSubscriptionConfigDBSchemaField("id"),
	BkBizId:        LogSubscriptionConfigDBSchemaField("bk_biz_id"),
	SubscriptionId: LogSubscriptionConfigDBSchemaField("subscription_id"),
	Config:         LogSubscriptionConfigDBSchemaField("config"),
	LogName:        LogSubscriptionConfigDBSchemaField("log_name"),
}

// Update updates LogSubscriptionConfig fields by primary key
// nolint: dupl
func (o *LogSubscriptionConfig) Update(db *gorm.DB, fields ...LogSubscriptionConfigDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":              o.ID,
		"bk_biz_id":       o.BkBizId,
		"subscription_id": o.SubscriptionId,
		"config":          o.Config,
		"log_name":        o.LogName,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update LogSubscriptionConfig %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// LogSubscriptionConfigUpdater is an LogSubscriptionConfig updates manager
type LogSubscriptionConfigUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewLogSubscriptionConfigUpdater creates new LogSubscriptionConfig updater
// nolint: dupl
func NewLogSubscriptionConfigUpdater(db *gorm.DB) LogSubscriptionConfigUpdater {
	return LogSubscriptionConfigUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&LogSubscriptionConfig{}),
	}
}

// ===== END of LogSubscriptionConfig modifiers

// ===== END of all query sets
