// Code generated by go-queryset. DO NOT EDIT.
package customreport

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set LogGroupQuerySet

// LogGroupQuerySet is an queryset type for LogGroup
type LogGroupQuerySet struct {
	db *gorm.DB
}

// NewLogGroupQuerySet constructs new LogGroupQuerySet
func NewLogGroupQuerySet(db *gorm.DB) LogGroupQuerySet {
	return LogGroupQuerySet{
		db: db.Model(&LogGroup{}),
	}
}

func (qs LogGroupQuerySet) w(db *gorm.DB) LogGroupQuerySet {
	return NewLogGroupQuerySet(db)
}

func (qs LogGroupQuerySet) Select(fields ...LogGroupDBSchemaField) LogGroupQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *LogGroup) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *LogGroup) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) All(ret *[]LogGroup) error {
	return qs.db.Find(ret).Error
}

// BkBizIDEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDEq(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizID))
}

// BkBizIDGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDGt(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizID))
}

// BkBizIDGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDGte(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizID))
}

// BkBizIDIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDIn(bkBizID ...int) LogGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizID))
}

// BkBizIDLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDLt(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizID))
}

// BkBizIDLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDLte(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizID))
}

// BkBizIDNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDNe(bkBizID int) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizID))
}

// BkBizIDNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkBizIDNotIn(bkBizID ...int) LogGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizID))
}

// BkDataIDEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDEq(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataID))
}

// BkDataIDGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDGt(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataID))
}

// BkDataIDGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDGte(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataID))
}

// BkDataIDIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDIn(bkDataID ...uint) LogGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataID))
}

// BkDataIDLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDLt(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataID))
}

// BkDataIDLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDLte(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataID))
}

// BkDataIDNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDNe(bkDataID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataID))
}

// BkDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) BkDataIDNotIn(bkDataID ...uint) LogGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeEq(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeGt(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeGte(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeLt(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeLte(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreateTimeNe(createTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorEq(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorGt(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorGte(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorIn(creator ...string) LogGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorLike(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorLt(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorLte(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorNe(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorNotIn(creator ...string) LogGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) CreatorNotlike(creator string) LogGroupQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) Delete() error {
	return qs.db.Delete(LogGroup{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(LogGroup{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(LogGroup{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) GetUpdater() LogGroupUpdater {
	return NewLogGroupUpdater(qs.db)
}

// IsDeleteEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsDeleteEq(isDelete bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_delete = ?", isDelete))
}

// IsDeleteIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsDeleteIn(isDelete ...bool) LogGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete IN (?)", isDelete))
}

// IsDeleteNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsDeleteNe(isDelete bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_delete != ?", isDelete))
}

// IsDeleteNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsDeleteNotIn(isDelete ...bool) LogGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete NOT IN (?)", isDelete))
}

// IsEnableEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsEnableEq(isEnable bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_enable = ?", isEnable))
}

// IsEnableIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsEnableIn(isEnable ...bool) LogGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable IN (?)", isEnable))
}

// IsEnableNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsEnableNe(isEnable bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_enable != ?", isEnable))
}

// IsEnableNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsEnableNotIn(isEnable ...bool) LogGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable NOT IN (?)", isEnable))
}

// IsSplitMeasurementEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsSplitMeasurementEq(isSplitMeasurement bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement = ?", isSplitMeasurement))
}

// IsSplitMeasurementIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsSplitMeasurementIn(isSplitMeasurement ...bool) LogGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement IN (?)", isSplitMeasurement))
}

// IsSplitMeasurementNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsSplitMeasurementNe(isSplitMeasurement bool) LogGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement != ?", isSplitMeasurement))
}

// IsSplitMeasurementNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) IsSplitMeasurementNotIn(isSplitMeasurement ...bool) LogGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement NOT IN (?)", isSplitMeasurement))
}

// LabelEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelEq(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label = ?", label))
}

// LabelGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelGt(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label > ?", label))
}

// LabelGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelGte(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label >= ?", label))
}

// LabelIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelIn(label ...string) LogGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label IN (?)", label))
}

// LabelLike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelLike(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label LIKE ?", label))
}

// LabelLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelLt(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label < ?", label))
}

// LabelLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelLte(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label <= ?", label))
}

// LabelNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelNe(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label != ?", label))
}

// LabelNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelNotIn(label ...string) LogGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label NOT IN (?)", label))
}

// LabelNotlike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LabelNotlike(label string) LogGroupQuerySet {
	return qs.w(qs.db.Where("label NOT LIKE ?", label))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeEq(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeGt(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeGte(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeLt(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeLte(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyTimeNe(lastModifyTime time.Time) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserEq(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserGt(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserGte(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserIn(lastModifyUser ...string) LogGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserLike(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserLt(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserLte(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserNe(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserNotIn(lastModifyUser ...string) LogGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LastModifyUserNotlike(lastModifyUser string) LogGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) Limit(limit int) LogGroupQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// LogGroupIDEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDEq(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id = ?", logGroupID))
}

// LogGroupIDGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDGt(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id > ?", logGroupID))
}

// LogGroupIDGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDGte(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id >= ?", logGroupID))
}

// LogGroupIDIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDIn(logGroupID ...uint) LogGroupQuerySet {
	if len(logGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one logGroupID in LogGroupIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_group_id IN (?)", logGroupID))
}

// LogGroupIDLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDLt(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id < ?", logGroupID))
}

// LogGroupIDLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDLte(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id <= ?", logGroupID))
}

// LogGroupIDNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDNe(logGroupID uint) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_id != ?", logGroupID))
}

// LogGroupIDNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupIDNotIn(logGroupID ...uint) LogGroupQuerySet {
	if len(logGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one logGroupID in LogGroupIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_group_id NOT IN (?)", logGroupID))
}

// LogGroupNameEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameEq(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name = ?", logGroupName))
}

// LogGroupNameGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameGt(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name > ?", logGroupName))
}

// LogGroupNameGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameGte(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name >= ?", logGroupName))
}

// LogGroupNameIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameIn(logGroupName ...string) LogGroupQuerySet {
	if len(logGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one logGroupName in LogGroupNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_group_name IN (?)", logGroupName))
}

// LogGroupNameLike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameLike(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name LIKE ?", logGroupName))
}

// LogGroupNameLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameLt(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name < ?", logGroupName))
}

// LogGroupNameLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameLte(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name <= ?", logGroupName))
}

// LogGroupNameNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameNe(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name != ?", logGroupName))
}

// LogGroupNameNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameNotIn(logGroupName ...string) LogGroupQuerySet {
	if len(logGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one logGroupName in LogGroupNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("log_group_name NOT IN (?)", logGroupName))
}

// LogGroupNameNotlike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) LogGroupNameNotlike(logGroupName string) LogGroupQuerySet {
	return qs.w(qs.db.Where("log_group_name NOT LIKE ?", logGroupName))
}

// MaxRateEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateEq(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate = ?", maxRate))
}

// MaxRateGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateGt(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate > ?", maxRate))
}

// MaxRateGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateGte(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate >= ?", maxRate))
}

// MaxRateIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateIn(maxRate ...int) LogGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate IN (?)", maxRate))
}

// MaxRateLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateLt(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate < ?", maxRate))
}

// MaxRateLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateLte(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate <= ?", maxRate))
}

// MaxRateNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateNe(maxRate int) LogGroupQuerySet {
	return qs.w(qs.db.Where("max_rate != ?", maxRate))
}

// MaxRateNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) MaxRateNotIn(maxRate ...int) LogGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate NOT IN (?)", maxRate))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) Offset(offset int) LogGroupQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs LogGroupQuerySet) One(ret *LogGroup) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByBkBizID() LogGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByBkDataID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByBkDataID() LogGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByCreateTime() LogGroupQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByCreator() LogGroupQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByIsDelete is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByIsDelete() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_delete ASC"))
}

// OrderAscByIsEnable is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByIsEnable() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_enable ASC"))
}

// OrderAscByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByIsSplitMeasurement() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement ASC"))
}

// OrderAscByLabel is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByLabel() LogGroupQuerySet {
	return qs.w(qs.db.Order("label ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByLastModifyTime() LogGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByLastModifyUser() LogGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByLogGroupID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByLogGroupID() LogGroupQuerySet {
	return qs.w(qs.db.Order("log_group_id ASC"))
}

// OrderAscByLogGroupName is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByLogGroupName() LogGroupQuerySet {
	return qs.w(qs.db.Order("log_group_name ASC"))
}

// OrderAscByMaxRate is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByMaxRate() LogGroupQuerySet {
	return qs.w(qs.db.Order("max_rate ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderAscByTableID() LogGroupQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByBkBizID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByBkBizID() LogGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByBkDataID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByBkDataID() LogGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByCreateTime() LogGroupQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByCreator() LogGroupQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByIsDelete is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByIsDelete() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_delete DESC"))
}

// OrderDescByIsEnable is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByIsEnable() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_enable DESC"))
}

// OrderDescByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByIsSplitMeasurement() LogGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement DESC"))
}

// OrderDescByLabel is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByLabel() LogGroupQuerySet {
	return qs.w(qs.db.Order("label DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByLastModifyTime() LogGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByLastModifyUser() LogGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByLogGroupID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByLogGroupID() LogGroupQuerySet {
	return qs.w(qs.db.Order("log_group_id DESC"))
}

// OrderDescByLogGroupName is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByLogGroupName() LogGroupQuerySet {
	return qs.w(qs.db.Order("log_group_name DESC"))
}

// OrderDescByMaxRate is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByMaxRate() LogGroupQuerySet {
	return qs.w(qs.db.Order("max_rate DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) OrderDescByTableID() LogGroupQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDEq(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDGt(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDGte(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDIn(tableID ...string) LogGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDLike(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDLt(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDLte(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDNe(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDNotIn(tableID ...string) LogGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs LogGroupQuerySet) TableIDNotlike(tableID string) LogGroupQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// SetBkBizID is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetBkBizID(bkBizID int) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.BkBizID)] = bkBizID
	return u
}

// SetBkDataID is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetBkDataID(bkDataID uint) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.BkDataID)] = bkDataID
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetCreateTime(createTime time.Time) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetCreator(creator string) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.Creator)] = creator
	return u
}

// SetIsDelete is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetIsDelete(isDelete bool) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.IsDelete)] = isDelete
	return u
}

// SetIsEnable is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetIsEnable(isEnable bool) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.IsEnable)] = isEnable
	return u
}

// SetIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetIsSplitMeasurement(isSplitMeasurement bool) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.IsSplitMeasurement)] = isSplitMeasurement
	return u
}

// SetLabel is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetLabel(label string) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.Label)] = label
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetLastModifyTime(lastModifyTime time.Time) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetLastModifyUser(lastModifyUser string) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetLogGroupID is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetLogGroupID(logGroupID uint) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.LogGroupID)] = logGroupID
	return u
}

// SetLogGroupName is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetLogGroupName(logGroupName string) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.LogGroupName)] = logGroupName
	return u
}

// SetMaxRate is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetMaxRate(maxRate int) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.MaxRate)] = maxRate
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) SetTableID(tableID string) LogGroupUpdater {
	u.fields[string(LogGroupDBSchema.TableID)] = tableID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u LogGroupUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set LogGroupQuerySet

// ===== BEGIN of LogGroup modifiers

// LogGroupDBSchemaField describes database schema field. It requires for method 'Update'
type LogGroupDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f LogGroupDBSchemaField) String() string {
	return string(f)
}

// LogGroupDBSchema stores db field names of LogGroup
var LogGroupDBSchema = struct {
	BkDataID           LogGroupDBSchemaField
	BkBizID            LogGroupDBSchemaField
	TableID            LogGroupDBSchemaField
	MaxRate            LogGroupDBSchemaField
	Label              LogGroupDBSchemaField
	IsEnable           LogGroupDBSchemaField
	IsDelete           LogGroupDBSchemaField
	Creator            LogGroupDBSchemaField
	CreateTime         LogGroupDBSchemaField
	LastModifyUser     LogGroupDBSchemaField
	LastModifyTime     LogGroupDBSchemaField
	IsSplitMeasurement LogGroupDBSchemaField
	LogGroupID         LogGroupDBSchemaField
	LogGroupName       LogGroupDBSchemaField
}{

	BkDataID:           LogGroupDBSchemaField("bk_data_id"),
	BkBizID:            LogGroupDBSchemaField("bk_biz_id"),
	TableID:            LogGroupDBSchemaField("table_id"),
	MaxRate:            LogGroupDBSchemaField("max_rate"),
	Label:              LogGroupDBSchemaField("label"),
	IsEnable:           LogGroupDBSchemaField("is_enable"),
	IsDelete:           LogGroupDBSchemaField("is_delete"),
	Creator:            LogGroupDBSchemaField("creator"),
	CreateTime:         LogGroupDBSchemaField("create_time"),
	LastModifyUser:     LogGroupDBSchemaField("last_modify_user"),
	LastModifyTime:     LogGroupDBSchemaField("last_modify_time"),
	IsSplitMeasurement: LogGroupDBSchemaField("is_split_measurement"),
	LogGroupID:         LogGroupDBSchemaField("log_group_id"),
	LogGroupName:       LogGroupDBSchemaField("log_group_name"),
}

// Update updates LogGroup fields by primary key
// nolint: dupl
func (o *LogGroup) Update(db *gorm.DB, fields ...LogGroupDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"bk_data_id":           o.BkDataID,
		"bk_biz_id":            o.BkBizID,
		"table_id":             o.TableID,
		"max_rate":             o.MaxRate,
		"label":                o.Label,
		"is_enable":            o.IsEnable,
		"is_delete":            o.IsDelete,
		"creator":              o.Creator,
		"create_time":          o.CreateTime,
		"last_modify_user":     o.LastModifyUser,
		"last_modify_time":     o.LastModifyTime,
		"is_split_measurement": o.IsSplitMeasurement,
		"log_group_id":         o.LogGroupID,
		"log_group_name":       o.LogGroupName,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update LogGroup %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// LogGroupUpdater is an LogGroup updates manager
type LogGroupUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewLogGroupUpdater creates new LogGroup updater
// nolint: dupl
func NewLogGroupUpdater(db *gorm.DB) LogGroupUpdater {
	return LogGroupUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&LogGroup{}),
	}
}

// ===== END of LogGroup modifiers

// ===== END of all query sets
