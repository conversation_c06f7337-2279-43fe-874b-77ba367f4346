// Code generated by go-queryset. DO NOT EDIT.
package customreport

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set TimeSeriesGroupQuerySet

// TimeSeriesGroupQuerySet is an queryset type for TimeSeriesGroup
type TimeSeriesGroupQuerySet struct {
	db *gorm.DB
}

// NewTimeSeriesGroupQuerySet constructs new TimeSeriesGroupQuerySet
func NewTimeSeriesGroupQuerySet(db *gorm.DB) TimeSeriesGroupQuerySet {
	return TimeSeriesGroupQuerySet{
		db: db.Model(&TimeSeriesGroup{}),
	}
}

func (qs TimeSeriesGroupQuerySet) w(db *gorm.DB) TimeSeriesGroupQuerySet {
	return NewTimeSeriesGroupQuerySet(db)
}

func (qs TimeSeriesGroupQuerySet) Select(fields ...TimeSeriesGroupDBSchemaField) TimeSeriesGroupQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *TimeSeriesGroup) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *TimeSeriesGroup) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) All(ret *[]TimeSeriesGroup) error {
	return qs.db.Find(ret).Error
}

// BkBizIDEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDEq(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizID))
}

// BkBizIDGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDGt(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizID))
}

// BkBizIDGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDGte(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizID))
}

// BkBizIDIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDIn(bkBizID ...int) TimeSeriesGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizID))
}

// BkBizIDLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDLt(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizID))
}

// BkBizIDLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDLte(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizID))
}

// BkBizIDNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDNe(bkBizID int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizID))
}

// BkBizIDNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkBizIDNotIn(bkBizID ...int) TimeSeriesGroupQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizID))
}

// BkDataIDEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDEq(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataID))
}

// BkDataIDGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDGt(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataID))
}

// BkDataIDGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDGte(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataID))
}

// BkDataIDIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDIn(bkDataID ...uint) TimeSeriesGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataID))
}

// BkDataIDLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDLt(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataID))
}

// BkDataIDLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDLte(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataID))
}

// BkDataIDNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDNe(bkDataID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataID))
}

// BkDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) BkDataIDNotIn(bkDataID ...uint) TimeSeriesGroupQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeEq(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeGt(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeGte(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeLt(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeLte(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreateTimeNe(createTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorEq(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorGt(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorGte(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorIn(creator ...string) TimeSeriesGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorLike(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorLt(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorLte(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorNe(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorNotIn(creator ...string) TimeSeriesGroupQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) CreatorNotlike(creator string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) Delete() error {
	return qs.db.Delete(TimeSeriesGroup{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(TimeSeriesGroup{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(TimeSeriesGroup{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) GetUpdater() TimeSeriesGroupUpdater {
	return NewTimeSeriesGroupUpdater(qs.db)
}

// IsDeleteEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsDeleteEq(isDelete bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_delete = ?", isDelete))
}

// IsDeleteIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsDeleteIn(isDelete ...bool) TimeSeriesGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete IN (?)", isDelete))
}

// IsDeleteNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsDeleteNe(isDelete bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_delete != ?", isDelete))
}

// IsDeleteNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsDeleteNotIn(isDelete ...bool) TimeSeriesGroupQuerySet {
	if len(isDelete) == 0 {
		qs.db.AddError(errors.New("must at least pass one isDelete in IsDeleteNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_delete NOT IN (?)", isDelete))
}

// IsEnableEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsEnableEq(isEnable bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_enable = ?", isEnable))
}

// IsEnableIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsEnableIn(isEnable ...bool) TimeSeriesGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable IN (?)", isEnable))
}

// IsEnableNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsEnableNe(isEnable bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_enable != ?", isEnable))
}

// IsEnableNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsEnableNotIn(isEnable ...bool) TimeSeriesGroupQuerySet {
	if len(isEnable) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnable in IsEnableNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enable NOT IN (?)", isEnable))
}

// IsSplitMeasurementEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsSplitMeasurementEq(isSplitMeasurement bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement = ?", isSplitMeasurement))
}

// IsSplitMeasurementIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsSplitMeasurementIn(isSplitMeasurement ...bool) TimeSeriesGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement IN (?)", isSplitMeasurement))
}

// IsSplitMeasurementNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsSplitMeasurementNe(isSplitMeasurement bool) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("is_split_measurement != ?", isSplitMeasurement))
}

// IsSplitMeasurementNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) IsSplitMeasurementNotIn(isSplitMeasurement ...bool) TimeSeriesGroupQuerySet {
	if len(isSplitMeasurement) == 0 {
		qs.db.AddError(errors.New("must at least pass one isSplitMeasurement in IsSplitMeasurementNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_split_measurement NOT IN (?)", isSplitMeasurement))
}

// LabelEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelEq(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label = ?", label))
}

// LabelGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelGt(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label > ?", label))
}

// LabelGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelGte(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label >= ?", label))
}

// LabelIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelIn(label ...string) TimeSeriesGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label IN (?)", label))
}

// LabelLike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelLike(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label LIKE ?", label))
}

// LabelLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelLt(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label < ?", label))
}

// LabelLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelLte(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label <= ?", label))
}

// LabelNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelNe(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label != ?", label))
}

// LabelNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelNotIn(label ...string) TimeSeriesGroupQuerySet {
	if len(label) == 0 {
		qs.db.AddError(errors.New("must at least pass one label in LabelNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label NOT IN (?)", label))
}

// LabelNotlike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LabelNotlike(label string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("label NOT LIKE ?", label))
}

// LastModifyTimeEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeEq(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time = ?", lastModifyTime))
}

// LastModifyTimeGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeGt(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time > ?", lastModifyTime))
}

// LastModifyTimeGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeGte(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time >= ?", lastModifyTime))
}

// LastModifyTimeLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeLt(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time < ?", lastModifyTime))
}

// LastModifyTimeLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeLte(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time <= ?", lastModifyTime))
}

// LastModifyTimeNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyTimeNe(lastModifyTime time.Time) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_time != ?", lastModifyTime))
}

// LastModifyUserEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserEq(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user = ?", lastModifyUser))
}

// LastModifyUserGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserGt(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user > ?", lastModifyUser))
}

// LastModifyUserGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserGte(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user >= ?", lastModifyUser))
}

// LastModifyUserIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserIn(lastModifyUser ...string) TimeSeriesGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user IN (?)", lastModifyUser))
}

// LastModifyUserLike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserLike(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user LIKE ?", lastModifyUser))
}

// LastModifyUserLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserLt(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user < ?", lastModifyUser))
}

// LastModifyUserLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserLte(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user <= ?", lastModifyUser))
}

// LastModifyUserNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserNe(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user != ?", lastModifyUser))
}

// LastModifyUserNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserNotIn(lastModifyUser ...string) TimeSeriesGroupQuerySet {
	if len(lastModifyUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one lastModifyUser in LastModifyUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("last_modify_user NOT IN (?)", lastModifyUser))
}

// LastModifyUserNotlike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) LastModifyUserNotlike(lastModifyUser string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("last_modify_user NOT LIKE ?", lastModifyUser))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) Limit(limit int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// MaxRateEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateEq(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate = ?", maxRate))
}

// MaxRateGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateGt(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate > ?", maxRate))
}

// MaxRateGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateGte(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate >= ?", maxRate))
}

// MaxRateIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateIn(maxRate ...int) TimeSeriesGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate IN (?)", maxRate))
}

// MaxRateLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateLt(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate < ?", maxRate))
}

// MaxRateLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateLte(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate <= ?", maxRate))
}

// MaxRateNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateNe(maxRate int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("max_rate != ?", maxRate))
}

// MaxRateNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) MaxRateNotIn(maxRate ...int) TimeSeriesGroupQuerySet {
	if len(maxRate) == 0 {
		qs.db.AddError(errors.New("must at least pass one maxRate in MaxRateNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("max_rate NOT IN (?)", maxRate))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) Offset(offset int) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs TimeSeriesGroupQuerySet) One(ret *TimeSeriesGroup) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByBkBizID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByBkDataID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByBkDataID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByCreateTime() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByCreator() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByIsDelete is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByIsDelete() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_delete ASC"))
}

// OrderAscByIsEnable is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByIsEnable() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_enable ASC"))
}

// OrderAscByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByIsSplitMeasurement() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement ASC"))
}

// OrderAscByLabel is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByLabel() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("label ASC"))
}

// OrderAscByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByLastModifyTime() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time ASC"))
}

// OrderAscByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByLastModifyUser() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user ASC"))
}

// OrderAscByMaxRate is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByMaxRate() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("max_rate ASC"))
}

// OrderAscByTableID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByTableID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderAscByTimeSeriesGroupID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByTimeSeriesGroupID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("time_series_group_id ASC"))
}

// OrderAscByTimeSeriesGroupName is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderAscByTimeSeriesGroupName() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("time_series_group_name ASC"))
}

// OrderDescByBkBizID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByBkBizID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByBkDataID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByBkDataID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByCreateTime() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByCreator() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByIsDelete is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByIsDelete() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_delete DESC"))
}

// OrderDescByIsEnable is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByIsEnable() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_enable DESC"))
}

// OrderDescByIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByIsSplitMeasurement() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("is_split_measurement DESC"))
}

// OrderDescByLabel is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByLabel() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("label DESC"))
}

// OrderDescByLastModifyTime is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByLastModifyTime() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_time DESC"))
}

// OrderDescByLastModifyUser is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByLastModifyUser() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("last_modify_user DESC"))
}

// OrderDescByMaxRate is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByMaxRate() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("max_rate DESC"))
}

// OrderDescByTableID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByTableID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// OrderDescByTimeSeriesGroupID is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByTimeSeriesGroupID() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("time_series_group_id DESC"))
}

// OrderDescByTimeSeriesGroupName is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) OrderDescByTimeSeriesGroupName() TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Order("time_series_group_name DESC"))
}

// TableIDEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDEq(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableID))
}

// TableIDGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDGt(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableID))
}

// TableIDGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDGte(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableID))
}

// TableIDIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDIn(tableID ...string) TimeSeriesGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableID))
}

// TableIDLike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDLike(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableID))
}

// TableIDLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDLt(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableID))
}

// TableIDLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDLte(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableID))
}

// TableIDNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDNe(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableID))
}

// TableIDNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDNotIn(tableID ...string) TimeSeriesGroupQuerySet {
	if len(tableID) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableID in TableIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableID))
}

// TableIDNotlike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TableIDNotlike(tableID string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableID))
}

// TimeSeriesGroupIDEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDEq(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id = ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDGt(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id > ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDGte(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id >= ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDIn(timeSeriesGroupID ...uint) TimeSeriesGroupQuerySet {
	if len(timeSeriesGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeSeriesGroupID in TimeSeriesGroupIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_series_group_id IN (?)", timeSeriesGroupID))
}

// TimeSeriesGroupIDLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDLt(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id < ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDLte(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id <= ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDNe(timeSeriesGroupID uint) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_id != ?", timeSeriesGroupID))
}

// TimeSeriesGroupIDNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupIDNotIn(timeSeriesGroupID ...uint) TimeSeriesGroupQuerySet {
	if len(timeSeriesGroupID) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeSeriesGroupID in TimeSeriesGroupIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_series_group_id NOT IN (?)", timeSeriesGroupID))
}

// TimeSeriesGroupNameEq is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameEq(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name = ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameGt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameGt(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name > ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameGte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameGte(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name >= ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameIn(timeSeriesGroupName ...string) TimeSeriesGroupQuerySet {
	if len(timeSeriesGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeSeriesGroupName in TimeSeriesGroupNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_series_group_name IN (?)", timeSeriesGroupName))
}

// TimeSeriesGroupNameLike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameLike(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name LIKE ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameLt is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameLt(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name < ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameLte is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameLte(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name <= ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameNe is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameNe(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name != ?", timeSeriesGroupName))
}

// TimeSeriesGroupNameNotIn is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameNotIn(timeSeriesGroupName ...string) TimeSeriesGroupQuerySet {
	if len(timeSeriesGroupName) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeSeriesGroupName in TimeSeriesGroupNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_series_group_name NOT IN (?)", timeSeriesGroupName))
}

// TimeSeriesGroupNameNotlike is an autogenerated method
// nolint: dupl
func (qs TimeSeriesGroupQuerySet) TimeSeriesGroupNameNotlike(timeSeriesGroupName string) TimeSeriesGroupQuerySet {
	return qs.w(qs.db.Where("time_series_group_name NOT LIKE ?", timeSeriesGroupName))
}

// SetBkBizID is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetBkBizID(bkBizID int) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.BkBizID)] = bkBizID
	return u
}

// SetBkDataID is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetBkDataID(bkDataID uint) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.BkDataID)] = bkDataID
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetCreateTime(createTime time.Time) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetCreator(creator string) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.Creator)] = creator
	return u
}

// SetIsDelete is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetIsDelete(isDelete bool) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.IsDelete)] = isDelete
	return u
}

// SetIsEnable is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetIsEnable(isEnable bool) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.IsEnable)] = isEnable
	return u
}

// SetIsSplitMeasurement is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetIsSplitMeasurement(isSplitMeasurement bool) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.IsSplitMeasurement)] = isSplitMeasurement
	return u
}

// SetLabel is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetLabel(label string) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.Label)] = label
	return u
}

// SetLastModifyTime is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetLastModifyTime(lastModifyTime time.Time) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.LastModifyTime)] = lastModifyTime
	return u
}

// SetLastModifyUser is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetLastModifyUser(lastModifyUser string) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.LastModifyUser)] = lastModifyUser
	return u
}

// SetMaxRate is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetMaxRate(maxRate int) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.MaxRate)] = maxRate
	return u
}

// SetTableID is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetTableID(tableID string) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.TableID)] = tableID
	return u
}

// SetTimeSeriesGroupID is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetTimeSeriesGroupID(timeSeriesGroupID uint) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.TimeSeriesGroupID)] = timeSeriesGroupID
	return u
}

// SetTimeSeriesGroupName is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) SetTimeSeriesGroupName(timeSeriesGroupName string) TimeSeriesGroupUpdater {
	u.fields[string(TimeSeriesGroupDBSchema.TimeSeriesGroupName)] = timeSeriesGroupName
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u TimeSeriesGroupUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set TimeSeriesGroupQuerySet

// ===== BEGIN of TimeSeriesGroup modifiers

// TimeSeriesGroupDBSchemaField describes database schema field. It requires for method 'Update'
type TimeSeriesGroupDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f TimeSeriesGroupDBSchemaField) String() string {
	return string(f)
}

// TimeSeriesGroupDBSchema stores db field names of TimeSeriesGroup
var TimeSeriesGroupDBSchema = struct {
	BkDataID            TimeSeriesGroupDBSchemaField
	BkBizID             TimeSeriesGroupDBSchemaField
	TableID             TimeSeriesGroupDBSchemaField
	MaxRate             TimeSeriesGroupDBSchemaField
	Label               TimeSeriesGroupDBSchemaField
	IsEnable            TimeSeriesGroupDBSchemaField
	IsDelete            TimeSeriesGroupDBSchemaField
	Creator             TimeSeriesGroupDBSchemaField
	CreateTime          TimeSeriesGroupDBSchemaField
	LastModifyUser      TimeSeriesGroupDBSchemaField
	LastModifyTime      TimeSeriesGroupDBSchemaField
	IsSplitMeasurement  TimeSeriesGroupDBSchemaField
	TimeSeriesGroupID   TimeSeriesGroupDBSchemaField
	TimeSeriesGroupName TimeSeriesGroupDBSchemaField
}{

	BkDataID:            TimeSeriesGroupDBSchemaField("bk_data_id"),
	BkBizID:             TimeSeriesGroupDBSchemaField("bk_biz_id"),
	TableID:             TimeSeriesGroupDBSchemaField("table_id"),
	MaxRate:             TimeSeriesGroupDBSchemaField("max_rate"),
	Label:               TimeSeriesGroupDBSchemaField("label"),
	IsEnable:            TimeSeriesGroupDBSchemaField("is_enable"),
	IsDelete:            TimeSeriesGroupDBSchemaField("is_delete"),
	Creator:             TimeSeriesGroupDBSchemaField("creator"),
	CreateTime:          TimeSeriesGroupDBSchemaField("create_time"),
	LastModifyUser:      TimeSeriesGroupDBSchemaField("last_modify_user"),
	LastModifyTime:      TimeSeriesGroupDBSchemaField("last_modify_time"),
	IsSplitMeasurement:  TimeSeriesGroupDBSchemaField("is_split_measurement"),
	TimeSeriesGroupID:   TimeSeriesGroupDBSchemaField("time_series_group_id"),
	TimeSeriesGroupName: TimeSeriesGroupDBSchemaField("time_series_group_name"),
}

// Update updates TimeSeriesGroup fields by primary key
// nolint: dupl
func (o *TimeSeriesGroup) Update(db *gorm.DB, fields ...TimeSeriesGroupDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"bk_data_id":             o.BkDataID,
		"bk_biz_id":              o.BkBizID,
		"table_id":               o.TableID,
		"max_rate":               o.MaxRate,
		"label":                  o.Label,
		"is_enable":              o.IsEnable,
		"is_delete":              o.IsDelete,
		"creator":                o.Creator,
		"create_time":            o.CreateTime,
		"last_modify_user":       o.LastModifyUser,
		"last_modify_time":       o.LastModifyTime,
		"is_split_measurement":   o.IsSplitMeasurement,
		"time_series_group_id":   o.TimeSeriesGroupID,
		"time_series_group_name": o.TimeSeriesGroupName,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update TimeSeriesGroup %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// TimeSeriesGroupUpdater is an TimeSeriesGroup updates manager
type TimeSeriesGroupUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewTimeSeriesGroupUpdater creates new TimeSeriesGroup updater
// nolint: dupl
func NewTimeSeriesGroupUpdater(db *gorm.DB) TimeSeriesGroupUpdater {
	return TimeSeriesGroupUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&TimeSeriesGroup{}),
	}
}

// ===== END of TimeSeriesGroup modifiers

// ===== END of all query sets
