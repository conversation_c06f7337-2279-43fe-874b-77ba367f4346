// Code generated by go-queryset. DO NOT EDIT.
package customreport

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set CustomReportSubscriptionQuerySet

// CustomReportSubscriptionQuerySet is an queryset type for CustomReportSubscription
type CustomReportSubscriptionQuerySet struct {
	db *gorm.DB
}

// NewCustomReportSubscriptionQuerySet constructs new CustomReportSubscriptionQuerySet
func NewCustomReportSubscriptionQuerySet(db *gorm.DB) CustomReportSubscriptionQuerySet {
	return CustomReportSubscriptionQuerySet{
		db: db.Model(&CustomReportSubscription{}),
	}
}

func (qs CustomReportSubscriptionQuerySet) w(db *gorm.DB) CustomReportSubscriptionQuerySet {
	return NewCustomReportSubscriptionQuerySet(db)
}

func (qs CustomReportSubscriptionQuerySet) Select(fields ...CustomReportSubscriptionDBSchemaField) CustomReportSubscriptionQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *CustomReportSubscription) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *CustomReportSubscription) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) All(ret *[]CustomReportSubscription) error {
	return qs.db.Find(ret).Error
}

// BkBizIdEq is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdEq(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizId))
}

// BkBizIdGt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdGt(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizId))
}

// BkBizIdGte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdGte(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizId))
}

// BkBizIdIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdIn(bkBizId ...int) CustomReportSubscriptionQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizId))
}

// BkBizIdLt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdLt(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizId))
}

// BkBizIdLte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdLte(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizId))
}

// BkBizIdNe is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdNe(bkBizId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizId))
}

// BkBizIdNotIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkBizIdNotIn(bkBizId ...int) CustomReportSubscriptionQuerySet {
	if len(bkBizId) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizId in BkBizIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizId))
}

// BkDataIDEq is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDEq(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id = ?", bkDataID))
}

// BkDataIDGt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDGt(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id > ?", bkDataID))
}

// BkDataIDGte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDGte(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id >= ?", bkDataID))
}

// BkDataIDIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDIn(bkDataID ...uint) CustomReportSubscriptionQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id IN (?)", bkDataID))
}

// BkDataIDLt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDLt(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id < ?", bkDataID))
}

// BkDataIDLte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDLte(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id <= ?", bkDataID))
}

// BkDataIDNe is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDNe(bkDataID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("bk_data_id != ?", bkDataID))
}

// BkDataIDNotIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) BkDataIDNotIn(bkDataID ...uint) CustomReportSubscriptionQuerySet {
	if len(bkDataID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkDataID in BkDataIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_data_id NOT IN (?)", bkDataID))
}

// ConfigEq is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigEq(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config = ?", config))
}

// ConfigGt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigGt(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config > ?", config))
}

// ConfigGte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigGte(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config >= ?", config))
}

// ConfigIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigIn(config ...string) CustomReportSubscriptionQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config IN (?)", config))
}

// ConfigLike is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigLike(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config LIKE ?", config))
}

// ConfigLt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigLt(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config < ?", config))
}

// ConfigLte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigLte(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config <= ?", config))
}

// ConfigNe is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigNe(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config != ?", config))
}

// ConfigNotIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigNotIn(config ...string) CustomReportSubscriptionQuerySet {
	if len(config) == 0 {
		qs.db.AddError(errors.New("must at least pass one config in ConfigNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("config NOT IN (?)", config))
}

// ConfigNotlike is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) ConfigNotlike(config string) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("config NOT LIKE ?", config))
}

// Count is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) Delete() error {
	return qs.db.Delete(CustomReportSubscription{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(CustomReportSubscription{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(CustomReportSubscription{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) GetUpdater() CustomReportSubscriptionUpdater {
	return NewCustomReportSubscriptionUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDEq(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDGt(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDGte(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDIn(ID ...uint) CustomReportSubscriptionQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDLt(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDLte(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDNe(ID uint) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) IDNotIn(ID ...uint) CustomReportSubscriptionQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) Limit(limit int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) Offset(offset int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs CustomReportSubscriptionQuerySet) One(ret *CustomReportSubscription) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizId is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderAscByBkBizId() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByBkDataID is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderAscByBkDataID() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("bk_data_id ASC"))
}

// OrderAscByConfig is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderAscByConfig() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("config ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderAscByID() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderAscBySubscriptionId() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("subscription_id ASC"))
}

// OrderDescByBkBizId is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderDescByBkBizId() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByBkDataID is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderDescByBkDataID() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("bk_data_id DESC"))
}

// OrderDescByConfig is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderDescByConfig() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("config DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderDescByID() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescBySubscriptionId is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) OrderDescBySubscriptionId() CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Order("subscription_id DESC"))
}

// SubscriptionIdEq is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdEq(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id = ?", subscriptionId))
}

// SubscriptionIdGt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdGt(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id > ?", subscriptionId))
}

// SubscriptionIdGte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdGte(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id >= ?", subscriptionId))
}

// SubscriptionIdIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdIn(subscriptionId ...int) CustomReportSubscriptionQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id IN (?)", subscriptionId))
}

// SubscriptionIdLt is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdLt(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id < ?", subscriptionId))
}

// SubscriptionIdLte is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdLte(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id <= ?", subscriptionId))
}

// SubscriptionIdNe is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdNe(subscriptionId int) CustomReportSubscriptionQuerySet {
	return qs.w(qs.db.Where("subscription_id != ?", subscriptionId))
}

// SubscriptionIdNotIn is an autogenerated method
// nolint: dupl
func (qs CustomReportSubscriptionQuerySet) SubscriptionIdNotIn(subscriptionId ...int) CustomReportSubscriptionQuerySet {
	if len(subscriptionId) == 0 {
		qs.db.AddError(errors.New("must at least pass one subscriptionId in SubscriptionIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("subscription_id NOT IN (?)", subscriptionId))
}

// SetBkBizId is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) SetBkBizId(bkBizId int) CustomReportSubscriptionUpdater {
	u.fields[string(CustomReportSubscriptionDBSchema.BkBizId)] = bkBizId
	return u
}

// SetBkDataID is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) SetBkDataID(bkDataID uint) CustomReportSubscriptionUpdater {
	u.fields[string(CustomReportSubscriptionDBSchema.BkDataID)] = bkDataID
	return u
}

// SetConfig is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) SetConfig(config string) CustomReportSubscriptionUpdater {
	u.fields[string(CustomReportSubscriptionDBSchema.Config)] = config
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) SetID(ID uint) CustomReportSubscriptionUpdater {
	u.fields[string(CustomReportSubscriptionDBSchema.ID)] = ID
	return u
}

// SetSubscriptionId is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) SetSubscriptionId(subscriptionId int) CustomReportSubscriptionUpdater {
	u.fields[string(CustomReportSubscriptionDBSchema.SubscriptionId)] = subscriptionId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u CustomReportSubscriptionUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set CustomReportSubscriptionQuerySet

// ===== BEGIN of CustomReportSubscription modifiers

// CustomReportSubscriptionDBSchemaField describes database schema field. It requires for method 'Update'
type CustomReportSubscriptionDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f CustomReportSubscriptionDBSchemaField) String() string {
	return string(f)
}

// CustomReportSubscriptionDBSchema stores db field names of CustomReportSubscription
var CustomReportSubscriptionDBSchema = struct {
	ID             CustomReportSubscriptionDBSchemaField
	BkBizId        CustomReportSubscriptionDBSchemaField
	SubscriptionId CustomReportSubscriptionDBSchemaField
	BkDataID       CustomReportSubscriptionDBSchemaField
	Config         CustomReportSubscriptionDBSchemaField
}{

	ID:             CustomReportSubscriptionDBSchemaField("id"),
	BkBizId:        CustomReportSubscriptionDBSchemaField("bk_biz_id"),
	SubscriptionId: CustomReportSubscriptionDBSchemaField("subscription_id"),
	BkDataID:       CustomReportSubscriptionDBSchemaField("bk_data_id"),
	Config:         CustomReportSubscriptionDBSchemaField("config"),
}

// Update updates CustomReportSubscription fields by primary key
// nolint: dupl
func (o *CustomReportSubscription) Update(db *gorm.DB, fields ...CustomReportSubscriptionDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":              o.ID,
		"bk_biz_id":       o.BkBizId,
		"subscription_id": o.SubscriptionId,
		"bk_data_id":      o.BkDataID,
		"config":          o.Config,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update CustomReportSubscription %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// CustomReportSubscriptionUpdater is an CustomReportSubscription updates manager
type CustomReportSubscriptionUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewCustomReportSubscriptionUpdater creates new CustomReportSubscription updater
// nolint: dupl
func NewCustomReportSubscriptionUpdater(db *gorm.DB) CustomReportSubscriptionUpdater {
	return CustomReportSubscriptionUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&CustomReportSubscription{}),
	}
}

// ===== END of CustomReportSubscription modifiers

// ===== END of all query sets
