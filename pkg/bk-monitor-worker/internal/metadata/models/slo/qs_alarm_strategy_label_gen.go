// Code generated by go-queryset. DO NOT EDIT.
package slo

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set AlarmStrategyLabelQuerySet

// AlarmStrategyLabelQuerySet is an queryset type for AlarmStrategyLabel
type AlarmStrategyLabelQuerySet struct {
	db *gorm.DB
}

// NewAlarmStrategyLabelQuerySet constructs new AlarmStrategyLabelQuerySet
func NewAlarmStrategyLabelQuerySet(db *gorm.DB) AlarmStrategyLabelQuerySet {
	return AlarmStrategyLabelQuerySet{
		db: db.Model(&AlarmStrategyLabel{}),
	}
}

func (qs AlarmStrategyLabelQuerySet) w(db *gorm.DB) AlarmStrategyLabelQuerySet {
	return NewAlarmStrategyLabelQuerySet(db)
}

func (qs AlarmStrategyLabelQuerySet) Select(fields ...AlarmStrategyLabelDBSchemaField) AlarmStrategyLabelQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *AlarmStrategyLabel) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *AlarmStrategyLabel) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) All(ret *[]AlarmStrategyLabel) error {
	return qs.db.Find(ret).Error
}

// BkBizIDEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDEq(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizID))
}

// BkBizIDGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDGt(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizID))
}

// BkBizIDGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDGte(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizID))
}

// BkBizIDIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDIn(bkBizID ...int32) AlarmStrategyLabelQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizID))
}

// BkBizIDLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDLt(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizID))
}

// BkBizIDLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDLte(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizID))
}

// BkBizIDNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDNe(bkBizID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizID))
}

// BkBizIDNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) BkBizIDNotIn(bkBizID ...int32) AlarmStrategyLabelQuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// Delete is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) Delete() error {
	return qs.db.Delete(AlarmStrategyLabel{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(AlarmStrategyLabel{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(AlarmStrategyLabel{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) GetUpdater() AlarmStrategyLabelUpdater {
	return NewAlarmStrategyLabelUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDEq(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDGt(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDGte(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDIn(ID ...int32) AlarmStrategyLabelQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDLt(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDLte(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDNe(ID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) IDNotIn(ID ...int32) AlarmStrategyLabelQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// LabelNameEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameEq(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name = ?", labelName))
}

// LabelNameGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameGt(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name > ?", labelName))
}

// LabelNameGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameGte(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name >= ?", labelName))
}

// LabelNameIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameIn(labelName ...string) AlarmStrategyLabelQuerySet {
	if len(labelName) == 0 {
		qs.db.AddError(errors.New("must at least pass one labelName in LabelNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label_name IN (?)", labelName))
}

// LabelNameLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameLike(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name LIKE ?", labelName))
}

// LabelNameLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameLt(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name < ?", labelName))
}

// LabelNameLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameLte(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name <= ?", labelName))
}

// LabelNameNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameNe(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name != ?", labelName))
}

// LabelNameNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameNotIn(labelName ...string) AlarmStrategyLabelQuerySet {
	if len(labelName) == 0 {
		qs.db.AddError(errors.New("must at least pass one labelName in LabelNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("label_name NOT IN (?)", labelName))
}

// LabelNameNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) LabelNameNotlike(labelName string) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("label_name NOT LIKE ?", labelName))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) Limit(limit int) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) Offset(offset int) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs AlarmStrategyLabelQuerySet) One(ret *AlarmStrategyLabel) error {
	return qs.db.First(ret).Error
}

// OrderAscByBkBizID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderAscByBkBizID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderAscByID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByLabelName is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderAscByLabelName() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("label_name ASC"))
}

// OrderAscByStrategyID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderAscByStrategyID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("strategy_id ASC"))
}

// OrderDescByBkBizID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderDescByBkBizID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderDescByID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByLabelName is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderDescByLabelName() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("label_name DESC"))
}

// OrderDescByStrategyID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) OrderDescByStrategyID() AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Order("strategy_id DESC"))
}

// StrategyIDEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDEq(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id = ?", strategyID))
}

// StrategyIDGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDGt(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id > ?", strategyID))
}

// StrategyIDGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDGte(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id >= ?", strategyID))
}

// StrategyIDIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDIn(strategyID ...int32) AlarmStrategyLabelQuerySet {
	if len(strategyID) == 0 {
		qs.db.AddError(errors.New("must at least pass one strategyID in StrategyIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("strategy_id IN (?)", strategyID))
}

// StrategyIDLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDLt(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id < ?", strategyID))
}

// StrategyIDLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDLte(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id <= ?", strategyID))
}

// StrategyIDNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDNe(strategyID int32) AlarmStrategyLabelQuerySet {
	return qs.w(qs.db.Where("strategy_id != ?", strategyID))
}

// StrategyIDNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyLabelQuerySet) StrategyIDNotIn(strategyID ...int32) AlarmStrategyLabelQuerySet {
	if len(strategyID) == 0 {
		qs.db.AddError(errors.New("must at least pass one strategyID in StrategyIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("strategy_id NOT IN (?)", strategyID))
}

// SetBkBizID is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) SetBkBizID(bkBizID int32) AlarmStrategyLabelUpdater {
	u.fields[string(AlarmStrategyLabelDBSchema.BkBizID)] = bkBizID
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) SetID(ID int32) AlarmStrategyLabelUpdater {
	u.fields[string(AlarmStrategyLabelDBSchema.ID)] = ID
	return u
}

// SetLabelName is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) SetLabelName(labelName string) AlarmStrategyLabelUpdater {
	u.fields[string(AlarmStrategyLabelDBSchema.LabelName)] = labelName
	return u
}

// SetStrategyID is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) SetStrategyID(strategyID int32) AlarmStrategyLabelUpdater {
	u.fields[string(AlarmStrategyLabelDBSchema.StrategyID)] = strategyID
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u AlarmStrategyLabelUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set AlarmStrategyLabelQuerySet

// ===== BEGIN of AlarmStrategyLabel modifiers

// AlarmStrategyLabelDBSchemaField describes database schema field. It requires for method 'Update'
type AlarmStrategyLabelDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f AlarmStrategyLabelDBSchemaField) String() string {
	return string(f)
}

// AlarmStrategyLabelDBSchema stores db field names of AlarmStrategyLabel
var AlarmStrategyLabelDBSchema = struct {
	ID         AlarmStrategyLabelDBSchemaField
	LabelName  AlarmStrategyLabelDBSchemaField
	BkBizID    AlarmStrategyLabelDBSchemaField
	StrategyID AlarmStrategyLabelDBSchemaField
}{

	ID:         AlarmStrategyLabelDBSchemaField("id"),
	LabelName:  AlarmStrategyLabelDBSchemaField("label_name"),
	BkBizID:    AlarmStrategyLabelDBSchemaField("bk_biz_id"),
	StrategyID: AlarmStrategyLabelDBSchemaField("strategy_id"),
}

// Update updates AlarmStrategyLabel fields by primary key
// nolint: dupl
func (o *AlarmStrategyLabel) Update(db *gorm.DB, fields ...AlarmStrategyLabelDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":          o.ID,
		"label_name":  o.LabelName,
		"bk_biz_id":   o.BkBizID,
		"strategy_id": o.StrategyID,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update AlarmStrategyLabel %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// AlarmStrategyLabelUpdater is an AlarmStrategyLabel updates manager
type AlarmStrategyLabelUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewAlarmStrategyLabelUpdater creates new AlarmStrategyLabel updater
// nolint: dupl
func NewAlarmStrategyLabelUpdater(db *gorm.DB) AlarmStrategyLabelUpdater {
	return AlarmStrategyLabelUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&AlarmStrategyLabel{}),
	}
}

// ===== END of AlarmStrategyLabel modifiers

// ===== END of all query sets
