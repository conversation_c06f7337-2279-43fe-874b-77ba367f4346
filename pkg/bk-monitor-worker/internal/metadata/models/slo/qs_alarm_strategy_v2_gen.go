// Code generated by go-queryset. DO NOT EDIT.
package slo

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set AlarmStrategyV2QuerySet

// AlarmStrategyV2QuerySet is an queryset type for AlarmStrategyV2
type AlarmStrategyV2QuerySet struct {
	db *gorm.DB
}

// NewAlarmStrategyV2QuerySet constructs new AlarmStrategyV2QuerySet
func NewAlarmStrategyV2QuerySet(db *gorm.DB) AlarmStrategyV2QuerySet {
	return AlarmStrategyV2QuerySet{
		db: db.Model(&AlarmStrategyV2{}),
	}
}

func (qs AlarmStrategyV2QuerySet) w(db *gorm.DB) AlarmStrategyV2QuerySet {
	return NewAlarmStrategyV2QuerySet(db)
}

func (qs AlarmStrategyV2QuerySet) Select(fields ...AlarmStrategyV2DBSchemaField) AlarmStrategyV2QuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *AlarmStrategyV2) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *AlarmStrategyV2) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) All(ret *[]AlarmStrategyV2) error {
	return qs.db.Find(ret).Error
}

// AppEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppEq(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app = ?", app))
}

// AppGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppGt(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app > ?", app))
}

// AppGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppGte(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app >= ?", app))
}

// AppIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppIn(app ...string) AlarmStrategyV2QuerySet {
	if len(app) == 0 {
		qs.db.AddError(errors.New("must at least pass one app in AppIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("app IN (?)", app))
}

// AppLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppLike(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app LIKE ?", app))
}

// AppLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppLt(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app < ?", app))
}

// AppLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppLte(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app <= ?", app))
}

// AppNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppNe(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app != ?", app))
}

// AppNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppNotIn(app ...string) AlarmStrategyV2QuerySet {
	if len(app) == 0 {
		qs.db.AddError(errors.New("must at least pass one app in AppNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("app NOT IN (?)", app))
}

// AppNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) AppNotlike(app string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("app NOT LIKE ?", app))
}

// BkBizIDEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDEq(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id = ?", bkBizID))
}

// BkBizIDGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDGt(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id > ?", bkBizID))
}

// BkBizIDGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDGte(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id >= ?", bkBizID))
}

// BkBizIDIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDIn(bkBizID ...int32) AlarmStrategyV2QuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id IN (?)", bkBizID))
}

// BkBizIDLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDLt(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id < ?", bkBizID))
}

// BkBizIDLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDLte(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id <= ?", bkBizID))
}

// BkBizIDNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDNe(bkBizID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("bk_biz_id != ?", bkBizID))
}

// BkBizIDNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) BkBizIDNotIn(bkBizID ...int32) AlarmStrategyV2QuerySet {
	if len(bkBizID) == 0 {
		qs.db.AddError(errors.New("must at least pass one bkBizID in BkBizIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("bk_biz_id NOT IN (?)", bkBizID))
}

// Count is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeEq(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeGt(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeGte(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeLt(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeLte(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateTimeNe(createTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreateUserEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserEq(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user = ?", createUser))
}

// CreateUserGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserGt(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user > ?", createUser))
}

// CreateUserGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserGte(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user >= ?", createUser))
}

// CreateUserIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserIn(createUser ...string) AlarmStrategyV2QuerySet {
	if len(createUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one createUser in CreateUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("create_user IN (?)", createUser))
}

// CreateUserLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserLike(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user LIKE ?", createUser))
}

// CreateUserLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserLt(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user < ?", createUser))
}

// CreateUserLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserLte(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user <= ?", createUser))
}

// CreateUserNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserNe(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user != ?", createUser))
}

// CreateUserNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserNotIn(createUser ...string) AlarmStrategyV2QuerySet {
	if len(createUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one createUser in CreateUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("create_user NOT IN (?)", createUser))
}

// CreateUserNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) CreateUserNotlike(createUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("create_user NOT LIKE ?", createUser))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) Delete() error {
	return qs.db.Delete(AlarmStrategyV2{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(AlarmStrategyV2{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(AlarmStrategyV2{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) GetUpdater() AlarmStrategyV2Updater {
	return NewAlarmStrategyV2Updater(qs.db)
}

// HashEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashEq(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash = ?", hash))
}

// HashGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashGt(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash > ?", hash))
}

// HashGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashGte(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash >= ?", hash))
}

// HashIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashIn(hash ...string) AlarmStrategyV2QuerySet {
	if len(hash) == 0 {
		qs.db.AddError(errors.New("must at least pass one hash in HashIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("hash IN (?)", hash))
}

// HashLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashLike(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash LIKE ?", hash))
}

// HashLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashLt(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash < ?", hash))
}

// HashLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashLte(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash <= ?", hash))
}

// HashNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashNe(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash != ?", hash))
}

// HashNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashNotIn(hash ...string) AlarmStrategyV2QuerySet {
	if len(hash) == 0 {
		qs.db.AddError(errors.New("must at least pass one hash in HashNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("hash NOT IN (?)", hash))
}

// HashNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) HashNotlike(hash string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("hash NOT LIKE ?", hash))
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDEq(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDGt(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDGte(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDIn(ID ...int32) AlarmStrategyV2QuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDLt(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDLte(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDNe(ID int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IDNotIn(ID ...int32) AlarmStrategyV2QuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// InvalidTypeEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeEq(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type = ?", invalidType))
}

// InvalidTypeGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeGt(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type > ?", invalidType))
}

// InvalidTypeGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeGte(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type >= ?", invalidType))
}

// InvalidTypeIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeIn(invalidType ...string) AlarmStrategyV2QuerySet {
	if len(invalidType) == 0 {
		qs.db.AddError(errors.New("must at least pass one invalidType in InvalidTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("invalid_type IN (?)", invalidType))
}

// InvalidTypeLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeLike(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type LIKE ?", invalidType))
}

// InvalidTypeLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeLt(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type < ?", invalidType))
}

// InvalidTypeLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeLte(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type <= ?", invalidType))
}

// InvalidTypeNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeNe(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type != ?", invalidType))
}

// InvalidTypeNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeNotIn(invalidType ...string) AlarmStrategyV2QuerySet {
	if len(invalidType) == 0 {
		qs.db.AddError(errors.New("must at least pass one invalidType in InvalidTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("invalid_type NOT IN (?)", invalidType))
}

// InvalidTypeNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) InvalidTypeNotlike(invalidType string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("invalid_type NOT LIKE ?", invalidType))
}

// IsEnabledEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsEnabledEq(isEnabled bool) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("is_enabled = ?", isEnabled))
}

// IsEnabledIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsEnabledIn(isEnabled ...bool) AlarmStrategyV2QuerySet {
	if len(isEnabled) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnabled in IsEnabledIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enabled IN (?)", isEnabled))
}

// IsEnabledNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsEnabledNe(isEnabled bool) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("is_enabled != ?", isEnabled))
}

// IsEnabledNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsEnabledNotIn(isEnabled ...bool) AlarmStrategyV2QuerySet {
	if len(isEnabled) == 0 {
		qs.db.AddError(errors.New("must at least pass one isEnabled in IsEnabledNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_enabled NOT IN (?)", isEnabled))
}

// IsInvalidEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsInvalidEq(isInvalid bool) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("is_invalid = ?", isInvalid))
}

// IsInvalidIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsInvalidIn(isInvalid ...bool) AlarmStrategyV2QuerySet {
	if len(isInvalid) == 0 {
		qs.db.AddError(errors.New("must at least pass one isInvalid in IsInvalidIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_invalid IN (?)", isInvalid))
}

// IsInvalidNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsInvalidNe(isInvalid bool) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("is_invalid != ?", isInvalid))
}

// IsInvalidNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) IsInvalidNotIn(isInvalid ...bool) AlarmStrategyV2QuerySet {
	if len(isInvalid) == 0 {
		qs.db.AddError(errors.New("must at least pass one isInvalid in IsInvalidNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_invalid NOT IN (?)", isInvalid))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) Limit(limit int) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Limit(limit))
}

// NameEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameEq(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name = ?", name))
}

// NameGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameGt(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name > ?", name))
}

// NameGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameGte(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name >= ?", name))
}

// NameIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameIn(name ...string) AlarmStrategyV2QuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name IN (?)", name))
}

// NameLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameLike(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name LIKE ?", name))
}

// NameLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameLt(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name < ?", name))
}

// NameLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameLte(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name <= ?", name))
}

// NameNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameNe(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name != ?", name))
}

// NameNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameNotIn(name ...string) AlarmStrategyV2QuerySet {
	if len(name) == 0 {
		qs.db.AddError(errors.New("must at least pass one name in NameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("name NOT IN (?)", name))
}

// NameNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) NameNotlike(name string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("name NOT LIKE ?", name))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) Offset(offset int) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs AlarmStrategyV2QuerySet) One(ret *AlarmStrategyV2) error {
	return qs.db.First(ret).Error
}

// OrderAscByApp is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByApp() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("app ASC"))
}

// OrderAscByBkBizID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByBkBizID() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("bk_biz_id ASC"))
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByCreateTime() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreateUser is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByCreateUser() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("create_user ASC"))
}

// OrderAscByHash is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByHash() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("hash ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByID() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByInvalidType is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByInvalidType() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("invalid_type ASC"))
}

// OrderAscByIsEnabled is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByIsEnabled() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("is_enabled ASC"))
}

// OrderAscByIsInvalid is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByIsInvalid() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("is_invalid ASC"))
}

// OrderAscByName is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByName() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("name ASC"))
}

// OrderAscByPath is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByPath() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("path ASC"))
}

// OrderAscByPriority is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByPriority() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("priority ASC"))
}

// OrderAscByPriorityGroupKey is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByPriorityGroupKey() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("priority_group_key ASC"))
}

// OrderAscByScenario is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByScenario() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("scenario ASC"))
}

// OrderAscBySnippet is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscBySnippet() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("snippet ASC"))
}

// OrderAscBySource is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscBySource() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("source ASC"))
}

// OrderAscByType is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByType() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("type ASC"))
}

// OrderAscByUpdateTime is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByUpdateTime() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("update_time ASC"))
}

// OrderAscByUpdateUser is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderAscByUpdateUser() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("update_user ASC"))
}

// OrderDescByApp is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByApp() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("app DESC"))
}

// OrderDescByBkBizID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByBkBizID() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("bk_biz_id DESC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByCreateTime() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreateUser is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByCreateUser() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("create_user DESC"))
}

// OrderDescByHash is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByHash() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("hash DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByID() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByInvalidType is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByInvalidType() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("invalid_type DESC"))
}

// OrderDescByIsEnabled is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByIsEnabled() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("is_enabled DESC"))
}

// OrderDescByIsInvalid is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByIsInvalid() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("is_invalid DESC"))
}

// OrderDescByName is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByName() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("name DESC"))
}

// OrderDescByPath is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByPath() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("path DESC"))
}

// OrderDescByPriority is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByPriority() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("priority DESC"))
}

// OrderDescByPriorityGroupKey is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByPriorityGroupKey() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("priority_group_key DESC"))
}

// OrderDescByScenario is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByScenario() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("scenario DESC"))
}

// OrderDescBySnippet is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescBySnippet() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("snippet DESC"))
}

// OrderDescBySource is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescBySource() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("source DESC"))
}

// OrderDescByType is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByType() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("type DESC"))
}

// OrderDescByUpdateTime is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByUpdateTime() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("update_time DESC"))
}

// OrderDescByUpdateUser is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) OrderDescByUpdateUser() AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Order("update_user DESC"))
}

// PathEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathEq(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path = ?", path))
}

// PathGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathGt(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path > ?", path))
}

// PathGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathGte(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path >= ?", path))
}

// PathIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathIn(path ...string) AlarmStrategyV2QuerySet {
	if len(path) == 0 {
		qs.db.AddError(errors.New("must at least pass one path in PathIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("path IN (?)", path))
}

// PathLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathLike(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path LIKE ?", path))
}

// PathLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathLt(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path < ?", path))
}

// PathLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathLte(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path <= ?", path))
}

// PathNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathNe(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path != ?", path))
}

// PathNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathNotIn(path ...string) AlarmStrategyV2QuerySet {
	if len(path) == 0 {
		qs.db.AddError(errors.New("must at least pass one path in PathNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("path NOT IN (?)", path))
}

// PathNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PathNotlike(path string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("path NOT LIKE ?", path))
}

// PriorityEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityEq(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority = ?", priority))
}

// PriorityGroupKeyEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyEq(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key = ?", priorityGroupKey))
}

// PriorityGroupKeyGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyGt(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key > ?", priorityGroupKey))
}

// PriorityGroupKeyGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyGte(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key >= ?", priorityGroupKey))
}

// PriorityGroupKeyIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyIn(priorityGroupKey ...string) AlarmStrategyV2QuerySet {
	if len(priorityGroupKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one priorityGroupKey in PriorityGroupKeyIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("priority_group_key IN (?)", priorityGroupKey))
}

// PriorityGroupKeyLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyLike(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key LIKE ?", priorityGroupKey))
}

// PriorityGroupKeyLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyLt(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key < ?", priorityGroupKey))
}

// PriorityGroupKeyLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyLte(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key <= ?", priorityGroupKey))
}

// PriorityGroupKeyNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyNe(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key != ?", priorityGroupKey))
}

// PriorityGroupKeyNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyNotIn(priorityGroupKey ...string) AlarmStrategyV2QuerySet {
	if len(priorityGroupKey) == 0 {
		qs.db.AddError(errors.New("must at least pass one priorityGroupKey in PriorityGroupKeyNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("priority_group_key NOT IN (?)", priorityGroupKey))
}

// PriorityGroupKeyNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGroupKeyNotlike(priorityGroupKey string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority_group_key NOT LIKE ?", priorityGroupKey))
}

// PriorityGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGt(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority > ?", priority))
}

// PriorityGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityGte(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority >= ?", priority))
}

// PriorityIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityIn(priority ...int32) AlarmStrategyV2QuerySet {
	if len(priority) == 0 {
		qs.db.AddError(errors.New("must at least pass one priority in PriorityIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("priority IN (?)", priority))
}

// PriorityLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityLt(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority < ?", priority))
}

// PriorityLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityLte(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority <= ?", priority))
}

// PriorityNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityNe(priority int32) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("priority != ?", priority))
}

// PriorityNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) PriorityNotIn(priority ...int32) AlarmStrategyV2QuerySet {
	if len(priority) == 0 {
		qs.db.AddError(errors.New("must at least pass one priority in PriorityNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("priority NOT IN (?)", priority))
}

// ScenarioEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioEq(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario = ?", scenario))
}

// ScenarioGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioGt(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario > ?", scenario))
}

// ScenarioGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioGte(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario >= ?", scenario))
}

// ScenarioIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioIn(scenario ...string) AlarmStrategyV2QuerySet {
	if len(scenario) == 0 {
		qs.db.AddError(errors.New("must at least pass one scenario in ScenarioIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("scenario IN (?)", scenario))
}

// ScenarioLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioLike(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario LIKE ?", scenario))
}

// ScenarioLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioLt(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario < ?", scenario))
}

// ScenarioLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioLte(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario <= ?", scenario))
}

// ScenarioNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioNe(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario != ?", scenario))
}

// ScenarioNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioNotIn(scenario ...string) AlarmStrategyV2QuerySet {
	if len(scenario) == 0 {
		qs.db.AddError(errors.New("must at least pass one scenario in ScenarioNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("scenario NOT IN (?)", scenario))
}

// ScenarioNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) ScenarioNotlike(scenario string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("scenario NOT LIKE ?", scenario))
}

// SnippetEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetEq(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet = ?", snippet))
}

// SnippetGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetGt(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet > ?", snippet))
}

// SnippetGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetGte(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet >= ?", snippet))
}

// SnippetIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetIn(snippet ...string) AlarmStrategyV2QuerySet {
	if len(snippet) == 0 {
		qs.db.AddError(errors.New("must at least pass one snippet in SnippetIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snippet IN (?)", snippet))
}

// SnippetLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetLike(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet LIKE ?", snippet))
}

// SnippetLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetLt(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet < ?", snippet))
}

// SnippetLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetLte(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet <= ?", snippet))
}

// SnippetNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetNe(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet != ?", snippet))
}

// SnippetNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetNotIn(snippet ...string) AlarmStrategyV2QuerySet {
	if len(snippet) == 0 {
		qs.db.AddError(errors.New("must at least pass one snippet in SnippetNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("snippet NOT IN (?)", snippet))
}

// SnippetNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SnippetNotlike(snippet string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("snippet NOT LIKE ?", snippet))
}

// SourceEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceEq(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source = ?", source))
}

// SourceGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceGt(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source > ?", source))
}

// SourceGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceGte(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source >= ?", source))
}

// SourceIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceIn(source ...string) AlarmStrategyV2QuerySet {
	if len(source) == 0 {
		qs.db.AddError(errors.New("must at least pass one source in SourceIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source IN (?)", source))
}

// SourceLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceLike(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source LIKE ?", source))
}

// SourceLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceLt(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source < ?", source))
}

// SourceLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceLte(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source <= ?", source))
}

// SourceNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceNe(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source != ?", source))
}

// SourceNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceNotIn(source ...string) AlarmStrategyV2QuerySet {
	if len(source) == 0 {
		qs.db.AddError(errors.New("must at least pass one source in SourceNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("source NOT IN (?)", source))
}

// SourceNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) SourceNotlike(source string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("source NOT LIKE ?", source))
}

// TypeEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeEq(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type = ?", typeValue))
}

// TypeGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeGt(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type > ?", typeValue))
}

// TypeGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeGte(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type >= ?", typeValue))
}

// TypeIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeIn(typeValue ...string) AlarmStrategyV2QuerySet {
	if len(typeValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeValue in TypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type IN (?)", typeValue))
}

// TypeLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeLike(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type LIKE ?", typeValue))
}

// TypeLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeLt(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type < ?", typeValue))
}

// TypeLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeLte(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type <= ?", typeValue))
}

// TypeNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeNe(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type != ?", typeValue))
}

// TypeNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeNotIn(typeValue ...string) AlarmStrategyV2QuerySet {
	if len(typeValue) == 0 {
		qs.db.AddError(errors.New("must at least pass one typeValue in TypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("type NOT IN (?)", typeValue))
}

// TypeNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) TypeNotlike(typeValue string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("type NOT LIKE ?", typeValue))
}

// UpdateTimeEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeEq(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time = ?", updateTime))
}

// UpdateTimeGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeGt(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time > ?", updateTime))
}

// UpdateTimeGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeGte(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time >= ?", updateTime))
}

// UpdateTimeLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeLt(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time < ?", updateTime))
}

// UpdateTimeLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeLte(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time <= ?", updateTime))
}

// UpdateTimeNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateTimeNe(updateTime time.Time) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_time != ?", updateTime))
}

// UpdateUserEq is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserEq(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user = ?", updateUser))
}

// UpdateUserGt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserGt(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user > ?", updateUser))
}

// UpdateUserGte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserGte(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user >= ?", updateUser))
}

// UpdateUserIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserIn(updateUser ...string) AlarmStrategyV2QuerySet {
	if len(updateUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one updateUser in UpdateUserIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("update_user IN (?)", updateUser))
}

// UpdateUserLike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserLike(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user LIKE ?", updateUser))
}

// UpdateUserLt is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserLt(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user < ?", updateUser))
}

// UpdateUserLte is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserLte(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user <= ?", updateUser))
}

// UpdateUserNe is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserNe(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user != ?", updateUser))
}

// UpdateUserNotIn is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserNotIn(updateUser ...string) AlarmStrategyV2QuerySet {
	if len(updateUser) == 0 {
		qs.db.AddError(errors.New("must at least pass one updateUser in UpdateUserNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("update_user NOT IN (?)", updateUser))
}

// UpdateUserNotlike is an autogenerated method
// nolint: dupl
func (qs AlarmStrategyV2QuerySet) UpdateUserNotlike(updateUser string) AlarmStrategyV2QuerySet {
	return qs.w(qs.db.Where("update_user NOT LIKE ?", updateUser))
}

// SetApp is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetApp(app string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.App)] = app
	return u
}

// SetBkBizID is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetBkBizID(bkBizID int32) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.BkBizID)] = bkBizID
	return u
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetCreateTime(createTime time.Time) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.CreateTime)] = createTime
	return u
}

// SetCreateUser is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetCreateUser(createUser string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.CreateUser)] = createUser
	return u
}

// SetHash is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetHash(hash string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Hash)] = hash
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetID(ID int32) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.ID)] = ID
	return u
}

// SetInvalidType is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetInvalidType(invalidType string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.InvalidType)] = invalidType
	return u
}

// SetIsEnabled is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetIsEnabled(isEnabled bool) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.IsEnabled)] = isEnabled
	return u
}

// SetIsInvalid is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetIsInvalid(isInvalid bool) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.IsInvalid)] = isInvalid
	return u
}

// SetName is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetName(name string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Name)] = name
	return u
}

// SetPath is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetPath(path string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Path)] = path
	return u
}

// SetPriority is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetPriority(priority int32) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Priority)] = priority
	return u
}

// SetPriorityGroupKey is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetPriorityGroupKey(priorityGroupKey string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.PriorityGroupKey)] = priorityGroupKey
	return u
}

// SetScenario is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetScenario(scenario string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Scenario)] = scenario
	return u
}

// SetSnippet is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetSnippet(snippet string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Snippet)] = snippet
	return u
}

// SetSource is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetSource(source string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Source)] = source
	return u
}

// SetType is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetType(typeValue string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.Type)] = typeValue
	return u
}

// SetUpdateTime is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetUpdateTime(updateTime time.Time) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.UpdateTime)] = updateTime
	return u
}

// SetUpdateUser is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) SetUpdateUser(updateUser string) AlarmStrategyV2Updater {
	u.fields[string(AlarmStrategyV2DBSchema.UpdateUser)] = updateUser
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u AlarmStrategyV2Updater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set AlarmStrategyV2QuerySet

// ===== BEGIN of AlarmStrategyV2 modifiers

// AlarmStrategyV2DBSchemaField describes database schema field. It requires for method 'Update'
type AlarmStrategyV2DBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f AlarmStrategyV2DBSchemaField) String() string {
	return string(f)
}

// AlarmStrategyV2DBSchema stores db field names of AlarmStrategyV2
var AlarmStrategyV2DBSchema = struct {
	ID               AlarmStrategyV2DBSchemaField
	Name             AlarmStrategyV2DBSchemaField
	BkBizID          AlarmStrategyV2DBSchemaField
	Source           AlarmStrategyV2DBSchemaField
	Scenario         AlarmStrategyV2DBSchemaField
	Type             AlarmStrategyV2DBSchemaField
	IsEnabled        AlarmStrategyV2DBSchemaField
	CreateUser       AlarmStrategyV2DBSchemaField
	CreateTime       AlarmStrategyV2DBSchemaField
	UpdateUser       AlarmStrategyV2DBSchemaField
	UpdateTime       AlarmStrategyV2DBSchemaField
	IsInvalid        AlarmStrategyV2DBSchemaField
	InvalidType      AlarmStrategyV2DBSchemaField
	App              AlarmStrategyV2DBSchemaField
	Hash             AlarmStrategyV2DBSchemaField
	Path             AlarmStrategyV2DBSchemaField
	Snippet          AlarmStrategyV2DBSchemaField
	Priority         AlarmStrategyV2DBSchemaField
	PriorityGroupKey AlarmStrategyV2DBSchemaField
}{

	ID:               AlarmStrategyV2DBSchemaField("id"),
	Name:             AlarmStrategyV2DBSchemaField("name"),
	BkBizID:          AlarmStrategyV2DBSchemaField("bk_biz_id"),
	Source:           AlarmStrategyV2DBSchemaField("source"),
	Scenario:         AlarmStrategyV2DBSchemaField("scenario"),
	Type:             AlarmStrategyV2DBSchemaField("type"),
	IsEnabled:        AlarmStrategyV2DBSchemaField("is_enabled"),
	CreateUser:       AlarmStrategyV2DBSchemaField("create_user"),
	CreateTime:       AlarmStrategyV2DBSchemaField("create_time"),
	UpdateUser:       AlarmStrategyV2DBSchemaField("update_user"),
	UpdateTime:       AlarmStrategyV2DBSchemaField("update_time"),
	IsInvalid:        AlarmStrategyV2DBSchemaField("is_invalid"),
	InvalidType:      AlarmStrategyV2DBSchemaField("invalid_type"),
	App:              AlarmStrategyV2DBSchemaField("app"),
	Hash:             AlarmStrategyV2DBSchemaField("hash"),
	Path:             AlarmStrategyV2DBSchemaField("path"),
	Snippet:          AlarmStrategyV2DBSchemaField("snippet"),
	Priority:         AlarmStrategyV2DBSchemaField("priority"),
	PriorityGroupKey: AlarmStrategyV2DBSchemaField("priority_group_key"),
}

// Update updates AlarmStrategyV2 fields by primary key
// nolint: dupl
func (o *AlarmStrategyV2) Update(db *gorm.DB, fields ...AlarmStrategyV2DBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                 o.ID,
		"name":               o.Name,
		"bk_biz_id":          o.BkBizID,
		"source":             o.Source,
		"scenario":           o.Scenario,
		"type":               o.Type,
		"is_enabled":         o.IsEnabled,
		"create_user":        o.CreateUser,
		"create_time":        o.CreateTime,
		"update_user":        o.UpdateUser,
		"update_time":        o.UpdateTime,
		"is_invalid":         o.IsInvalid,
		"invalid_type":       o.InvalidType,
		"app":                o.App,
		"hash":               o.Hash,
		"path":               o.Path,
		"snippet":            o.Snippet,
		"priority":           o.Priority,
		"priority_group_key": o.PriorityGroupKey,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update AlarmStrategyV2 %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// AlarmStrategyV2Updater is an AlarmStrategyV2 updates manager
type AlarmStrategyV2Updater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewAlarmStrategyV2Updater creates new AlarmStrategyV2 updater
// nolint: dupl
func NewAlarmStrategyV2Updater(db *gorm.DB) AlarmStrategyV2Updater {
	return AlarmStrategyV2Updater{
		fields: map[string]interface{}{},
		db:     db.Model(&AlarmStrategyV2{}),
	}
}

// ===== END of AlarmStrategyV2 modifiers

// ===== END of all query sets
