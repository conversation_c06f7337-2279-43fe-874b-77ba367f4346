// Code generated by go-queryset. DO NOT EDIT.
package space

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set SpaceQuerySet

// SpaceQuerySet is an queryset type for Space
type SpaceQuerySet struct {
	db *gorm.DB
}

// NewSpaceQuerySet constructs new SpaceQuerySet
func NewSpaceQuerySet(db *gorm.DB) SpaceQuerySet {
	return SpaceQuerySet{
		db: db.Model(&Space{}),
	}
}

func (qs SpaceQuerySet) w(db *gorm.DB) SpaceQuerySet {
	return NewSpaceQuerySet(db)
}

func (qs SpaceQuerySet) Select(fields ...SpaceDBSchemaField) SpaceQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *Space) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *Space) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) All(ret *[]Space) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeEq(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeGt(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeGte(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeLt(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeLte(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreateTimeNe(createTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorEq(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorGt(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorGte(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorIn(creator ...string) SpaceQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorLike(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorLt(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorLte(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorNe(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorNotIn(creator ...string) SpaceQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) CreatorNotlike(creator string) SpaceQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) Delete() error {
	return qs.db.Delete(Space{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(Space{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(Space{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) GetUpdater() SpaceUpdater {
	return NewSpaceUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdEq(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdGt(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdGte(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdIn(id ...int) SpaceQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdLt(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdLte(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdNe(id int) SpaceQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IdNotIn(id ...int) SpaceQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// IsBcsValidEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IsBcsValidEq(isBcsValid bool) SpaceQuerySet {
	return qs.w(qs.db.Where("is_bcs_valid = ?", isBcsValid))
}

// IsBcsValidIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IsBcsValidIn(isBcsValid ...bool) SpaceQuerySet {
	if len(isBcsValid) == 0 {
		qs.db.AddError(errors.New("must at least pass one isBcsValid in IsBcsValidIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_bcs_valid IN (?)", isBcsValid))
}

// IsBcsValidNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IsBcsValidNe(isBcsValid bool) SpaceQuerySet {
	return qs.w(qs.db.Where("is_bcs_valid != ?", isBcsValid))
}

// IsBcsValidNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) IsBcsValidNotIn(isBcsValid ...bool) SpaceQuerySet {
	if len(isBcsValid) == 0 {
		qs.db.AddError(errors.New("must at least pass one isBcsValid in IsBcsValidNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("is_bcs_valid NOT IN (?)", isBcsValid))
}

// LanguageEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageEq(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language = ?", language))
}

// LanguageGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageGt(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language > ?", language))
}

// LanguageGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageGte(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language >= ?", language))
}

// LanguageIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageIn(language ...string) SpaceQuerySet {
	if len(language) == 0 {
		qs.db.AddError(errors.New("must at least pass one language in LanguageIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("language IN (?)", language))
}

// LanguageLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageLike(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language LIKE ?", language))
}

// LanguageLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageLt(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language < ?", language))
}

// LanguageLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageLte(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language <= ?", language))
}

// LanguageNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageNe(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language != ?", language))
}

// LanguageNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageNotIn(language ...string) SpaceQuerySet {
	if len(language) == 0 {
		qs.db.AddError(errors.New("must at least pass one language in LanguageNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("language NOT IN (?)", language))
}

// LanguageNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) LanguageNotlike(language string) SpaceQuerySet {
	return qs.w(qs.db.Where("language NOT LIKE ?", language))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) Limit(limit int) SpaceQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) Offset(offset int) SpaceQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs SpaceQuerySet) One(ret *Space) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByCreateTime() SpaceQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByCreator() SpaceQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscById() SpaceQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscByIsBcsValid is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByIsBcsValid() SpaceQuerySet {
	return qs.w(qs.db.Order("is_bcs_valid ASC"))
}

// OrderAscByLanguage is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByLanguage() SpaceQuerySet {
	return qs.w(qs.db.Order("language ASC"))
}

// OrderAscBySpaceCode is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscBySpaceCode() SpaceQuerySet {
	return qs.w(qs.db.Order("space_code ASC"))
}

// OrderAscBySpaceId is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscBySpaceId() SpaceQuerySet {
	return qs.w(qs.db.Order("space_id ASC"))
}

// OrderAscBySpaceName is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscBySpaceName() SpaceQuerySet {
	return qs.w(qs.db.Order("space_name ASC"))
}

// OrderAscBySpaceTypeId is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscBySpaceTypeId() SpaceQuerySet {
	return qs.w(qs.db.Order("space_type_id ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByStatus() SpaceQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscByTimeZone is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByTimeZone() SpaceQuerySet {
	return qs.w(qs.db.Order("time_zone ASC"))
}

// OrderAscByUpdateTime is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByUpdateTime() SpaceQuerySet {
	return qs.w(qs.db.Order("update_time ASC"))
}

// OrderAscByUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderAscByUpdater() SpaceQuerySet {
	return qs.w(qs.db.Order("updater ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByCreateTime() SpaceQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByCreator() SpaceQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescById() SpaceQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescByIsBcsValid is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByIsBcsValid() SpaceQuerySet {
	return qs.w(qs.db.Order("is_bcs_valid DESC"))
}

// OrderDescByLanguage is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByLanguage() SpaceQuerySet {
	return qs.w(qs.db.Order("language DESC"))
}

// OrderDescBySpaceCode is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescBySpaceCode() SpaceQuerySet {
	return qs.w(qs.db.Order("space_code DESC"))
}

// OrderDescBySpaceId is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescBySpaceId() SpaceQuerySet {
	return qs.w(qs.db.Order("space_id DESC"))
}

// OrderDescBySpaceName is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescBySpaceName() SpaceQuerySet {
	return qs.w(qs.db.Order("space_name DESC"))
}

// OrderDescBySpaceTypeId is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescBySpaceTypeId() SpaceQuerySet {
	return qs.w(qs.db.Order("space_type_id DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByStatus() SpaceQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescByTimeZone is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByTimeZone() SpaceQuerySet {
	return qs.w(qs.db.Order("time_zone DESC"))
}

// OrderDescByUpdateTime is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByUpdateTime() SpaceQuerySet {
	return qs.w(qs.db.Order("update_time DESC"))
}

// OrderDescByUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) OrderDescByUpdater() SpaceQuerySet {
	return qs.w(qs.db.Order("updater DESC"))
}

// SpaceCodeEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeEq(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code = ?", spaceCode))
}

// SpaceCodeGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeGt(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code > ?", spaceCode))
}

// SpaceCodeGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeGte(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code >= ?", spaceCode))
}

// SpaceCodeIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeIn(spaceCode ...string) SpaceQuerySet {
	if len(spaceCode) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceCode in SpaceCodeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_code IN (?)", spaceCode))
}

// SpaceCodeLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeLike(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code LIKE ?", spaceCode))
}

// SpaceCodeLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeLt(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code < ?", spaceCode))
}

// SpaceCodeLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeLte(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code <= ?", spaceCode))
}

// SpaceCodeNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeNe(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code != ?", spaceCode))
}

// SpaceCodeNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeNotIn(spaceCode ...string) SpaceQuerySet {
	if len(spaceCode) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceCode in SpaceCodeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_code NOT IN (?)", spaceCode))
}

// SpaceCodeNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceCodeNotlike(spaceCode string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_code NOT LIKE ?", spaceCode))
}

// SpaceIdEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdEq(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id = ?", spaceId))
}

// SpaceIdGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdGt(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id > ?", spaceId))
}

// SpaceIdGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdGte(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id >= ?", spaceId))
}

// SpaceIdIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdIn(spaceId ...string) SpaceQuerySet {
	if len(spaceId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceId in SpaceIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id IN (?)", spaceId))
}

// SpaceIdLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdLike(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id LIKE ?", spaceId))
}

// SpaceIdLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdLt(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id < ?", spaceId))
}

// SpaceIdLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdLte(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id <= ?", spaceId))
}

// SpaceIdNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdNe(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id != ?", spaceId))
}

// SpaceIdNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdNotIn(spaceId ...string) SpaceQuerySet {
	if len(spaceId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceId in SpaceIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id NOT IN (?)", spaceId))
}

// SpaceIdNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceIdNotlike(spaceId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_id NOT LIKE ?", spaceId))
}

// SpaceNameEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameEq(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name = ?", spaceName))
}

// SpaceNameGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameGt(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name > ?", spaceName))
}

// SpaceNameGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameGte(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name >= ?", spaceName))
}

// SpaceNameIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameIn(spaceName ...string) SpaceQuerySet {
	if len(spaceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceName in SpaceNameIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_name IN (?)", spaceName))
}

// SpaceNameLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameLike(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name LIKE ?", spaceName))
}

// SpaceNameLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameLt(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name < ?", spaceName))
}

// SpaceNameLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameLte(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name <= ?", spaceName))
}

// SpaceNameNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameNe(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name != ?", spaceName))
}

// SpaceNameNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameNotIn(spaceName ...string) SpaceQuerySet {
	if len(spaceName) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceName in SpaceNameNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_name NOT IN (?)", spaceName))
}

// SpaceNameNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceNameNotlike(spaceName string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_name NOT LIKE ?", spaceName))
}

// SpaceTypeIdEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdEq(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id = ?", spaceTypeId))
}

// SpaceTypeIdGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdGt(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id > ?", spaceTypeId))
}

// SpaceTypeIdGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdGte(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id >= ?", spaceTypeId))
}

// SpaceTypeIdIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdIn(spaceTypeId ...string) SpaceQuerySet {
	if len(spaceTypeId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceTypeId in SpaceTypeIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type_id IN (?)", spaceTypeId))
}

// SpaceTypeIdLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdLike(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id LIKE ?", spaceTypeId))
}

// SpaceTypeIdLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdLt(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id < ?", spaceTypeId))
}

// SpaceTypeIdLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdLte(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id <= ?", spaceTypeId))
}

// SpaceTypeIdNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdNe(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id != ?", spaceTypeId))
}

// SpaceTypeIdNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdNotIn(spaceTypeId ...string) SpaceQuerySet {
	if len(spaceTypeId) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceTypeId in SpaceTypeIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type_id NOT IN (?)", spaceTypeId))
}

// SpaceTypeIdNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) SpaceTypeIdNotlike(spaceTypeId string) SpaceQuerySet {
	return qs.w(qs.db.Where("space_type_id NOT LIKE ?", spaceTypeId))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusEq(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusGt(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status > ?", status))
}

// StatusGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusGte(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status >= ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusIn(status ...string) SpaceQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusLike(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status LIKE ?", status))
}

// StatusLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusLt(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status < ?", status))
}

// StatusLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusLte(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status <= ?", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusNe(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusNotIn(status ...string) SpaceQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// StatusNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) StatusNotlike(status string) SpaceQuerySet {
	return qs.w(qs.db.Where("status NOT LIKE ?", status))
}

// TimeZoneEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneEq(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone = ?", timeZone))
}

// TimeZoneGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneGt(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone > ?", timeZone))
}

// TimeZoneGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneGte(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone >= ?", timeZone))
}

// TimeZoneIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneIn(timeZone ...string) SpaceQuerySet {
	if len(timeZone) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeZone in TimeZoneIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_zone IN (?)", timeZone))
}

// TimeZoneLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneLike(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone LIKE ?", timeZone))
}

// TimeZoneLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneLt(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone < ?", timeZone))
}

// TimeZoneLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneLte(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone <= ?", timeZone))
}

// TimeZoneNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneNe(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone != ?", timeZone))
}

// TimeZoneNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneNotIn(timeZone ...string) SpaceQuerySet {
	if len(timeZone) == 0 {
		qs.db.AddError(errors.New("must at least pass one timeZone in TimeZoneNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("time_zone NOT IN (?)", timeZone))
}

// TimeZoneNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) TimeZoneNotlike(timeZone string) SpaceQuerySet {
	return qs.w(qs.db.Where("time_zone NOT LIKE ?", timeZone))
}

// UpdateTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeEq(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time = ?", updateTime))
}

// UpdateTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeGt(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time > ?", updateTime))
}

// UpdateTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeGte(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time >= ?", updateTime))
}

// UpdateTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeLt(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time < ?", updateTime))
}

// UpdateTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeLte(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time <= ?", updateTime))
}

// UpdateTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdateTimeNe(updateTime time.Time) SpaceQuerySet {
	return qs.w(qs.db.Where("update_time != ?", updateTime))
}

// UpdaterEq is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterEq(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater = ?", updater))
}

// UpdaterGt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterGt(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater > ?", updater))
}

// UpdaterGte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterGte(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater >= ?", updater))
}

// UpdaterIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterIn(updater ...string) SpaceQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater IN (?)", updater))
}

// UpdaterLike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterLike(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater LIKE ?", updater))
}

// UpdaterLt is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterLt(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater < ?", updater))
}

// UpdaterLte is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterLte(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater <= ?", updater))
}

// UpdaterNe is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterNe(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater != ?", updater))
}

// UpdaterNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterNotIn(updater ...string) SpaceQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater NOT IN (?)", updater))
}

// UpdaterNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceQuerySet) UpdaterNotlike(updater string) SpaceQuerySet {
	return qs.w(qs.db.Where("updater NOT LIKE ?", updater))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetCreateTime(createTime time.Time) SpaceUpdater {
	u.fields[string(SpaceDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetCreator(creator string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.Creator)] = creator
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetId(id int) SpaceUpdater {
	u.fields[string(SpaceDBSchema.Id)] = id
	return u
}

// SetIsBcsValid is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetIsBcsValid(isBcsValid bool) SpaceUpdater {
	u.fields[string(SpaceDBSchema.IsBcsValid)] = isBcsValid
	return u
}

// SetLanguage is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetLanguage(language string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.Language)] = language
	return u
}

// SetSpaceCode is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetSpaceCode(spaceCode string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.SpaceCode)] = spaceCode
	return u
}

// SetSpaceId is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetSpaceId(spaceId string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.SpaceId)] = spaceId
	return u
}

// SetSpaceName is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetSpaceName(spaceName string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.SpaceName)] = spaceName
	return u
}

// SetSpaceTypeId is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetSpaceTypeId(spaceTypeId string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.SpaceTypeId)] = spaceTypeId
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetStatus(status string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.Status)] = status
	return u
}

// SetTimeZone is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetTimeZone(timeZone string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.TimeZone)] = timeZone
	return u
}

// SetUpdateTime is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetUpdateTime(updateTime time.Time) SpaceUpdater {
	u.fields[string(SpaceDBSchema.UpdateTime)] = updateTime
	return u
}

// SetUpdater is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) SetUpdater(updater string) SpaceUpdater {
	u.fields[string(SpaceDBSchema.Updater)] = updater
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u SpaceUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set SpaceQuerySet

// ===== BEGIN of Space modifiers

// SpaceDBSchemaField describes database schema field. It requires for method 'Update'
type SpaceDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f SpaceDBSchemaField) String() string {
	return string(f)
}

// SpaceDBSchema stores db field names of Space
var SpaceDBSchema = struct {
	Id          SpaceDBSchemaField
	SpaceTypeId SpaceDBSchemaField
	SpaceId     SpaceDBSchemaField
	SpaceName   SpaceDBSchemaField
	SpaceCode   SpaceDBSchemaField
	Status      SpaceDBSchemaField
	TimeZone    SpaceDBSchemaField
	Language    SpaceDBSchemaField
	IsBcsValid  SpaceDBSchemaField
	Creator     SpaceDBSchemaField
	CreateTime  SpaceDBSchemaField
	Updater     SpaceDBSchemaField
	UpdateTime  SpaceDBSchemaField
}{

	Id:          SpaceDBSchemaField("id"),
	SpaceTypeId: SpaceDBSchemaField("space_type_id"),
	SpaceId:     SpaceDBSchemaField("space_id"),
	SpaceName:   SpaceDBSchemaField("space_name"),
	SpaceCode:   SpaceDBSchemaField("space_code"),
	Status:      SpaceDBSchemaField("status"),
	TimeZone:    SpaceDBSchemaField("time_zone"),
	Language:    SpaceDBSchemaField("language"),
	IsBcsValid:  SpaceDBSchemaField("is_bcs_valid"),
	Creator:     SpaceDBSchemaField("creator"),
	CreateTime:  SpaceDBSchemaField("create_time"),
	Updater:     SpaceDBSchemaField("updater"),
	UpdateTime:  SpaceDBSchemaField("update_time"),
}

// Update updates Space fields by primary key
// nolint: dupl
func (o *Space) Update(db *gorm.DB, fields ...SpaceDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":            o.Id,
		"space_type_id": o.SpaceTypeId,
		"space_id":      o.SpaceId,
		"space_name":    o.SpaceName,
		"space_code":    o.SpaceCode,
		"status":        o.Status,
		"time_zone":     o.TimeZone,
		"language":      o.Language,
		"is_bcs_valid":  o.IsBcsValid,
		"creator":       o.Creator,
		"create_time":   o.CreateTime,
		"updater":       o.Updater,
		"update_time":   o.UpdateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update Space %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// SpaceUpdater is an Space updates manager
type SpaceUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewSpaceUpdater creates new Space updater
// nolint: dupl
func NewSpaceUpdater(db *gorm.DB) SpaceUpdater {
	return SpaceUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&Space{}),
	}
}

// ===== END of Space modifiers

// ===== END of all query sets
