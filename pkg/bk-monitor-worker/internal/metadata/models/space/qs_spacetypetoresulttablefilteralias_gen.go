// Code generated by go-queryset. DO NOT EDIT.
package space

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set SpaceTypeToResultTableFilterAliasQuerySet

// SpaceTypeToResultTableFilterAliasQuerySet is an queryset type for SpaceTypeToResultTableFilterAlias
type SpaceTypeToResultTableFilterAliasQuerySet struct {
	db *gorm.DB
}

// NewSpaceTypeToResultTableFilterAliasQuerySet constructs new SpaceTypeToResultTableFilterAliasQuerySet
func NewSpaceTypeToResultTableFilterAliasQuerySet(db *gorm.DB) SpaceTypeToResultTableFilterAliasQuerySet {
	return SpaceTypeToResultTableFilterAliasQuerySet{
		db: db.Model(&SpaceTypeToResultTableFilterAlias{}),
	}
}

func (qs SpaceTypeToResultTableFilterAliasQuerySet) w(db *gorm.DB) SpaceTypeToResultTableFilterAliasQuerySet {
	return NewSpaceTypeToResultTableFilterAliasQuerySet(db)
}

func (qs SpaceTypeToResultTableFilterAliasQuerySet) Select(fields ...SpaceTypeToResultTableFilterAliasDBSchemaField) SpaceTypeToResultTableFilterAliasQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *SpaceTypeToResultTableFilterAlias) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *SpaceTypeToResultTableFilterAlias) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) All(ret *[]SpaceTypeToResultTableFilterAlias) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeEq(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeGt(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeGte(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeLt(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeLte(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) CreateTimeNe(createTime time.Time) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) Delete() error {
	return qs.db.Delete(SpaceTypeToResultTableFilterAlias{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(SpaceTypeToResultTableFilterAlias{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(SpaceTypeToResultTableFilterAlias{})
	return db.RowsAffected, db.Error
}

// FilterAliasEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasEq(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias = ?", filterAlias))
}

// FilterAliasGt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasGt(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias > ?", filterAlias))
}

// FilterAliasGte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasGte(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias >= ?", filterAlias))
}

// FilterAliasIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasIn(filterAlias ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(filterAlias) == 0 {
		qs.db.AddError(errors.New("must at least pass one filterAlias in FilterAliasIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("filter_alias IN (?)", filterAlias))
}

// FilterAliasLike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasLike(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias LIKE ?", filterAlias))
}

// FilterAliasLt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasLt(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias < ?", filterAlias))
}

// FilterAliasLte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasLte(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias <= ?", filterAlias))
}

// FilterAliasNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasNe(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias != ?", filterAlias))
}

// FilterAliasNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasNotIn(filterAlias ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(filterAlias) == 0 {
		qs.db.AddError(errors.New("must at least pass one filterAlias in FilterAliasNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("filter_alias NOT IN (?)", filterAlias))
}

// FilterAliasNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) FilterAliasNotlike(filterAlias string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("filter_alias NOT LIKE ?", filterAlias))
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) GetUpdater() SpaceTypeToResultTableFilterAliasUpdater {
	return NewSpaceTypeToResultTableFilterAliasUpdater(qs.db)
}

// IdEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdEq(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id = ?", id))
}

// IdGt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdGt(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id > ?", id))
}

// IdGte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdGte(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id >= ?", id))
}

// IdIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdIn(id ...int) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", id))
}

// IdLt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdLt(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id < ?", id))
}

// IdLte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdLte(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id <= ?", id))
}

// IdNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdNe(id int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("id != ?", id))
}

// IdNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) IdNotIn(id ...int) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(id) == 0 {
		qs.db.AddError(errors.New("must at least pass one id in IdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", id))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) Limit(limit int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) Offset(offset int) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs SpaceTypeToResultTableFilterAliasQuerySet) One(ret *SpaceTypeToResultTableFilterAlias) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscByCreateTime() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByFilterAlias is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscByFilterAlias() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("filter_alias ASC"))
}

// OrderAscById is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscById() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscBySpaceType is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscBySpaceType() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("space_type ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscByStatus() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscByTableId is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderAscByTableId() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("table_id ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescByCreateTime() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByFilterAlias is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescByFilterAlias() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("filter_alias DESC"))
}

// OrderDescById is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescById() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescBySpaceType is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescBySpaceType() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("space_type DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescByStatus() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescByTableId is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) OrderDescByTableId() SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Order("table_id DESC"))
}

// SpaceTypeEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeEq(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type = ?", spaceType))
}

// SpaceTypeGt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeGt(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type > ?", spaceType))
}

// SpaceTypeGte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeGte(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type >= ?", spaceType))
}

// SpaceTypeIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeIn(spaceType ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type IN (?)", spaceType))
}

// SpaceTypeLike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeLike(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type LIKE ?", spaceType))
}

// SpaceTypeLt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeLt(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type < ?", spaceType))
}

// SpaceTypeLte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeLte(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type <= ?", spaceType))
}

// SpaceTypeNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeNe(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type != ?", spaceType))
}

// SpaceTypeNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeNotIn(spaceType ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type NOT IN (?)", spaceType))
}

// SpaceTypeNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) SpaceTypeNotlike(spaceType string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("space_type NOT LIKE ?", spaceType))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) StatusEq(status bool) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) StatusIn(status ...bool) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) StatusNe(status bool) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) StatusNotIn(status ...bool) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// TableIdEq is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdEq(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id = ?", tableId))
}

// TableIdGt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdGt(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id > ?", tableId))
}

// TableIdGte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdGte(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id >= ?", tableId))
}

// TableIdIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdIn(tableId ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id IN (?)", tableId))
}

// TableIdLike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdLike(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id LIKE ?", tableId))
}

// TableIdLt is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdLt(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id < ?", tableId))
}

// TableIdLte is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdLte(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id <= ?", tableId))
}

// TableIdNe is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdNe(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id != ?", tableId))
}

// TableIdNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdNotIn(tableId ...string) SpaceTypeToResultTableFilterAliasQuerySet {
	if len(tableId) == 0 {
		qs.db.AddError(errors.New("must at least pass one tableId in TableIdNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("table_id NOT IN (?)", tableId))
}

// TableIdNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceTypeToResultTableFilterAliasQuerySet) TableIdNotlike(tableId string) SpaceTypeToResultTableFilterAliasQuerySet {
	return qs.w(qs.db.Where("table_id NOT LIKE ?", tableId))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetCreateTime(createTime time.Time) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.CreateTime)] = createTime
	return u
}

// SetFilterAlias is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetFilterAlias(filterAlias string) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.FilterAlias)] = filterAlias
	return u
}

// SetId is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetId(id int) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.Id)] = id
	return u
}

// SetSpaceType is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetSpaceType(spaceType string) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.SpaceType)] = spaceType
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetStatus(status bool) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.Status)] = status
	return u
}

// SetTableId is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) SetTableId(tableId string) SpaceTypeToResultTableFilterAliasUpdater {
	u.fields[string(SpaceTypeToResultTableFilterAliasDBSchema.TableId)] = tableId
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u SpaceTypeToResultTableFilterAliasUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set SpaceTypeToResultTableFilterAliasQuerySet

// ===== BEGIN of SpaceTypeToResultTableFilterAlias modifiers

// SpaceTypeToResultTableFilterAliasDBSchemaField describes database schema field. It requires for method 'Update'
type SpaceTypeToResultTableFilterAliasDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f SpaceTypeToResultTableFilterAliasDBSchemaField) String() string {
	return string(f)
}

// SpaceTypeToResultTableFilterAliasDBSchema stores db field names of SpaceTypeToResultTableFilterAlias
var SpaceTypeToResultTableFilterAliasDBSchema = struct {
	Id          SpaceTypeToResultTableFilterAliasDBSchemaField
	SpaceType   SpaceTypeToResultTableFilterAliasDBSchemaField
	TableId     SpaceTypeToResultTableFilterAliasDBSchemaField
	FilterAlias SpaceTypeToResultTableFilterAliasDBSchemaField
	Status      SpaceTypeToResultTableFilterAliasDBSchemaField
	CreateTime  SpaceTypeToResultTableFilterAliasDBSchemaField
}{

	Id:          SpaceTypeToResultTableFilterAliasDBSchemaField("id"),
	SpaceType:   SpaceTypeToResultTableFilterAliasDBSchemaField("space_type"),
	TableId:     SpaceTypeToResultTableFilterAliasDBSchemaField("table_id"),
	FilterAlias: SpaceTypeToResultTableFilterAliasDBSchemaField("filter_alias"),
	Status:      SpaceTypeToResultTableFilterAliasDBSchemaField("status"),
	CreateTime:  SpaceTypeToResultTableFilterAliasDBSchemaField("create_time"),
}

// Update updates SpaceTypeToResultTableFilterAlias fields by primary key
// nolint: dupl
func (o *SpaceTypeToResultTableFilterAlias) Update(db *gorm.DB, fields ...SpaceTypeToResultTableFilterAliasDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":           o.Id,
		"space_type":   o.SpaceType,
		"table_id":     o.TableId,
		"filter_alias": o.FilterAlias,
		"status":       o.Status,
		"create_time":  o.CreateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update SpaceTypeToResultTableFilterAlias %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// SpaceTypeToResultTableFilterAliasUpdater is an SpaceTypeToResultTableFilterAlias updates manager
type SpaceTypeToResultTableFilterAliasUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewSpaceTypeToResultTableFilterAliasUpdater creates new SpaceTypeToResultTableFilterAlias updater
// nolint: dupl
func NewSpaceTypeToResultTableFilterAliasUpdater(db *gorm.DB) SpaceTypeToResultTableFilterAliasUpdater {
	return SpaceTypeToResultTableFilterAliasUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&SpaceTypeToResultTableFilterAlias{}),
	}
}

// ===== END of SpaceTypeToResultTableFilterAlias modifiers

// ===== END of all query sets
