// Code generated by go-queryset. DO NOT EDIT.
package space

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

// ===== BEGIN of all query sets

// ===== BEGIN of query set SpaceVmInfoQuerySet

// SpaceVmInfoQuerySet is an queryset type for SpaceVmInfo
type SpaceVmInfoQuerySet struct {
	db *gorm.DB
}

// NewSpaceVmInfoQuerySet constructs new SpaceVmInfoQuerySet
func NewSpaceVmInfoQuerySet(db *gorm.DB) SpaceVmInfoQuerySet {
	return SpaceVmInfoQuerySet{
		db: db.Model(&SpaceVmInfo{}),
	}
}

func (qs SpaceVmInfoQuerySet) w(db *gorm.DB) SpaceVmInfoQuerySet {
	return NewSpaceVmInfoQuerySet(db)
}

func (qs SpaceVmInfoQuerySet) Select(fields ...SpaceVmInfoDBSchemaField) SpaceVmInfoQuerySet {
	names := []string{}
	for _, f := range fields {
		names = append(names, f.String())
	}

	return qs.w(qs.db.Select(strings.Join(names, ",")))
}

// Create is an autogenerated method
// nolint: dupl
func (o *SpaceVmInfo) Create(db *gorm.DB) error {
	return db.Create(o).Error
}

// Delete is an autogenerated method
// nolint: dupl
func (o *SpaceVmInfo) Delete(db *gorm.DB) error {
	return db.Delete(o).Error
}

// All is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) All(ret *[]SpaceVmInfo) error {
	return qs.db.Find(ret).Error
}

// Count is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) Count() (int, error) {
	var count int
	err := qs.db.Count(&count).Error
	return count, err
}

// CreateTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeEq(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time = ?", createTime))
}

// CreateTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeGt(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time > ?", createTime))
}

// CreateTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeGte(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time >= ?", createTime))
}

// CreateTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeLt(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time < ?", createTime))
}

// CreateTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeLte(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time <= ?", createTime))
}

// CreateTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreateTimeNe(createTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("create_time != ?", createTime))
}

// CreatorEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorEq(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator = ?", creator))
}

// CreatorGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorGt(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator > ?", creator))
}

// CreatorGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorGte(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator >= ?", creator))
}

// CreatorIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorIn(creator ...string) SpaceVmInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator IN (?)", creator))
}

// CreatorLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorLike(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator LIKE ?", creator))
}

// CreatorLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorLt(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator < ?", creator))
}

// CreatorLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorLte(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator <= ?", creator))
}

// CreatorNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorNe(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator != ?", creator))
}

// CreatorNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorNotIn(creator ...string) SpaceVmInfoQuerySet {
	if len(creator) == 0 {
		qs.db.AddError(errors.New("must at least pass one creator in CreatorNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("creator NOT IN (?)", creator))
}

// CreatorNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) CreatorNotlike(creator string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("creator NOT LIKE ?", creator))
}

// Delete is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) Delete() error {
	return qs.db.Delete(SpaceVmInfo{}).Error
}

// DeleteNum is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) DeleteNum() (int64, error) {
	db := qs.db.Delete(SpaceVmInfo{})
	return db.RowsAffected, db.Error
}

// DeleteNumUnscoped is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) DeleteNumUnscoped() (int64, error) {
	db := qs.db.Unscoped().Delete(SpaceVmInfo{})
	return db.RowsAffected, db.Error
}

// GetDB is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) GetDB() *gorm.DB {
	return qs.db
}

// GetUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) GetUpdater() SpaceVmInfoUpdater {
	return NewSpaceVmInfoUpdater(qs.db)
}

// IDEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDEq(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id = ?", ID))
}

// IDGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDGt(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id > ?", ID))
}

// IDGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDGte(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id >= ?", ID))
}

// IDIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDIn(ID ...uint) SpaceVmInfoQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id IN (?)", ID))
}

// IDLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDLt(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id < ?", ID))
}

// IDLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDLte(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id <= ?", ID))
}

// IDNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDNe(ID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("id != ?", ID))
}

// IDNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) IDNotIn(ID ...uint) SpaceVmInfoQuerySet {
	if len(ID) == 0 {
		qs.db.AddError(errors.New("must at least pass one ID in IDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("id NOT IN (?)", ID))
}

// Limit is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) Limit(limit int) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Limit(limit))
}

// Offset is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) Offset(offset int) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Offset(offset))
}

// One is used to retrieve one result. It returns gorm.ErrRecordNotFound
// if nothing was fetched
func (qs SpaceVmInfoQuerySet) One(ret *SpaceVmInfo) error {
	return qs.db.First(ret).Error
}

// OrderAscByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByCreateTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("create_time ASC"))
}

// OrderAscByCreator is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByCreator() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("creator ASC"))
}

// OrderAscByID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("id ASC"))
}

// OrderAscBySpaceID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscBySpaceID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("space_id ASC"))
}

// OrderAscBySpaceType is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscBySpaceType() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("space_type ASC"))
}

// OrderAscByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByStatus() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("status ASC"))
}

// OrderAscByUpdateTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByUpdateTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("update_time ASC"))
}

// OrderAscByUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByUpdater() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("updater ASC"))
}

// OrderAscByVMClusterID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByVMClusterID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("vm_cluster_id ASC"))
}

// OrderAscByVMRetentionTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderAscByVMRetentionTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("vm_retention_time ASC"))
}

// OrderDescByCreateTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByCreateTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("create_time DESC"))
}

// OrderDescByCreator is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByCreator() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("creator DESC"))
}

// OrderDescByID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("id DESC"))
}

// OrderDescBySpaceID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescBySpaceID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("space_id DESC"))
}

// OrderDescBySpaceType is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescBySpaceType() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("space_type DESC"))
}

// OrderDescByStatus is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByStatus() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("status DESC"))
}

// OrderDescByUpdateTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByUpdateTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("update_time DESC"))
}

// OrderDescByUpdater is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByUpdater() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("updater DESC"))
}

// OrderDescByVMClusterID is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByVMClusterID() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("vm_cluster_id DESC"))
}

// OrderDescByVMRetentionTime is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) OrderDescByVMRetentionTime() SpaceVmInfoQuerySet {
	return qs.w(qs.db.Order("vm_retention_time DESC"))
}

// SpaceIDEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDEq(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id = ?", spaceID))
}

// SpaceIDGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDGt(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id > ?", spaceID))
}

// SpaceIDGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDGte(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id >= ?", spaceID))
}

// SpaceIDIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDIn(spaceID ...string) SpaceVmInfoQuerySet {
	if len(spaceID) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceID in SpaceIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id IN (?)", spaceID))
}

// SpaceIDLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDLike(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id LIKE ?", spaceID))
}

// SpaceIDLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDLt(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id < ?", spaceID))
}

// SpaceIDLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDLte(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id <= ?", spaceID))
}

// SpaceIDNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDNe(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id != ?", spaceID))
}

// SpaceIDNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDNotIn(spaceID ...string) SpaceVmInfoQuerySet {
	if len(spaceID) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceID in SpaceIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_id NOT IN (?)", spaceID))
}

// SpaceIDNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceIDNotlike(spaceID string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_id NOT LIKE ?", spaceID))
}

// SpaceTypeEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeEq(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type = ?", spaceType))
}

// SpaceTypeGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeGt(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type > ?", spaceType))
}

// SpaceTypeGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeGte(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type >= ?", spaceType))
}

// SpaceTypeIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeIn(spaceType ...string) SpaceVmInfoQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type IN (?)", spaceType))
}

// SpaceTypeLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeLike(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type LIKE ?", spaceType))
}

// SpaceTypeLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeLt(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type < ?", spaceType))
}

// SpaceTypeLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeLte(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type <= ?", spaceType))
}

// SpaceTypeNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeNe(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type != ?", spaceType))
}

// SpaceTypeNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeNotIn(spaceType ...string) SpaceVmInfoQuerySet {
	if len(spaceType) == 0 {
		qs.db.AddError(errors.New("must at least pass one spaceType in SpaceTypeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("space_type NOT IN (?)", spaceType))
}

// SpaceTypeNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) SpaceTypeNotlike(spaceType string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("space_type NOT LIKE ?", spaceType))
}

// StatusEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusEq(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status = ?", status))
}

// StatusGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusGt(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status > ?", status))
}

// StatusGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusGte(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status >= ?", status))
}

// StatusIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusIn(status ...string) SpaceVmInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status IN (?)", status))
}

// StatusLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusLike(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status LIKE ?", status))
}

// StatusLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusLt(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status < ?", status))
}

// StatusLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusLte(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status <= ?", status))
}

// StatusNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusNe(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status != ?", status))
}

// StatusNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusNotIn(status ...string) SpaceVmInfoQuerySet {
	if len(status) == 0 {
		qs.db.AddError(errors.New("must at least pass one status in StatusNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("status NOT IN (?)", status))
}

// StatusNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) StatusNotlike(status string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("status NOT LIKE ?", status))
}

// UpdateTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeEq(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time = ?", updateTime))
}

// UpdateTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeGt(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time > ?", updateTime))
}

// UpdateTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeGte(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time >= ?", updateTime))
}

// UpdateTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeLt(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time < ?", updateTime))
}

// UpdateTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeLte(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time <= ?", updateTime))
}

// UpdateTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdateTimeNe(updateTime time.Time) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("update_time != ?", updateTime))
}

// UpdaterEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterEq(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater = ?", updater))
}

// UpdaterGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterGt(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater > ?", updater))
}

// UpdaterGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterGte(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater >= ?", updater))
}

// UpdaterIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterIn(updater ...string) SpaceVmInfoQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater IN (?)", updater))
}

// UpdaterLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterLike(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater LIKE ?", updater))
}

// UpdaterLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterLt(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater < ?", updater))
}

// UpdaterLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterLte(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater <= ?", updater))
}

// UpdaterNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterNe(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater != ?", updater))
}

// UpdaterNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterNotIn(updater ...string) SpaceVmInfoQuerySet {
	if len(updater) == 0 {
		qs.db.AddError(errors.New("must at least pass one updater in UpdaterNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("updater NOT IN (?)", updater))
}

// UpdaterNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) UpdaterNotlike(updater string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("updater NOT LIKE ?", updater))
}

// VMClusterIDEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDEq(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id = ?", vMClusterID))
}

// VMClusterIDGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDGt(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id > ?", vMClusterID))
}

// VMClusterIDGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDGte(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id >= ?", vMClusterID))
}

// VMClusterIDIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDIn(vMClusterID ...uint) SpaceVmInfoQuerySet {
	if len(vMClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one vMClusterID in VMClusterIDIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_cluster_id IN (?)", vMClusterID))
}

// VMClusterIDLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDLt(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id < ?", vMClusterID))
}

// VMClusterIDLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDLte(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id <= ?", vMClusterID))
}

// VMClusterIDNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDNe(vMClusterID uint) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_cluster_id != ?", vMClusterID))
}

// VMClusterIDNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMClusterIDNotIn(vMClusterID ...uint) SpaceVmInfoQuerySet {
	if len(vMClusterID) == 0 {
		qs.db.AddError(errors.New("must at least pass one vMClusterID in VMClusterIDNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_cluster_id NOT IN (?)", vMClusterID))
}

// VMRetentionTimeEq is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeEq(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time = ?", vMRetentionTime))
}

// VMRetentionTimeGt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeGt(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time > ?", vMRetentionTime))
}

// VMRetentionTimeGte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeGte(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time >= ?", vMRetentionTime))
}

// VMRetentionTimeIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeIn(vMRetentionTime ...string) SpaceVmInfoQuerySet {
	if len(vMRetentionTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one vMRetentionTime in VMRetentionTimeIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_retention_time IN (?)", vMRetentionTime))
}

// VMRetentionTimeLike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeLike(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time LIKE ?", vMRetentionTime))
}

// VMRetentionTimeLt is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeLt(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time < ?", vMRetentionTime))
}

// VMRetentionTimeLte is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeLte(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time <= ?", vMRetentionTime))
}

// VMRetentionTimeNe is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeNe(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time != ?", vMRetentionTime))
}

// VMRetentionTimeNotIn is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeNotIn(vMRetentionTime ...string) SpaceVmInfoQuerySet {
	if len(vMRetentionTime) == 0 {
		qs.db.AddError(errors.New("must at least pass one vMRetentionTime in VMRetentionTimeNotIn"))
		return qs.w(qs.db)
	}
	return qs.w(qs.db.Where("vm_retention_time NOT IN (?)", vMRetentionTime))
}

// VMRetentionTimeNotlike is an autogenerated method
// nolint: dupl
func (qs SpaceVmInfoQuerySet) VMRetentionTimeNotlike(vMRetentionTime string) SpaceVmInfoQuerySet {
	return qs.w(qs.db.Where("vm_retention_time NOT LIKE ?", vMRetentionTime))
}

// SetCreateTime is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetCreateTime(createTime time.Time) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.CreateTime)] = createTime
	return u
}

// SetCreator is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetCreator(creator string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.Creator)] = creator
	return u
}

// SetID is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetID(ID uint) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.ID)] = ID
	return u
}

// SetSpaceID is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetSpaceID(spaceID string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.SpaceID)] = spaceID
	return u
}

// SetSpaceType is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetSpaceType(spaceType string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.SpaceType)] = spaceType
	return u
}

// SetStatus is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetStatus(status string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.Status)] = status
	return u
}

// SetUpdateTime is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetUpdateTime(updateTime time.Time) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.UpdateTime)] = updateTime
	return u
}

// SetUpdater is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetUpdater(updater string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.Updater)] = updater
	return u
}

// SetVMClusterID is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetVMClusterID(vMClusterID uint) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.VMClusterID)] = vMClusterID
	return u
}

// SetVMRetentionTime is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) SetVMRetentionTime(vMRetentionTime string) SpaceVmInfoUpdater {
	u.fields[string(SpaceVmInfoDBSchema.VMRetentionTime)] = vMRetentionTime
	return u
}

// Update is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) Update() error {
	return u.db.Updates(u.fields).Error
}

// UpdateNum is an autogenerated method
// nolint: dupl
func (u SpaceVmInfoUpdater) UpdateNum() (int64, error) {
	db := u.db.Updates(u.fields)
	return db.RowsAffected, db.Error
}

// ===== END of query set SpaceVmInfoQuerySet

// ===== BEGIN of SpaceVmInfo modifiers

// SpaceVmInfoDBSchemaField describes database schema field. It requires for method 'Update'
type SpaceVmInfoDBSchemaField string

// String method returns string representation of field.
// nolint: dupl
func (f SpaceVmInfoDBSchemaField) String() string {
	return string(f)
}

// SpaceVmInfoDBSchema stores db field names of SpaceVmInfo
var SpaceVmInfoDBSchema = struct {
	ID              SpaceVmInfoDBSchemaField
	SpaceType       SpaceVmInfoDBSchemaField
	SpaceID         SpaceVmInfoDBSchemaField
	VMClusterID     SpaceVmInfoDBSchemaField
	VMRetentionTime SpaceVmInfoDBSchemaField
	Status          SpaceVmInfoDBSchemaField
	Creator         SpaceVmInfoDBSchemaField
	CreateTime      SpaceVmInfoDBSchemaField
	Updater         SpaceVmInfoDBSchemaField
	UpdateTime      SpaceVmInfoDBSchemaField
}{

	ID:              SpaceVmInfoDBSchemaField("id"),
	SpaceType:       SpaceVmInfoDBSchemaField("space_type"),
	SpaceID:         SpaceVmInfoDBSchemaField("space_id"),
	VMClusterID:     SpaceVmInfoDBSchemaField("vm_cluster_id"),
	VMRetentionTime: SpaceVmInfoDBSchemaField("vm_retention_time"),
	Status:          SpaceVmInfoDBSchemaField("status"),
	Creator:         SpaceVmInfoDBSchemaField("creator"),
	CreateTime:      SpaceVmInfoDBSchemaField("create_time"),
	Updater:         SpaceVmInfoDBSchemaField("updater"),
	UpdateTime:      SpaceVmInfoDBSchemaField("update_time"),
}

// Update updates SpaceVmInfo fields by primary key
// nolint: dupl
func (o *SpaceVmInfo) Update(db *gorm.DB, fields ...SpaceVmInfoDBSchemaField) error {
	dbNameToFieldName := map[string]interface{}{
		"id":                o.ID,
		"space_type":        o.SpaceType,
		"space_id":          o.SpaceID,
		"vm_cluster_id":     o.VMClusterID,
		"vm_retention_time": o.VMRetentionTime,
		"status":            o.Status,
		"creator":           o.Creator,
		"create_time":       o.CreateTime,
		"updater":           o.Updater,
		"update_time":       o.UpdateTime,
	}
	u := map[string]interface{}{}
	for _, f := range fields {
		fs := f.String()
		u[fs] = dbNameToFieldName[fs]
	}
	if err := db.Model(o).Updates(u).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}

		return fmt.Errorf("can't update SpaceVmInfo %v fields %v: %s",
			o, fields, err)
	}

	return nil
}

// SpaceVmInfoUpdater is an SpaceVmInfo updates manager
type SpaceVmInfoUpdater struct {
	fields map[string]interface{}
	db     *gorm.DB
}

// NewSpaceVmInfoUpdater creates new SpaceVmInfo updater
// nolint: dupl
func NewSpaceVmInfoUpdater(db *gorm.DB) SpaceVmInfoUpdater {
	return SpaceVmInfoUpdater{
		fields: map[string]interface{}{},
		db:     db.Model(&SpaceVmInfo{}),
	}
}

// ===== END of SpaceVmInfo modifiers

// ===== END of all query sets
