# containerd/cgroups copyIfNeeded 调用链分析

## 完整调用流程

```
1. 用户代码调用
   cgroups.New(cgroups.V1, staticPath, resource)
   
2. New() 函数
   ↓
   遍历所有子系统 (包括cpuset)
   ↓
   调用 initializeSubsystem(s, path, resources)
   
3. initializeSubsystem() 函数
   ↓
   检查子系统是否为 creator 接口
   ↓
   调用 s.Create(path, resources)  // s 是 cpusetController
   
4. cpusetController.Create() 方法
   ↓
   调用 c.ensureParent(c.Path(path), c.root)
   ↓
   调用 os.MkdirAll(c.Path(path), defaultDirPerm)
   ↓
   调用 c.copyIfNeeded(c.Path(path), filepath.Dir(c.Path(path)))  ← 第一次调用
   
5. ensureParent() 方法 (递归确保父目录存在)
   ↓
   如果父目录不存在，递归调用 c.ensureParent(parent, root)
   ↓
   调用 os.MkdirAll(current, defaultDirPerm)
   ↓
   调用 c.copyIfNeeded(current, parent)  ← 可能的第二次调用
   
6. copyIfNeeded() 方法
   ↓
   读取当前目录的 cpuset.cpus 和 cpuset.mems
   ↓
   读取父目录的 cpuset.cpus 和 cpuset.mems
   ↓
   如果当前目录的文件为空，从父目录复制内容
```

## 关键代码片段

### 1. New() 函数中的子系统初始化
```go
// 来自 cgroup.go
func New(path Path, resources *specs.LinuxResources, opts ...InitOpts) (Cgroup, error) {
    // ...
    for _, s := range subsystems {
        // 这里会调用每个子系统的Create方法，包括cpuset
        if err := initializeSubsystem(s, path, resources); err != nil {
            // ...
        }
        active = append(active, s)
    }
    // ...
}
```

### 2. initializeSubsystem() 函数
```go
// 推测的initializeSubsystem实现 (在源码中可能在其他文件)
func initializeSubsystem(s Subsystem, path Path, resources *specs.LinuxResources) error {
    if c, ok := s.(creator); ok {
        p, err := path(s.Name())
        if err != nil {
            return err
        }
        return c.Create(p, resources)  // 调用cpusetController.Create
    }
    return nil
}
```

### 3. cpusetController.Create() 方法
```go
func (c *cpusetController) Create(path string, resources *specs.LinuxResources) error {
    // 1. 确保父目录存在并正确配置
    if err := c.ensureParent(c.Path(path), c.root); err != nil {
        return err
    }
    
    // 2. 创建当前目录
    if err := os.MkdirAll(c.Path(path), defaultDirPerm); err != nil {
        return err
    }
    
    // 3. 从父目录复制cpuset配置 (关键步骤!)
    if err := c.copyIfNeeded(c.Path(path), filepath.Dir(c.Path(path))); err != nil {
        return err
    }
    
    // 4. 如果resources中指定了CPU配置，则覆盖
    if resources.CPU != nil {
        // 只有明确指定了Cpus和Mems才会覆盖继承的值
        if resources.CPU.Cpus != "" {
            // 写入指定的cpuset.cpus
        }
        if resources.CPU.Mems != "" {
            // 写入指定的cpuset.mems
        }
    }
    return nil
}
```

### 4. copyIfNeeded() 方法的核心逻辑
```go
func (c *cpusetController) copyIfNeeded(current, parent string) error {
    // 读取当前目录的cpuset配置
    currentCpus, currentMems, err := c.getValues(current)
    if err != nil {
        return err
    }
    
    // 读取父目录的cpuset配置
    parentCpus, parentMems, err := c.getValues(parent)
    if err != nil {
        return err
    }
    
    // 如果当前cpuset.cpus为空，从父目录复制
    if isEmpty(currentCpus) {
        if err := os.WriteFile(
            filepath.Join(current, "cpuset.cpus"),
            parentCpus,  // 继承父目录的CPU配置
            defaultFilePerm,
        ); err != nil {
            return err
        }
    }
    
    // 如果当前cpuset.mems为空，从父目录复制
    if isEmpty(currentMems) {
        if err := os.WriteFile(
            filepath.Join(current, "cpuset.mems"),
            parentMems,  // 继承父目录的内存节点配置
            defaultFilePerm,
        ); err != nil {
            return err
        }
    }
    return nil
}
```

## 继承链示例

假设创建路径为 `/sys/fs/cgroup/cpuset/collector-test`：

```
1. 根cgroup: /sys/fs/cgroup/cpuset/
   cpuset.cpus = "0-7"  (所有在线CPU，包括隔离CPU)
   cpuset.mems = "0"
   
2. 创建: /sys/fs/cgroup/cpuset/collector-test/
   初始: cpuset.cpus = ""  (空)
   初始: cpuset.mems = ""  (空)
   
3. copyIfNeeded() 执行:
   检测到cpuset.cpus为空
   从父目录(/sys/fs/cgroup/cpuset/)复制
   结果: cpuset.cpus = "0-7"  (继承了所有CPU!)
   结果: cpuset.mems = "0"
```

## 关键结论

1. **copyIfNeeded在cgroup创建时自动调用**
2. **它确保新创建的cpuset cgroup有有效的CPU和内存配置**
3. **如果没有显式指定，就从父cgroup继承**
4. **这是Linux cpuset子系统的要求，不能有空的cpuset.cpus**
5. **这就是为什么即使只设置CPU配额，也会继承所有CPU的原因**
