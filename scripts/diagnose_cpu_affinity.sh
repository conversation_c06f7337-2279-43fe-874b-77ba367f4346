#!/bin/bash

# CPU亲和性诊断脚本
# 用于排查bkmonitor-beat进程是否运行在隔离CPU核心上

set -e

echo "=== bkmonitor-beat CPU亲和性诊断脚本 ==="
echo "时间: $(date)"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 解析CPU列表字符串，如 "0-3,6,8-10" 转换为数组
parse_cpu_list() {
    local cpu_str="$1"
    local -a cpus=()

    if [ -z "$cpu_str" ]; then
        echo ""
        return
    fi

    # 分割逗号分隔的部分
    IFS=',' read -ra parts <<< "$cpu_str"
    for part in "${parts[@]}"; do
        part=$(echo "$part" | tr -d ' ')  # 去除空格
        if [[ "$part" == *-* ]]; then
            # 处理范围，如 "0-3"
            IFS='-' read -ra range <<< "$part"
            if [ ${#range[@]} -eq 2 ]; then
                start=${range[0]}
                end=${range[1]}
                for ((i=start; i<=end; i++)); do
                    cpus+=($i)
                done
            fi
        else
            # 处理单个CPU
            if [[ "$part" =~ ^[0-9]+$ ]]; then
                cpus+=($part)
            fi
        fi
    done

    # 输出数组，用空格分隔
    echo "${cpus[@]}"
}

# 检查CPU是否在列表中
cpu_in_list() {
    local target_cpu="$1"
    local cpu_list="$2"
    local -a cpus=($(parse_cpu_list "$cpu_list"))

    for cpu in "${cpus[@]}"; do
        if [ "$cpu" -eq "$target_cpu" ]; then
            return 0  # 找到了
        fi
    done
    return 1  # 没找到
}

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 1. 检查系统CPU信息
print_info "1. 检查系统CPU信息"
echo "CPU核心数: $(nproc)"
echo "在线CPU: $(cat /sys/devices/system/cpu/online 2>/dev/null || echo '未知')"

if [ -f /sys/devices/system/cpu/isolated ]; then
    isolated_cpus=$(cat /sys/devices/system/cpu/isolated)
    if [ -n "$isolated_cpus" ]; then
        print_warn "隔离CPU: $isolated_cpus"
    else
        print_success "没有隔离CPU"
    fi
else
    print_success "没有隔离CPU配置文件"
fi

# 检查内核启动参数
print_info "内核启动参数中的isolcpus设置:"
if grep -q "isolcpus" /proc/cmdline; then
    grep -o "isolcpus=[^ ]*" /proc/cmdline || echo "未找到isolcpus参数"
else
    print_success "内核启动参数中没有isolcpus设置"
fi

echo

# 2. 查找bkmonitor-beat进程
print_info "2. 查找bkmonitor-beat进程"
pids=$(pgrep -f "bkmonitor.*beat" || true)

if [ -z "$pids" ]; then
    print_error "未找到bkmonitor-beat进程"
    exit 1
fi

echo "找到以下bkmonitor-beat进程:"
for pid in $pids; do
    cmd=$(ps -p $pid -o cmd --no-headers 2>/dev/null || echo "未知")
    echo "  PID: $pid, 命令: $cmd"
done

echo

# 3. 检查每个进程的CPU亲和性
print_info "3. 检查进程CPU亲和性"

check_cpu_affinity() {
    local pid=$1
    local cmd=$(ps -p $pid -o cmd --no-headers 2>/dev/null || echo "未知")
    
    echo "进程 $pid ($cmd):"
    
    # 检查CPU亲和性
    if command -v taskset >/dev/null 2>&1; then
        affinity=$(taskset -p $pid 2>/dev/null | grep -o "0x[0-9a-f]*" || echo "")
        if [ -n "$affinity" ]; then
            echo "  CPU亲和性掩码: $affinity"
            
            # 转换为CPU列表
            python -c "
mask = int('$affinity', 16)
cpus = []
for i in range(64):  # 假设最多64个CPU
    if mask & (1 << i):
        cpus.append(i)
print('  允许的CPU核心:', cpus if cpus else '无')
" 2>/dev/null || echo "  无法解析CPU列表"
        else
            print_warn "  无法获取CPU亲和性"
        fi
    else
        print_warn "  taskset命令不可用"
    fi
    
    # 检查cpuset cgroup
    if [ -f "/proc/$pid/cgroup" ]; then
        cpuset_path=$(grep ":cpuset:" /proc/$pid/cgroup 2>/dev/null | cut -d: -f3 || echo "")
        if [ -n "$cpuset_path" ]; then
            echo "  cpuset cgroup路径: $cpuset_path"
            
            # 检查cpuset.cpus
            cpuset_file="/sys/fs/cgroup/cpuset${cpuset_path}/cpuset.cpus"
            if [ -f "$cpuset_file" ]; then
                cpuset_cpus=$(cat "$cpuset_file" 2>/dev/null || echo "")
                echo "  cpuset允许的CPU: $cpuset_cpus"
            fi
        else
            echo "  未使用cpuset cgroup"
        fi
    fi
    
    # 检查进程状态中的CPU信息
    if [ -f "/proc/$pid/status" ]; then
        cpus_allowed=$(grep "Cpus_allowed_list" /proc/$pid/status 2>/dev/null | cut -f2 || echo "")
        if [ -n "$cpus_allowed" ]; then
            echo "  进程状态中允许的CPU: $cpus_allowed"
        fi
    fi
    
    echo
}

for pid in $pids; do
    check_cpu_affinity $pid
done

# 4. 检查是否有进程运行在隔离CPU上
print_info "4. 检查是否有进程运行在隔离CPU上"

if [ -f /sys/devices/system/cpu/isolated ]; then
    isolated_cpus=$(cat /sys/devices/system/cpu/isolated)
    if [ -n "$isolated_cpus" ]; then
        echo "隔离CPU: $isolated_cpus"
        
        for pid in $pids; do
            # 获取进程当前运行的CPU
            current_cpu=$(ps -o psr -p $pid --no-headers 2>/dev/null | tr -d ' ' || echo "")
            if [ -n "$current_cpu" ]; then
                echo "进程 $pid 当前运行在CPU $current_cpu 上"
                
                # 检查是否在隔离CPU上
                python -c "
def parse_cpu_list(cpu_str):
    if not cpu_str.strip():
        return set()
    cpus = set()
    for part in cpu_str.split(','):
        if '-' in part:
            start, end = map(int, part.split('-'))
            cpus.update(range(start, end + 1))
        else:
            cpus.add(int(part))
    return cpus

isolated = parse_cpu_list('$isolated_cpus')
current = int('$current_cpu')

if current in isolated:
    print('  ⚠️  进程运行在隔离CPU上!')
else:
    print('  ✅ 进程未运行在隔离CPU上')
" 2>/dev/null || echo "  无法检查CPU状态"
            else
                print_warn "  无法获取进程当前运行的CPU"
            fi
        done
    else
        print_success "没有隔离CPU"
    fi
else
    print_success "没有隔离CPU配置"
fi

echo

# 5. 提供修复建议
print_info "5. 修复建议"

echo "如果发现进程运行在隔离CPU上，可以尝试以下解决方案:"
echo
echo "方案1: 重启进程时使用taskset限制CPU亲和性"
echo "  示例: taskset -c 0-3 ./bkmonitor-beat ..."
echo
echo "方案2: 使用cgroup cpuset限制"
echo "  示例: echo '0-3' > /sys/fs/cgroup/cpuset/bkmonitor/cpuset.cpus"
echo
echo "方案3: 修改启动脚本，自动避免隔离CPU"
echo "  参考本项目中修改后的 pkg/bkmonitorbeat/script/linux/start.sh"
echo
echo "方案4: 在代码中添加CPU亲和性设置"
echo "  参考本项目中新增的 pkg/bkmonitorbeat/utils/cpu_affinity_linux.go"

echo
print_info "诊断完成"
