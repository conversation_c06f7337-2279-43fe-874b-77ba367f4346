#!/bin/bash

# 验证bkmonitor-beat的cgroup创建行为

echo "=== bkmonitor-beat cgroup创建行为验证 ==="
echo "时间: $(date)"
echo

# 1. 检查bkmonitor-beat进程
echo "=== 1. 查找bkmonitor-beat进程 ==="
pids=$(pgrep -f "bkmonitor.*beat" || echo "")
if [ -z "$pids" ]; then
    echo "未找到bkmonitor-beat进程"
    echo "请先启动bkmonitor-beat，然后重新运行此脚本"
    exit 1
fi

echo "找到bkmonitor-beat进程: $pids"

for pid in $pids; do
    echo
    echo "进程 $pid 信息:"
    echo "  命令: $(ps -p $pid -o cmd --no-headers 2>/dev/null || echo '未知')"
    echo "  父进程: $(ps -p $pid -o ppid --no-headers 2>/dev/null | tr -d ' ')"
    echo "  启动时间: $(ps -p $pid -o lstart --no-headers 2>/dev/null || echo '未知')"
done

echo

# 2. 检查进程的cgroup归属
echo "=== 2. 进程cgroup归属 ==="
for pid in $pids; do
    echo "进程 $pid 的cgroup归属:"
    if [ -f "/proc/$pid/cgroup" ]; then
        cat "/proc/$pid/cgroup" | sed 's/^/  /'
    else
        echo "  无法读取cgroup信息"
    fi
    echo
done

# 3. 查找bkmonitor相关的cgroup目录
echo "=== 3. 查找bkmonitor创建的cgroup目录 ==="

echo "搜索所有可能的cgroup目录..."

# 搜索模式
search_patterns=("*bkmonitor*" "*collector*" "*beat*")

found_cgroups=()

for pattern in "${search_patterns[@]}"; do
    echo
    echo "搜索模式: $pattern"
    
    # 在各个子系统中搜索
    for subsys in cpu memory cpuset devices freezer net_cls blkio perf_event net_prio hugetlb pids rdma; do
        if [ -d "/sys/fs/cgroup/$subsys" ]; then
            found=$(find "/sys/fs/cgroup/$subsys" -name "$pattern" 2>/dev/null)
            if [ -n "$found" ]; then
                echo "  在 $subsys 子系统中找到:"
                echo "$found" | sed 's/^/    /'
                found_cgroups+=($found)
            fi
        fi
    done
done

echo

# 4. 详细检查找到的cgroup
echo "=== 4. 详细检查找到的cgroup ==="

if [ ${#found_cgroups[@]} -eq 0 ]; then
    echo "未找到bkmonitor相关的cgroup目录"
    echo "这可能意味着:"
    echo "1. bkmonitor-beat没有创建自定义cgroup"
    echo "2. 进程运行在根cgroup中"
    echo "3. 搜索模式没有覆盖到实际的cgroup名称"
else
    for cgroup_dir in "${found_cgroups[@]}"; do
        echo
        echo "检查cgroup: $cgroup_dir"
        
        # 确定子系统类型
        subsys=$(echo "$cgroup_dir" | sed 's|/sys/fs/cgroup/||' | cut -d'/' -f1)
        echo "  子系统: $subsys"
        
        # 检查进程列表
        if [ -f "$cgroup_dir/cgroup.procs" ]; then
            procs=$(cat "$cgroup_dir/cgroup.procs" 2>/dev/null | tr '\n' ' ')
            echo "  包含的进程: $procs"
        fi
        
        # 根据子系统类型检查特定配置
        case $subsys in
            "cpu")
                if [ -f "$cgroup_dir/cpu.cfs_quota_us" ]; then
                    quota=$(cat "$cgroup_dir/cpu.cfs_quota_us")
                    echo "  CPU配额: $quota"
                fi
                if [ -f "$cgroup_dir/cpu.cfs_period_us" ]; then
                    period=$(cat "$cgroup_dir/cpu.cfs_period_us")
                    echo "  CPU周期: $period"
                fi
                ;;
            "memory")
                if [ -f "$cgroup_dir/memory.limit_in_bytes" ]; then
                    limit=$(cat "$cgroup_dir/memory.limit_in_bytes")
                    if [ "$limit" != "9223372036854775807" ]; then
                        echo "  内存限制: $limit 字节"
                    else
                        echo "  内存限制: 无限制"
                    fi
                fi
                ;;
            "cpuset")
                echo "  *** 重要: 发现cpuset cgroup ***"
                if [ -f "$cgroup_dir/cpuset.cpus" ]; then
                    cpus=$(cat "$cgroup_dir/cpuset.cpus")
                    echo "  cpuset.cpus: $cpus"
                fi
                if [ -f "$cgroup_dir/cpuset.mems" ]; then
                    mems=$(cat "$cgroup_dir/cpuset.mems")
                    echo "  cpuset.mems: $mems"
                fi
                ;;
        esac
    done
fi

echo

# 5. 检查containerd/cgroups的默认行为
echo "=== 5. containerd/cgroups包的默认行为分析 ==="

echo "根据containerd/cgroups包的文档和源码:"
echo "1. cgroups.New() 会在所有可用的子系统中创建cgroup"
echo "2. 包括: cpu, memory, cpuset, devices, freezer, net_cls, blkio等"
echo "3. 即使你只指定了CPU和内存资源，也会创建cpuset cgroup"
echo "4. cpuset cgroup会继承父cgroup的配置（通常是根cgroup的配置）"

echo

# 6. 验证父进程信息
echo "=== 6. 验证父进程信息 ==="

for pid in $pids; do
    ppid=$(ps -p $pid -o ppid --no-headers 2>/dev/null | tr -d ' ')
    echo "进程 $pid 的父进程: $ppid"
    
    if [ "$ppid" = "1" ]; then
        echo "  ✓ 父进程是init(PID 1)"
    else
        echo "  父进程不是init，而是:"
        ps -p $ppid -o pid,ppid,cmd --no-headers 2>/dev/null || echo "  无法获取父进程信息"
    fi
    echo
done

# 7. 总结
echo "=== 7. 总结 ==="

echo "关于bkmonitor-beat的cgroup创建行为:"
echo
echo "1. **是否创建cpuset cgroup?**"
if [ ${#found_cgroups[@]} -gt 0 ]; then
    cpuset_found=false
    for cgroup_dir in "${found_cgroups[@]}"; do
        if [[ "$cgroup_dir" == *"/cpuset/"* ]]; then
            cpuset_found=true
            break
        fi
    done
    
    if $cpuset_found; then
        echo "   ✓ 是的，bkmonitor-beat创建了cpuset cgroup"
    else
        echo "   ✗ 没有发现cpuset cgroup"
    fi
else
    echo "   ? 未找到相关cgroup，可能使用根cgroup"
fi

echo
echo "2. **containerd/cgroups包的默认行为:**"
echo "   ✓ 是的，containerd/cgroups包默认会在所有子系统中创建cgroup"
echo "   ✓ 包括cpuset子系统，即使你没有显式配置cpuset资源"
echo "   ✓ 新创建的cpuset cgroup会继承父cgroup的cpuset配置"

echo
echo "3. **父进程关系:**"
echo "   根据上面的检查结果确定"

echo
echo "4. **关键结论:**"
echo "   如果bkmonitor-beat创建了自己的cpuset cgroup，"
echo "   那么它的cpuset配置会继承根cgroup的设置，"
echo "   这通常包含所有在线CPU（包括隔离CPU）。"

echo
echo "验证完成"
